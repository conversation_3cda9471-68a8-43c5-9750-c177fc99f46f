package com.datatech.slgzt.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * DAG产品DTO
 */
@Data
@Accessors(chain = true)
public class DagProductDTO {

    private String id;

    private String configId;

    //产品类型
    private String productType;

    //产品创建状态
    private String status;

    //消息
    private String message;

    //产品创建参数
    private String productParam;

    //产品创建全局参数 可能包含用户信息等
    private String globalParam;
} 
package com.datatech.slgzt.enums;

import org.springframework.util.StringUtils;

/**
 * 云主机操作
 */
public enum VmOperationEnum {

//    VM_CREATE("VM_CREATE", "云主机创建"),
//    VM_SHUTDOWN("VM_SHUTDOWN", "云主机关机"),
//    VM_STARTUP("VM_STARTUP", "云主机开机"),
//    VM_REBOOT("VM_REBOOT", "云主机重启"),
//    VM_RESIZE("VM_RESIZE", "云主机更改规格"),
//    VM_DELETE("VM_DELETE", "云主机删除"),
//    VM_PEND("VM_PEND", "云主机挂起"),
//    VM_RECOVERY("VM_RECOVERY", "云主机恢复"),
//    VM_DIS_DIMENSION("VM_DIS_DIMENSION","云主机退维"),
//
//    VM_RESETPWD("VM_RESETPWD","修改密码"),
//
//    /**
//     * 云主机挂载
//     */
//    VM_MOUNT_PORT("VM_MOUNT_PORT", "云主机挂载虚拟网卡"),
//    VM_UNMOUNT_PORT("VM_UNMOUNT_PORT", "云主机卸载虚拟网卡"),
//    VM_MOUNT_VOLUME("VM_MOUNT_VOLUME", "云主机挂载硬盘"),
//    VM_UNMOUNT_VOLUME("VM_UNMOUNT_VOLUME", "云主机卸载硬盘")

    START("VM_STARTUP", "START", "开机"),
    STOP("VM_SHUTDOWN", "STOP", "关机"),
    PAUSE("PAUSE", "PAUSE", "暂停"),
    UNPAUSE("UNPAUSE", "UNPAUSE", ""),
    SUSPEND("VM_PEND", "SUSPEND", "挂起"),
    RESUME("RESUME", "RESUME", "恢复"),
    LOCK("LOCK", "LOCK", "锁定"),
    UNLOCK("UNLOCK", "UNLOCK", "解锁"),
    SOFTREBOOT("SOFTREBOOT", "SOFTREBOOT", "软启动"),
    REBOOT("VM_REBOOT", "RESTART", "重启"),
    MIGRATE("MIGRATE", "MIGRATE", ""),
    LIVEMIGRATE("LIVEMIGRATE", "LIVEMIGRATE", ""),
    REBUILD("REBUILD", "REBUILD", "重建"),
    RESETPWD("RESETPWD", "RESETPWD", "重置密码"),
    DELETE("DELETE", "DELETE", "回收"),
    BIND_SG("BIND_SG", "BIND_SG", "绑定安全组"),
    UNBIND_SG("UNBIND_SG", "UNBIND_SG", "解绑安全组"),
    OPERATE("OPERATE", "OPERATE", "操作"),
    /**
     * =========安全组和规则操作===============
     */
    SG_CREATE("SG_CREATE", "SG_CREATE", "安全组创建"),
    SG_DELETE("SG_DELETE","SG_DELETE","安全组规则删除"),
    SG_BIND("SG_BIND","SG_BIND","安全组绑定"),
    SG_UNBIND("SG_UNBIND","SG_UNBIND","安全组解绑"),
    SECURITY_GROUP_RULE("SECURITY_GROUP_RULE","SECURITY_GROUP_RULE","安全组规则创建"),
    /**
     * =========虚拟网卡操作=================
     */
    NETCARD_CREATE("NETCARD_CREATE", "NETCARD_CREATE", "虚拟网卡创建"),
    NETCARD_DELETE("NETCARD_DELETE", "NETCARD_DELETE", "虚拟网卡删除"),
    NETCARD_BIND("NETCARD_BIND", "NETCARD_BIND", "虚拟网卡绑定"),
    NETCARD_UNBIND("NETCARD_UNBIND", "NETCARD_UNBIND", "虚拟网卡解绑");


    private final String code;

    private final String alias;

    private final String desc;

    public String getCode() {
        return code;
    }

    public String getAlias() {
        return alias;
    }

    public String getDesc() {
        return desc;
    }

    VmOperationEnum(String code, String alias, String desc) {
        this.code = code;
        this.alias = alias;
        this.desc = desc;
    }

    public static VmOperationEnum getByAlias(String alias) {
        if (!StringUtils.isEmpty(alias)) {
            for (VmOperationEnum value : values()) {
                if (value.getAlias().equals(alias)) {
                    return value;
                }
            }
        }
        return OPERATE;
    }
}

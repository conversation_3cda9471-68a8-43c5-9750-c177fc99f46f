package com.datatech.slgzt.dao.model;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * @program: cloudbasedbusinessproject
 * @description: 商品单资源详情实体类
 * @author: LK
 * @create: 2024-12-25 13:44
 **/
@Data
@TableName("WOC_RESOURCE_DETAIL")
public class ResourceDetailDO {

    /**
     * 商品订单id
     */
    @TableId(value = "ID", type = IdType.ID_WORKER)
    private Long id;

    @TableField(value = "GOODS_ORDER_ID")
    private Long goodsOrderId;

    /**
     * 资源类型
     */
    @TableField("TYPE")
    private String type;

    /**
     * 订单id
     */
    @TableField("ORDER_ID")
    private String orderId;

    /**
     * 设备id
     */
    @TableField("DEVICE_ID")
    private String deviceId;

    /**
     * 设备名称
     */
    @TableField("DEVICE_NAME")
    private String deviceName;

    /**
     * 资源id
     */
    @TableField("RESOURCE_ID")
    private String resourceId;

    /**
     * 系统版本
     */
    @TableField("OS_VERSION")
    private String osVersion;

    /**
     * 规格
     */
    @TableField("SPEC")
    private String spec;

    /**
     * 系统盘
     */
    @TableField("SYS_DISK")
    private String sysDisk;

    /**
     * 数据盘
     */
    @TableField("DATA_DISK")
    private String dataDisk;

    /**
     * 数据盘id
     */
    @TableField("VOLUME_ID")
    private String volumeId;

    /**
     * ip
     */
    @TableField("IP")
    private String ip;

    /**
     * 弹性ip
     */
    @TableField("EIP")
    private String eip;

    /**
     * 弹性ip id
     */
    @TableField("EIP_ID")
    private String eipId;

    /**
     * 带宽
     */
    @TableField("BAND_WIDTH")
    private String bandWidth;

    /**
     * 申请时长
     */
    @TableField("APPLY_TIME")
    private String applyTime;

    /**
     * 租户id
     */
    @TableField("TENANT_ID")
    private Long tenantId;

    /**
     * 租户名称
     */
    @TableField("TENANT_NAME")
    private String tenantName;

    /**
     * 业务系统id
     */
    @TableField("BUSINESS_SYS_ID")
    private Long businessSysId;

    /**
     * 业务系统名称
     */
    @TableField("BUSINESS_SYS_NAME")
    private String businessSysName;

    /**
     * 云平台
     */
    @TableField("CLOUD_PLATFORM")
    private String cloudPlatform;

    /**
     * 资源池id
     */
    @TableField("RESOURCE_POOL_ID")
    private String resourcePoolId;

    /**
     * 资源池名称
     */
    @TableField("RESOURCE_POOL_NAME")
    private String resourcePoolName;

    /**
     * 订单编码
     */
    @TableField("ORDER_CODE")
    private String orderCode;

    /**
     * 资源申请时间
     */
    @TableField("RESOURCE_APPLY_TIME")
    private LocalDateTime resourceApplyTime;

    /**
     * 生效时间
     */
    @TableField("EFFECTIVE_TIME")
    private LocalDateTime effectiveTime;

    /**
     * 过期时间
     */
    @TableField("EXPIRE_TIME")
    private LocalDateTime expireTime;

    /**
     * 设备状态
     */
    @TableField("DEVICE_STATUS")
    private String deviceStatus;

    /**
     * 申请用户id
     */
    @TableField("APPLY_USER_ID")
    private Long applyUserId;

    /**
     * 申请人名称
     */
    @TableField("APPLY_USER_NAME")
    private String applyUserName;

    /**
     * vpc
     */
    @TableField("VPC_ID")
    private String vpcId;

    /**
     * vpc
     */
    @TableField("VPC_NAME")
    private String vpcName;

    /**
     * 子网
     */
    @TableField("SUBNET_ID")
    private String subnetId;

    /**
     * 子网
     */
    @TableField("SUBNET_NAME")
    private String subnetName;

    /**
     * 状态
     */
    @TableField("STATUS")
    @TableLogic(value = "1", delval = "0")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField("CREATE_TIME")
    private LocalDateTime createTime;

    /**
     * 挂载云主机Id
     */
    @TableField("VM_ID")
    private String vmId;

    /**
     * 挂载主机名称(IP)
     */
    @TableField("ECS_NAME")
    private String ecsName;

    /**
     * 是否挂载云主机，true：是，false：否
     */
    @TableField("MOUNT_OR_NOT")
    private String mountOrNot;

    /**
     * 实例uuid
     */
    @TableField("INSTANCE_UUID")
    private String instanceUuid;

    /**
     * 网卡id
     */
    @TableField("NET_ID")
    private String netId;

    /**
     * 网络名称
     */
    @TableField("NET_NAME")
    private String netName;

    /**
     * mac地址
     */
    @TableField("MAC")
    private String mac;

    /**
     * 配置项id
     */
    @TableField("CONFIG_ID")
    private String configId;

    @TableField("RECOVERY_STATUS")
    private Integer recoveryStatus;

    /**
     * 退维状态
     *
     * @see DisDimensionStatusEnum
     */
    @TableField("DIS_DIMENSION_STATUS")
    private String disDimensionStatus;

    /**
     * ====obs字段====
     */
    /**
     * 公钥
     */
    @TableField("ACCESS_KEY")
    private String accessKey;

    /**
     * 私钥
     */
    @TableField("SECRET_KEY")
    private String secretKey;

    /**
     * 公网地址
     */
    @TableField("PUBLIC_ADDRESS")
    private String publicAddress;

    /**
     * 私网地址
     */
    @TableField("INTERNAL_ADDRESS")
    private String internalAddress;
    /**
     * 私网地址
     */
    @TableField("MANAGE_IP")
    private String manageIp;

    /**
     * 计费号
     */
    @TableField("BILL_ID")
    private String billId;

    /**
     * 域名
     */
    @TableField("DOMAIN_NAME")
    private String domainName;


    @TableField("DOMAIN_CODE")
    private String domainCode;

    //-----obs规格拆分字段--------
    /**
     * 存储类型（obs）
     */
    @TableField("STORE_TYPE")
    private String storeType;

    /**
     * 容量（obs）
     */
    @TableField("CAPACITY")
    private String capacity;
    //-----obs规格拆分字段--------

    /**
     * 回收状态描述
     */
    @TableField("RECOVERY_STATUS_CN")
    private String recoveryStatusCn;

    /**
     * 允许重置密码：1允许，0不允许
     */
    @TableField("RESET_PWD")
    private Integer resetPwd;

    /**
     * 退维状态：success-退维成功，running-退维中，fail-退维失败,null-不展示状态
     */
    @TableField("DIS_DIMENSION_STATUS_CN")
    private String disDimensionStatusCn;
    /**
     * 资源池code
     */
    @TableField("RESOURCE_POOL_CODE")
    private String resourcePoolCode;

    /**
     * 回收信息
     */
    @TableField("RECOVERY_INFO")
    private String message;


    /**
     * azCode
     */
    @TableField("AZ_CODE")
    private String azCode;

    /**
     * azName
     */
    @TableField("AZ_NAME")
    private String azName;

    /**
     * azId
     */
    @TableField("AZ_ID")
    private String azId;


    /**
     * 项目名称
     */
    @TableField("PROJECT_NAME")
    private String projectName;


    /**
     * 绑定的网络模型快照
     * todo 目前没有真实的业务需求，只用一个vpcId就可以满足
     */
    @TableField("NETWORK_MODEL_SNAPSHOT")
    private String networkModelSnapshot;


    /**
     * 资源开通来源
     */
    @TableField("SOURCE_TYPE")
    private String sourceType;

    @TableField("MODULE_ID")
    private Long moduleId;

    @TableField("MODULE_NAME")
    private String moduleName;


    @TableField(exist = false)
    private Integer month;

    @TableField("CHANGE_STATUS")
    private String changeStatus;

    @TableField("HANDOVER_STATUS")
    private String handoverStatus;

    /**
     * 关联设备id
     */
    @TableField("RELATED_DEVICE_ID")
    private String relatedDeviceId;
    /**
     * 关联设备名称
     */
    @TableField("RELATED_DEVICE_NAME")
    private String relatedDeviceName;
    /**
     * 关联设备类型
     */
    @TableField("RELATED_DEVICE_TYPE")
    private String relatedDeviceType;
    /**
     * 历史变更记录，变更单id，逗号分隔
     */
    @TableField("HIS_CHANGE_ORDER_IDS")
    private String hisChangeOrderIds;
    /**
     * 历史变更记录，变更单编号，逗号分隔
     */
    @TableField("HIS_CHANGE_ORDER_CODES")
    private String hisChangeOrderCodes;

    /**
     * 安全组id
     */
    @TableField(value = "SECURITY_GROUP_IDS")
    private String securityGroupIds;

    @TableField(exist = false)
    private String securityGroupName;

    /**
     * 虚拟网卡id（多个）
     */
    @TableField(exist = false)
    private String vnicId;

    /**
     * 虚拟网卡名称
     */
    @TableField(exist = false)
    private String vnicName;
}

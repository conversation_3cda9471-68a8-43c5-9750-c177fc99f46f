package com.datatech.slgzt.dao;

import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.VnicMapper;
import com.datatech.slgzt.dao.model.VnicDO;
import com.datatech.slgzt.model.query.VnicQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * 虚拟网卡DAO
 */
@Repository
public class VnicDAO {

    @Resource
    private VnicMapper vnicMapper;

    /**
     * 插入虚拟网卡
     */
    public int insert(VnicDO vnicDO) {
        return vnicMapper.insert(vnicDO);
    }

    /**
     * 更新虚拟网卡
     */
    public int update(VnicDO vnicDO) {
        return vnicMapper.updateById(vnicDO);
    }

    public void updateVmById(String id, String vmId, String vmName) {
        LambdaUpdateWrapper<VnicDO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(VnicDO::getId, id);
        updateWrapper.set(VnicDO::getVmId, vmId);
        updateWrapper.set(VnicDO::getVmName, vmName);
        vnicMapper.update(null, updateWrapper);
    }

    /**
     * 删除虚拟网卡
     */
    public int delete(String id) {
        return vnicMapper.deleteById(id);
    }

    /**
     * 根据ID查询
     */
    public VnicDO getById(String id) {
        return vnicMapper.selectById(id);
    }

    public VnicDO getByVnicId(String vnicId) {
        return vnicMapper.selectOne(Wrappers.<VnicDO>lambdaQuery().eq(VnicDO::getVnicId, vnicId));
    }

    /**
     * 列表查询
     */
    public List<VnicDO> list(VnicQuery query) {
        List<String> vpcIds = null;
        if (StringUtils.isNotBlank(query.getVpcId())) {
            vpcIds = Arrays.asList(query.getVpcId().split(","));
        }
        return vnicMapper.selectList(Wrappers.<VnicDO>lambdaQuery()
                .like(StringUtils.isNotBlank(query.getVnicName()), VnicDO::getVnicName, query.getVnicName())
                .like(StringUtils.isNotBlank(query.getBusinessSystemName()), VnicDO::getBusinessSystemName, query.getBusinessSystemName())
                .eq(StringUtils.isNotBlank(query.getBusinessSystemId()), VnicDO::getBusinessSystemId, query.getBusinessSystemId())
                .like(StringUtils.isNotBlank(query.getCatalogueDomainName()), VnicDO::getCatalogueDomainName, query.getCatalogueDomainName())
                .eq(StringUtils.isNotBlank(query.getCatalogueDomainCode()), VnicDO::getCatalogueDomainCode, query.getCatalogueDomainCode())
                .like(StringUtils.isNotBlank(query.getDomainName()), VnicDO::getDomainName, query.getDomainName())
                .eq(StringUtils.isNotBlank(query.getDomainCode()), VnicDO::getDomainCode, query.getDomainCode())
                .like(StringUtils.isNotBlank(query.getRegionName()), VnicDO::getRegionName, query.getRegionName())
                .eq(StringUtils.isNotBlank(query.getRegionId()), VnicDO::getRegionId, query.getRegionId())
                .like(StringUtils.isNotBlank(query.getAzName()), VnicDO::getAzName, query.getAzName())
                .eq(StringUtils.isNotBlank(query.getAzId()), VnicDO::getAzId, query.getAzId())
                .like(StringUtils.isNotBlank(query.getVpcName()), VnicDO::getVpcName, query.getVpcName())
                .in(StringUtils.isNotBlank(query.getVpcId()), VnicDO::getVpcId, vpcIds)
                .like(StringUtils.isNotBlank(query.getSubnetName()), VnicDO::getSubnetName, query.getSubnetName())
                .eq(StringUtils.isNotBlank(query.getSubnetId()), VnicDO::getSubnetId, query.getSubnetId())
                .like(StringUtils.isNotBlank(query.getIpAddress()), VnicDO::getIpAddress, query.getIpAddress())
                .like(StringUtils.isNotBlank(query.getVmName()), VnicDO::getVmName, query.getVmName())
                .eq(StringUtils.isNotBlank(query.getVmId()), VnicDO::getVmId, query.getVmId())
                .ge(ObjNullUtils.isNotNull(query.getStartTime()), VnicDO::getCreateTime, query.getStartTime())
                .le(ObjNullUtils.isNotNull(query.getEndTime()), VnicDO::getCreateTime, query.getEndTime())
                .in(ObjNullUtils.isNotNull(query.getVmIds()), VnicDO::getVmId, query.getVmIds())
                .orderByDesc(VnicDO::getCreateTime));
    }
} 
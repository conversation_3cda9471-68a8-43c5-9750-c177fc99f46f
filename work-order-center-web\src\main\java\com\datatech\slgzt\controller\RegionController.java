package com.datatech.slgzt.controller;

import com.datatech.slgzt.convert.PlatformWebConvert;
import com.datatech.slgzt.convert.RegionWebConvert;
import com.datatech.slgzt.enums.domain.CatalogueDomain;
import com.datatech.slgzt.manager.PlatformManager;
import com.datatech.slgzt.manager.RegionManager;
import com.datatech.slgzt.manager.VropsRegionManager;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.dto.PlatformDTO;
import com.datatech.slgzt.model.dto.RegionDTO;
import com.datatech.slgzt.model.dto.VropsRegionDTO;
import com.datatech.slgzt.model.req.region.RegionListReq;
import com.datatech.slgzt.model.vo.platform.PlatformVO;
import com.datatech.slgzt.model.vo.region.RegionVO;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.utils.StreamUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 资源控制器
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月10日 15:03:49
 */
@RestController
@RequestMapping("/region")
public class RegionController {

    @Resource
    private RegionManager regionManager;

    @Resource
    private VropsRegionManager vropsRegionManager;

    @Resource
    private RegionWebConvert convert;

    /**
     * 查询资源池列表
     * @return
     */
    @RequestMapping(value = "/list",method = RequestMethod.POST)
    public CommonResult<List<RegionVO>> list(@RequestBody RegionListReq req){
        List<RegionDTO> list = regionManager.list(convert.convert(req));
        return CommonResult.success(StreamUtils.mapArray(list, convert::convert));
    }

    /**
     * 报表资源池
     * 报表的资源池特殊需要增加别的地方查不到的资源池
     */
    @RequestMapping(value = "/reportList", method = RequestMethod.POST)
    public CommonResult<List<RegionVO>> reportList(@RequestBody RegionListReq req){
        List<RegionDTO> list = regionManager.list(convert.convert(req));
        List<RegionVO> regionVOS = StreamUtils.mapArray(list, convert::convert);
        if (ObjNullUtils.isNotNull(req.getDomainCodes())&&req.getDomainCodes().contains(CatalogueDomain.VMWARE.getCode())){
            List<VropsRegionDTO> vropsRegionDTOS = vropsRegionManager.listAll();
            vropsRegionDTOS.forEach(vropsRegionDTO -> {
                RegionVO regionVO = new RegionVO();
                regionVO.setId(vropsRegionDTO.getId());
                regionVO.setName(vropsRegionDTO.getName());
                regionVOS.add(regionVO);
            });
        }
        //如果domainCode是融合边缘云的 需要加上
        return CommonResult.success(regionVOS);
    }
}

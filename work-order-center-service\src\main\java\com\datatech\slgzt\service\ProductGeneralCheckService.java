package com.datatech.slgzt.service;

import com.datatech.slgzt.model.opm.ProductGeneralCheckOpm;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月10日 16:59:23
 * 产品通用检查服务
 */
public interface ProductGeneralCheckService {


    /**
     * 目前只有vmware的产品需要检查产品是否在对应az池中存在
     * 其他产品不需要检查 不要调用
     * @param opm
     */
    void checkProductInAzPool(ProductGeneralCheckOpm opm);

    /**
     * 检查产品是否在对应资源池中存在
     */
    void checkProductInResourcePool(ProductGeneralCheckOpm opm);

    /**
     * 资源池维度检查是否余量足够
     */
    void checkProductInResourcePoolCapacity(ProductGeneralCheckOpm opm);


    /**
     * az维度检查是否余量足够
     */
    void checkProductInAzCapacity(ProductGeneralCheckOpm opm);


    /**
     * 校验规格是否存在
     */
    void checkFlavorExist(ProductGeneralCheckOpm opm);

    /**
     * 校验镜像是否存在
     */
    void checkImageExist(ProductGeneralCheckOpm opm);

    /**
     * 校验slb数量是否足够
     */
    void checkSlbCapacity(ProductGeneralCheckOpm opm);



}

package com.datatech.slgzt.impl.service.xieyun;

import org.springframework.batch.core.BatchStatus;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobExecutionListener;
import org.springframework.batch.core.JobParameters;
import org.springframework.stereotype.Component;

import com.datatech.slgzt.enums.ResOpenEnum;
import com.datatech.slgzt.manager.StandardWorkOrderProductManager;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@Component
public class XieyunEnvironmentCreateJobListener implements JobExecutionListener {

    @Resource
    private StandardWorkOrderProductManager productManager;

    @Override
    public void beforeJob(JobExecution jobExecution) {
        // 可以在这里添加一些初始化操作，比如设置默认参数等
        System.out.println("Job即将执行: " + jobExecution.getJobInstance().getJobName());
    }

    @Override
    public void afterJob(JobExecution jobExecution) {
        if (jobExecution.getStatus() == BatchStatus.FAILED) {
            JobParameters jobParameters = jobExecution.getJobParameters();
            System.out.println("Job执行失败: " + jobParameters.getLong("subOrderId"));
            // 获取失败异常
            List<Throwable> exceptions = jobExecution.getAllFailureExceptions();
            String errorMsg = exceptions.stream()
                .map(Throwable::getMessage)
                .collect(Collectors.joining(", "));
            // 可以发送告警邮件/短信等
            System.err.println("Job执行失败: " + errorMsg);
        } else if (jobExecution.getStatus() == BatchStatus.COMPLETED) {
            Long subOrderId = jobExecution.getJobParameters().getLong("subOrderId");
            System.out.println("Job执行成功: " + subOrderId);
            // productManager.updateStatusById(subOrderId, ResOpenEnum.OPEN_SUCCESS.getCode());
        } else {
            System.out.println("Job" + jobExecution.getJobParameters().getLong("subOrderId") + "执行中, status:"
                    + jobExecution.getStatus());
        }
    }
}
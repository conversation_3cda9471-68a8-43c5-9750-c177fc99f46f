package com.datatech.slgzt.impl.service.dag;

import com.datatech.slgzt.enums.ResOpenEnum;
import com.datatech.slgzt.impl.service.xieyun.JobStepHelper;
import com.datatech.slgzt.manager.DagProductManager;
import com.datatech.slgzt.model.dto.DagProductDTO;
import com.datatech.slgzt.model.query.DagProductQuery;
import com.datatech.slgzt.service.dag.DagProductCreateService;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.StreamUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.support.ReferenceJobFactory;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.repeat.RepeatStatus;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 05月22日 10:23:50
 */
@Configuration
public class DagCreateJobDef implements InitializingBean {

    @Resource
    private JobBuilderFactory jobBuilderFactory;
    @Resource
    private StepBuilderFactory stepBuilderFactory;

    @Autowired
    private List<DagProductCreateService> dagProductCreateService;

    @Resource
    private DagCreateJobListener dagCreateJobListener;

    private final Map<String, DagProductCreateService> serviceMap = Maps.newHashMap();

    @Resource
    private DagProductManager dagProductManager;

    @Bean("dagProductCreateJob")
    public Job environmentCreationJob() {
        Job job = jobBuilderFactory.get("dagProductCreateJob").incrementer(new RunIdIncrementer())
                                   .listener(dagCreateJobListener)
                                   .start(dagProductCreateInit())
                                   //vpc
                                   .next(dagVpcCreate())
                                   //ecs
                                   .next(dagEcsCreate())
                                   //gcs
                                   .next(dagGcsCreate())
                                   //slb
                                   .next(dagSlbCreate())
                                   //nat
                                   .next(dagNatCreate())
                                   //eip
                                   .next(dagEipCreate())
                                   //evs
                                   .next(dagEvsCreate())
                                   //obs
                                   .next(dagObsCreate())
                                   .build();
        return new ReferenceJobFactory(job).createJob();
    }

    //初始化的step用来获取执行id 主动停止 但是只会有一次
    @Bean("dagProductCreateInit")
    public Step dagProductCreateInit() {
        return stepBuilderFactory.get("dagProductCreateInit").tasklet((stepContribution, chunkContext) -> {
            // 从JobParameters获取参数
            JobStepHelper jobStepHelper = new JobStepHelper(chunkContext);
            String stepAlreadyExecuted = jobStepHelper.get("firstInit");
            //判断是否为空
            if (ObjNullUtils.isNull(stepAlreadyExecuted)) {
                //如果为空，说明是第一次执行
                jobStepHelper.put("firstInit", "true");
                //主动停止任务
                jobStepHelper.stop();
                return RepeatStatus.FINISHED;
            }
            return RepeatStatus.FINISHED;
        }).build();
    }

    //用户创建
    @Bean("dagVpcCreate")
    public Step dagVpcCreate() {
        return stepBuilderFactory.get("dagVpcCreate").tasklet((stepContribution, chunkContext) -> {
            // 从JobParameters获取参数
            JobStepHelper jobStepHelper = new JobStepHelper(chunkContext);
            String configId = jobStepHelper.get("configId");
            //通过configId 获取到VPC创建的对象
            List<DagProductDTO> vpcDTOList = dagProductManager.list(new DagProductQuery()
                    .setConfigId(configId)
                    .setProductType("vpc")
                    .setStatusList(Lists.newArrayList(ResOpenEnum.WAIT_OPEN.getCode(), ResOpenEnum.OPEN_FAIL.getCode()))
            );
            //如果vpcDTOList不为空，说明已经创建完VPC了，直接返回
            if (ObjNullUtils.isNull(vpcDTOList)) {
                return RepeatStatus.FINISHED;
            }
            //随机获取一个VPC对象进行开通
            DagProductDTO dagProductDTO = StreamUtils.findAny(vpcDTOList);
            DagProductCreateService createService = serviceMap.get("vpc");
            //创建VPC--- 这里可以改成直接返回PlaneNetworkModel 让后面的程序直接用
            String vpcId = createService.createProduct(dagProductDTO.getProductParam(), dagProductDTO.getGlobalParam());
            jobStepHelper.put("vpcId", vpcId);
            //todo 这里应该还需要建立配置本身和VPC的关联关系放到变量里
            // 主动停止任务
            jobStepHelper.stop();
            return RepeatStatus.FINISHED;
        }).build();
    }

    //创建ecs
    @Bean("dagEcsCreate")
    public Step dagEcsCreate() {
        return stepBuilderFactory.get("dagEcsCreate").tasklet((stepContribution, chunkContext) -> {
            // 从JobParameters获取参数
            JobStepHelper jobStepHelper = new JobStepHelper(chunkContext);
            String configId = jobStepHelper.get("configId");
            //通过configId 获取到VPC创建的对象
            List<DagProductDTO> ecsDTOList = dagProductManager.list(new DagProductQuery()
                    .setConfigId(configId)
                    .setProductType("ecs"));
            //如果ecsDTOList不为空，说明已经创建完ecs了，直接返回
            if (ObjNullUtils.isNull(ecsDTOList)) {
                return RepeatStatus.FINISHED;
            }
            //随机获取一个VPC对象进行开通
            DagProductDTO dagProductDTO = StreamUtils.findAny(ecsDTOList);
            DagProductCreateService createService = serviceMap.get("ecs");
            //创建VPC--- 这里可以改成直接返回PlaneNetworkModel 让后面的程序直接用
            createService.createProduct(dagProductDTO.getProductParam(), dagProductDTO.getGlobalParam());
            //todo 这里应该还需要建立配置本身和VPC的关联关系放到变量里
            jobStepHelper.stop();
            return RepeatStatus.FINISHED;
        }).build();
    }

    //gcs
    @Bean("dagGcsCreate")
    public Step dagGcsCreate() {
        return stepBuilderFactory.get("dagGcsCreate").tasklet((stepContribution, chunkContext) -> {
            // 从JobParameters获取参数
            JobStepHelper jobStepHelper = new JobStepHelper(chunkContext);
            String configId = jobStepHelper.get("configId");
            //通过configId 获取到VPC创建的对象
            List<DagProductDTO> gcsDTOList = dagProductManager.list(new DagProductQuery()
                    .setConfigId(configId)
                    .setProductType("gcs"));
            //如果gcsDTOList不为空，说明已经创建完gcs了，直接返回
            if (ObjNullUtils.isNull(gcsDTOList)) {
                return RepeatStatus.FINISHED;
            }
            //随机获取一个VPC对象进行开通
            DagProductDTO dagProductDTO = StreamUtils.findAny(gcsDTOList);
            DagProductCreateService createService = serviceMap.get("gcs");
            //创建VPC--- 这里可以改成直接返回PlaneNetworkModel 让后面的程序直接用
            createService.createProduct(dagProductDTO.getProductParam(), dagProductDTO.getGlobalParam());
            //todo 这里应该还需要建立配置本身和VPC的关联关系放到变量里
            jobStepHelper.stop();
            return RepeatStatus.FINISHED;
        }).build();
    }

    //slb
    @Bean("dagSlbCreate")
    public Step dagSlbCreate() {
        return stepBuilderFactory.get("dagSlbCreate").tasklet((stepContribution, chunkContext) -> {
            // 从JobParameters获取参数
            JobStepHelper jobStepHelper = new JobStepHelper(chunkContext);
            String configId = jobStepHelper.get("configId");
            //通过configId 获取到VPC创建的对象
            List<DagProductDTO> slbDTOList = dagProductManager.list(new DagProductQuery()
                    .setConfigId(configId)
                    .setProductType("slb"));
            //如果slbDTOList不为空，说明已经创建完slb了，直接返回
            if (ObjNullUtils.isNull(slbDTOList)) {
                return RepeatStatus.FINISHED;
            }
            //随机获取一个VPC对象进行开通
            DagProductDTO dagProductDTO = StreamUtils.findAny(slbDTOList);
            DagProductCreateService createService = serviceMap.get("slb");
            //创建VPC--- 这里可以改成直接返回PlaneNetworkModel 让后面的程序直接用
            createService.createProduct(dagProductDTO.getProductParam(), dagProductDTO.getGlobalParam());
            //todo 这里应该还需要建立配置本身和VPC的关联关系放到变量里
            jobStepHelper.stop();
            return RepeatStatus.FINISHED;
        }).build();
    }

    //nat
    @Bean("dagNatCreate")
    public Step dagNatCreate() {
        return stepBuilderFactory.get("dagNatCreate").tasklet((stepContribution, chunkContext) -> {
            // 从JobParameters获取参数
            JobStepHelper jobStepHelper = new JobStepHelper(chunkContext);
            String configId = jobStepHelper.get("configId");
            //通过configId 获取到VPC创建的对象
            List<DagProductDTO> natDTOList = dagProductManager.list(new DagProductQuery()
                    .setConfigId(configId)
                    .setProductType("nat"));
            //如果natDTOList不为空，说明已经创建完nat了，直接返回
            if (ObjNullUtils.isNull(natDTOList)) {
                return RepeatStatus.FINISHED;
            }
            //随机获取一个VPC对象进行开通
            DagProductDTO dagProductDTO = StreamUtils.findAny(natDTOList);
            DagProductCreateService createService = serviceMap.get("nat");
            //创建VPC--- 这里可以改成直接返回PlaneNetworkModel 让后面的程序直接用
            createService.createProduct(dagProductDTO.getProductParam(), dagProductDTO.getGlobalParam());
            //todo 这里应该还需要建立配置本身和VPC的关联关系放到变量里
            jobStepHelper.stop();
            return RepeatStatus.FINISHED;
        }).build();
    }

    //eip
    @Bean("dagEipCreate")
    public Step dagEipCreate() {
        return stepBuilderFactory.get("dagEipCreate").tasklet((stepContribution, chunkContext) -> {
            // 从JobParameters获取参数
            JobStepHelper jobStepHelper = new JobStepHelper(chunkContext);
            String configId = jobStepHelper.get("configId");
            //通过configId 获取到VPC创建的对象
            List<DagProductDTO> eipDTOList = dagProductManager.list(new DagProductQuery()
                    .setConfigId(configId)
                    .setProductType("eip"));
            //如果eipDTOList不为空，说明已经创建完eip了，直接返回
            if (ObjNullUtils.isNull(eipDTOList)) {
                return RepeatStatus.FINISHED;
            }
            //随机获取一个VPC对象进行开通
            DagProductDTO dagProductDTO = StreamUtils.findAny(eipDTOList);
            DagProductCreateService createService = serviceMap.get("eip");
            //创建VPC--- 这里可以改成直接返回PlaneNetworkModel 让后面的程序直接用
            createService.createProduct(dagProductDTO.getProductParam(), dagProductDTO.getGlobalParam());
            //todo 这里应该还需要建立配置本身和VPC的关联关系放到变量里
            jobStepHelper.stop();
            return RepeatStatus.FINISHED;
        }).build();
    }

    //evs
    @Bean("dagEvsCreate")
    public Step dagEvsCreate() {
        return stepBuilderFactory.get("dagEvsCreate").tasklet((stepContribution, chunkContext) -> {
            // 从JobParameters获取参数
            JobStepHelper jobStepHelper = new JobStepHelper(chunkContext);
            String configId = jobStepHelper.get("configId");
            //通过configId 获取到VPC创建的对象
            List<DagProductDTO> evsDTOList = dagProductManager.list(new DagProductQuery()
                    .setConfigId(configId)
                    .setProductType("evs"));
            //如果evsDTOList不为空，说明已经创建完evs了，直接返回
            if (ObjNullUtils.isNull(evsDTOList)) {
                return RepeatStatus.FINISHED;
            }
            //随机获取一个VPC对象进行开通
            DagProductDTO dagProductDTO = StreamUtils.findAny(evsDTOList);
            DagProductCreateService createService = serviceMap.get("evs");
            //创建VPC--- 这里可以改成直接返回PlaneNetworkModel 让后面的程序直接用
            createService.createProduct(dagProductDTO.getProductParam(), dagProductDTO.getGlobalParam());
            //todo 这里应该还需要建立配置本身和VPC的关联关系放到变量里
            jobStepHelper.stop();
            return RepeatStatus.FINISHED;
        }).build();
    }

    //obs
    @Bean("dagObsCreate")
    public Step dagObsCreate() {
        return stepBuilderFactory.get("dagObsCreate").tasklet((stepContribution, chunkContext) -> {
            // 从JobParameters获取参数
            JobStepHelper jobStepHelper = new JobStepHelper(chunkContext);
            String configId = jobStepHelper.get("configId");
            //通过configId 获取到VPC创建的对象
            List<DagProductDTO> obsDTOList = dagProductManager.list(new DagProductQuery()
                    .setConfigId(configId)
                    .setProductType("obs"));
            //如果obsDTOList不为空，说明已经创建完obs了，直接返回
            if (ObjNullUtils.isNull(obsDTOList)) {
                return RepeatStatus.FINISHED;
            }
            //随机获取一个VPC对象进行开通
            DagProductDTO dagProductDTO = StreamUtils.findAny(obsDTOList);
            DagProductCreateService createService = serviceMap.get("obs");
            //创建VPC--- 这里可以改成直接返回PlaneNetworkModel 让后面的程序直接用
            createService.createProduct(dagProductDTO.getProductParam(), dagProductDTO.getGlobalParam());
            //todo 这里应该还需要建立配置本身和VPC的关联关系放到变量里
            jobStepHelper.stop();
            return RepeatStatus.FINISHED;
        }).build();
    }


    @Override
    public void afterPropertiesSet() {
        for (DagProductCreateService service : dagProductCreateService) {
            serviceMap.put(service.getProductType(), service);
        }
    }
}

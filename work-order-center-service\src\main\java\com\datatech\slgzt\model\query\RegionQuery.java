package com.datatech.slgzt.model.query;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月10日 14:08:08
 */
@Data
@Accessors(chain = true)
public class RegionQuery {

    //平台id
    private Long platformId;

    private String domainCode;

    private List<String> domainCodes;

    private String code;

    private Collection<String> codeList;

}

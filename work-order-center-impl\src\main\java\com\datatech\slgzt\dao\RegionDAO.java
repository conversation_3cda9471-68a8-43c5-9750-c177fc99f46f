package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.RegionMapper;
import com.datatech.slgzt.dao.model.RegionDO;
import com.datatech.slgzt.model.query.RegionQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月10日 14:07:22
 */
@Repository
public class RegionDAO {


    @Resource
    private RegionMapper mapper;


    /**
     * list
     */
    public List<RegionDO> list(RegionQuery query) {
      return  mapper.selectList(Wrappers.<RegionDO>lambdaQuery()
                .eq(ObjNullUtils.isNotNull(query.getDomainCode()), RegionDO::getDomainCode, query.getDomainCode())
                .in(ObjNullUtils.isNotNull(query.getDomainCodes()), RegionDO::getDomainCode, query.getDomainCodes())
                .eq(ObjNullUtils.isNotNull(query.getCode()), RegionDO::getCode, query.getCode())
                .in(ObjNullUtils.isNotNull(query.getCodeList()), RegionDO::getCode, query.getCodeList())
        );
    }

    public RegionDO getByCode(String code) {
        return  mapper.selectOne(Wrappers.<RegionDO>lambdaQuery()
                .eq(ObjNullUtils.isNotNull(code), RegionDO::getCode, code)
        );
    }

    /**
     * get
     */
    public RegionDO getById(Long id) {
        return mapper.selectById(id);
    }
}

package com.datatech.slgzt.dao.container;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.container.XieYunOrgMapper;
import com.datatech.slgzt.dao.model.container.XieYunOrgDO;
import com.datatech.slgzt.model.dto.XieYunOrgDTO;
import com.datatech.slgzt.model.query.container.XieYunOrgQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: liupeihan
 * @Date: 2025/4/14
 */

@Repository
public class XieYunOrgDAO {

    @Resource
    private XieYunOrgMapper mapper;

    public void insert(XieYunOrgDO orgDO) {
        mapper.insert(orgDO);
    }

    public XieYunOrgDO getById(String id) {
        return mapper.selectById(id);
    }

    public void update(XieYunOrgDO orgDO) {
        mapper.updateById(orgDO);
    }

    public List<XieYunOrgDO> list(XieYunOrgQuery query) {
        return mapper.selectList(Wrappers.<XieYunOrgDO>lambdaQuery()
                .eq(ObjNullUtils.isNotNull(query.getName()), XieYunOrgDO::getName, query.getName())
                .eq(ObjNullUtils.isNotNull(query.getXieYunUserId()), XieYunOrgDO::getXieYunUserId, query.getXieYunUserId())
                .eq(ObjNullUtils.isNotNull(query.getCode()), XieYunOrgDO::getCode, query.getCode())
        );
    }

    public void updateUserIdByOrgId(XieYunOrgDO orgDO, String orgId) {
        UpdateWrapper<XieYunOrgDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("XIE_YUN_ORG_ID", orgId);
        mapper.update(orgDO, updateWrapper);
    }

    public void updateOrgQuotaByOrgId(XieYunOrgDO orgDO) {
        UpdateWrapper<XieYunOrgDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("XIE_YUN_ORG_ID", orgDO.getXieYunOrgId());
        mapper.update(orgDO, updateWrapper);
    }
}


package com.datatech.slgzt.utils;

import java.util.UUID;

/**
 * ID生成工具类
 * <AUTHOR>
 * @description ID生成相关工具方法
 * @date 2025年05月27日
 */
public class IdUtils {

    /**
     * 生成UUID（去除横线）
     * @return 32位UUID字符串
     */
    public static String generateId() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 生成UUID（保留横线）
     * @return 36位UUID字符串
     */
    public static String generateUUID() {
        return UUID.randomUUID().toString();
    }

    /**
     * 生成短UUID（16位）
     * @return 16位UUID字符串
     */
    public static String generateShortId() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 16);
    }
}

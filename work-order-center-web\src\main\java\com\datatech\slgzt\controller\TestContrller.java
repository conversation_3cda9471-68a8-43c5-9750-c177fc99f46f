package com.datatech.slgzt.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.enums.RecoveryStatusEnum;
import com.datatech.slgzt.manager.RecoveryWorkOrderProductManager;
import com.datatech.slgzt.manager.ResourceDetailManager;
import com.datatech.slgzt.manager.xieyun.*;
import com.datatech.slgzt.model.dto.ProductDTO;
import com.datatech.slgzt.model.dto.RecoveryWorkOrderProductDTO;
import com.datatech.slgzt.model.vo.callback.TaskVO;
import com.datatech.slgzt.model.xieyun.*;
import com.datatech.slgzt.service.TestService;
import com.datatech.slgzt.service.xieyun.XieyunEnvironmentService;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.utils.StreamUtils;
import org.apache.ibatis.annotations.Param;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月16日 12:20:05
 */
@Slf4j
@RestController
@RequestMapping("/test123")
public class TestContrller {

    @Resource
    private TestService testService;


    @Resource
    private XieyunOrgManager xieyunOrgManager;

    @Resource
    private XieyunProjectManager xieyunProjectManager;

    @Resource
    private XieyunNamespacesManager xieyunNamespacesManager;
    @Resource
    private XieyunUserManager xieyunUserManager;

    @Resource
    private XieyunRepoManager xieyunRepoManager;

    private String registryId = "4940087269a6443b";

    @Resource
    private XieyunEnvironmentService xieyunEnvironmentService;


    @RequestMapping("/testXieYun")
    public void testXieyun() {
        //这里通过工单id查询到一系列需要创建环境的参数
        String orgName = "jjx-test-org-123";
        String orgDesc = "jjx-test-org-123Desc";
        String projectName = "jjx-test-project-123-02";
        String projectDesc = "jjx-test-project-123-02Desc";
        String namespaceName = "jjx-test-namespace-123";
        String namespaceDesc = "jjx-test-namespace-123Desc";
        String repoName = "jjx-test-repo";
        String clusterName = "eic-wz-cluster";
        String nodePoolName = "default";
        String cpuValue = "2";
        String memoryValue = "4";
        String createUserName = "jjx";
        String name = "jjx";
        String mobile = "12345678901";
        String email = "<EMAIL>";
        //------------------------------------开始创建环境------------------------------------------------------
        String orgId = null;
        String projectId = null;
        String repoId = null;

        //-------------------------------创建用户------------------------------------------------
        XieYunUserCreateOpm xieyunUserCreateOpm = new XieYunUserCreateOpm();
        xieyunUserCreateOpm.setUsername(createUserName);
        xieyunUserCreateOpm.setName(name);
        xieyunUserCreateOpm.setMobile(mobile);
        xieyunUserCreateOpm.setEmail(email);
        String userId = xieyunUserManager.ensureUser(xieyunUserCreateOpm);
        Precondition.checkArgument(ObjNullUtils.isNotNull(userId), "创建用户失败");
        //----------------------------------------------------创建组织------------------------------------------------
        XieyunOrgCreateOpm xieyunOrgCreateOpm = new XieyunOrgCreateOpm();
        xieyunOrgCreateOpm.setCode(IdUtil.nanoId());
        xieyunOrgCreateOpm.setName(orgName);
        xieyunOrgCreateOpm.setDescription(orgDesc);
        orgId = xieyunOrgManager.ensureOrg(xieyunOrgCreateOpm);
        //-----给组织分配用户-------
        xieyunUserManager.userJoinOrg(orgId, userId);
        //-----给组织分配资源-------
        XieyunOrgQuotaOpm xieyunOrgQuotaOpm = new XieyunOrgQuotaOpm();
        xieyunOrgQuotaOpm.setCpuValue(cpuValue);
        xieyunOrgQuotaOpm.setMemoryValue(memoryValue);
        xieyunOrgManager.orgQuota(clusterName, nodePoolName, orgId, xieyunOrgQuotaOpm);
        Precondition.checkArgument(ObjNullUtils.isNotNull(orgId), "创建组织失败");
        //-----------------------------------------------创建项目-----------------------------------------------------
        XieyunProjectCreateOpm xieyunProjectCreateOpm = new XieyunProjectCreateOpm();
        xieyunProjectCreateOpm.setProjectName(projectName);
        xieyunProjectCreateOpm.setDescription(projectDesc);
        xieyunProjectCreateOpm.setOrgId(orgId);
        projectId = xieyunProjectManager.ensureProject(orgId, xieyunProjectCreateOpm);
        Precondition.checkArgument(ObjNullUtils.isNotNull(projectId), "创建项目失败");
        //---------给项目分配额度----------
        XieyunProjectQuotaOpm xieyunProjectQuotaOpm = new XieyunProjectQuotaOpm();
        xieyunProjectQuotaOpm.setCpuValue(cpuValue);
        xieyunProjectQuotaOpm.setMemoryValue(memoryValue);
        xieyunProjectManager.projectQuota(orgId, projectId, clusterName, nodePoolName, xieyunProjectQuotaOpm);
        //----------------------------------------------创建命名空间------------------------------------------------------------------
        XieyunNamespaceCreateOpm xieyunNamespaceCreateOpm = new XieyunNamespaceCreateOpm();
        xieyunNamespaceCreateOpm.setName(namespaceName);
        xieyunNamespaceCreateOpm.setDescription(namespaceDesc);
        xieyunNamespaceCreateOpm.setCpu(cpuValue);
        xieyunNamespaceCreateOpm.setMemory(memoryValue);
        xieyunNamespaceCreateOpm.setClusterName(clusterName);
        xieyunNamespaceCreateOpm.setNodePoolName(nodePoolName);
        xieyunNamespacesManager.createNamespace(orgId, projectId, clusterName, xieyunNamespaceCreateOpm);
        //---------------------给命名空间分配网络---------------
        xieyunNamespacesManager.configNamespaceNetwork(null, orgId, projectId, clusterName, CollectionUtil.toList(namespaceName));
        //-------------------------------------------创建镜像仓库---------------------------------------------------
        repoId = xieyunRepoManager.createRepo(registryId, repoName);
        //----------给镜像仓库分配组织---------
        xieyunRepoManager.assignOrg(registryId, orgId, repoId);
        //-------------------------------------------创建环境完成---------------------------------------------------

        //return null;
    }





    @RequestMapping("/test123")
    public void test(@RequestBody JSONObject jsonObject) {
        testService.test(jsonObject.toJSONString());

    }

    @RequestMapping("/test1234")
    public void test1(@RequestBody JSONObject jsonObject) {
        String string = jsonObject.getString("key");
        testService.test2(string);

    }



    //
    AtomicLong number = new AtomicLong(0);








}

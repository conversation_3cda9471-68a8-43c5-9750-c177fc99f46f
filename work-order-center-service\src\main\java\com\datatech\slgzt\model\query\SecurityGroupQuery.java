package com.datatech.slgzt.model.query;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @program: workordercenterproject
 * @description: 安全组查询
 * @author: LK
 * @create: 2025-05-06 09:29
 **/
@Data
public class SecurityGroupQuery {

    private List<Long> ids;

    private Integer pageSize;

    private Integer pageNum;

    private String name;

    private String regionId;

    private String sysBusinessId;

    private String vpcName;

    private LocalDateTime createTimeStart;

    private LocalDateTime createTimeEnd;

    private String description;
}

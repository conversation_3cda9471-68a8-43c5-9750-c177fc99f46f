package com.datatech.slgzt.model.req.export;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 导出任务保存请求
 */
@Data
public class ExportTaskSaveReq {
    
    /**
     * 报表类型
     */
    private String reportType;

    /**
     * 资源池id
     */
    private String regionId;

    /**
     * 统计类型（HOUR/DAY/MONTH）
     */
    private String statType;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 导出字段
     */
    private List<String> exportFields;

} 
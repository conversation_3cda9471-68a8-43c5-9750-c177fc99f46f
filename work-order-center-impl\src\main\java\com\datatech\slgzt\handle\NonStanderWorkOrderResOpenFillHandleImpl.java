package com.datatech.slgzt.handle;

import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.domain.CatalogueDomain;
import com.datatech.slgzt.handle.nostander.NonStanderWorkOrderResOpenFillHandle;
import com.datatech.slgzt.manager.*;
import com.datatech.slgzt.model.dto.FlavorDTO;
import com.datatech.slgzt.model.dto.ImagesDTO;
import com.datatech.slgzt.model.dto.NonStanderWorkOrderProductDTO;
import com.datatech.slgzt.model.nostander.*;
import com.datatech.slgzt.model.opm.NonStanderWorkOrderResOpenFillHandleOpm;
import com.datatech.slgzt.model.query.FlavorQuery;
import com.datatech.slgzt.model.query.ImagesQuery;
import com.datatech.slgzt.model.query.NonStanderWorkOrderProductQuery;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.utils.StreamUtils;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月13日 17:23:54
 */
@Service
public class NonStanderWorkOrderResOpenFillHandleImpl implements NonStanderWorkOrderResOpenFillHandle {

    @Resource
    private AzManager azManager;

    @Resource
    private FlavorModelManager flavorModelManager;

    @Resource
    private ImagesManager imagesManager;


    @Resource
    private RegionManager regionManager;

    @Resource
    private NonStanderWorkOrderProductManager nonStanderWorkOrderProductManager;


    @Override
    public void fillStandardWorkOrderResOpen(NonStanderWorkOrderResOpenFillHandleOpm opm) {
        NonStanderWorkOrderProductDTO dto = opm.getDto();
        ProductTypeEnum typeEnum = ProductTypeEnum.getByCode(dto.getProductType());
        switch (typeEnum){
            case ECS:
                ecsFill(opm);
                break;
            case GCS:
                gcsFill(opm);
                break;
        }
    }

    //---------------------------------------------ecs-----------------------------------------------------------
    private void ecsFill(NonStanderWorkOrderResOpenFillHandleOpm opm){
        NonStanderWorkOrderProductDTO productDTO = opm.getDto();
        String propertySnapshot = productDTO.getPropertySnapshot();
        CloudEcsResourceModel cloudEcsResourceModel = JSON.parseObject(propertySnapshot, CloudEcsResourceModel.class);
        //--------------------获取规格---------------------------------------
        FlavorQuery flavorQuery = new FlavorQuery().setRegionId(cloudEcsResourceModel.getRegionId())
                .setTemplateCode(opm.getTemplateCode()).setName(cloudEcsResourceModel.getFlavorName());
        List<FlavorDTO> flavorDTOS = flavorModelManager.listFlavor(flavorQuery);
        Precondition.checkArgument(flavorDTOS,"规格不存在,请联系管理员");
        Precondition.checkArgument(flavorDTOS.size()==1,"规格不唯一,请联系管理员");
        cloudEcsResourceModel.setTemplateCode(opm.getTemplateCode());
        cloudEcsResourceModel.setFlavorCode(flavorDTOS.get(0).getId());
        cloudEcsResourceModel.setFlavorId(flavorDTOS.get(0).getId());
        cloudEcsResourceModel.setFlavorName(flavorDTOS.get(0).getName());
        //----------------------获取镜像---------------------------
        ImagesDTO imagesDTO = StreamUtils.findAny(imagesManager.list(new ImagesQuery()
                .setRegionId(cloudEcsResourceModel.getRegionId())
                //.setAzId(cloudEcsResourceModel.getAzId())
                .setOsType(cloudEcsResourceModel.getImageOs())
                .setVersion(cloudEcsResourceModel.getImageVersion())));
        Precondition.checkArgument(flavorDTOS.size()==1,"规格不唯一,请联系管理员");
        Precondition.checkArgument(imagesDTO,"镜像不存在,请联系管理员");
        cloudEcsResourceModel.setImageId(imagesDTO.getId());
        //----------------------设置网络---------------------------
        List<PlaneNetworkModel> planeNetworkModelList = opm.getPlaneNetworkModelList();
        planeNetworkModelHandle(planeNetworkModelList);
        //如果domainCode 为 vmware/h3c/huawei 需要走VPC
        cloudEcsResourceModel.setPlaneNetworkModel(opm.getPlaneNetworkModelList());
        //查询到对应的子产品设置AZ和Region
        List<NonStanderWorkOrderProductDTO> productDTOList = nonStanderWorkOrderProductManager.list(new NonStanderWorkOrderProductQuery()
                .setParentId(productDTO.getId()));
        for(NonStanderWorkOrderProductDTO standardWorkOrderProductDTO : productDTOList) {
            String productType = standardWorkOrderProductDTO.getProductType();
            String propertyJson = standardWorkOrderProductDTO.getPropertySnapshot();
            if(productType.equals(ProductTypeEnum.EVS.getCode())){
                MountDataDiskModel mountDataDiskModel = JSON.parseObject(propertyJson, MountDataDiskModel.class);
                mountDataDiskModel.setRegionId(cloudEcsResourceModel.getRegionId());
                mountDataDiskModel.setRegionCode(cloudEcsResourceModel.getRegionCode());
                mountDataDiskModel.setRegionName(cloudEcsResourceModel.getRegionName());
                mountDataDiskModel.setAzId(cloudEcsResourceModel.getAzId());
                mountDataDiskModel.setAzCode(cloudEcsResourceModel.getAzCode());
                mountDataDiskModel.setAzName(cloudEcsResourceModel.getAzName());
                propertyJson=JSON.toJSONString(mountDataDiskModel);
            }
            if (productType.equals(ProductTypeEnum.EIP.getCode())) {
                EipModel eipModel = JSON.parseObject(propertyJson, EipModel.class);
                eipModel.setRegionId(cloudEcsResourceModel.getRegionId());
                eipModel.setRegionCode(cloudEcsResourceModel.getRegionCode());
                eipModel.setRegionName(cloudEcsResourceModel.getRegionName());
                eipModel.setAzId(cloudEcsResourceModel.getAzId());
                eipModel.setAzCode(cloudEcsResourceModel.getAzCode());
                eipModel.setAzName(cloudEcsResourceModel.getAzName());
                propertyJson=JSON.toJSONString(eipModel);
            }
            standardWorkOrderProductDTO.setPropertySnapshot(propertyJson);
            nonStanderWorkOrderProductManager.update(standardWorkOrderProductDTO);
        }
        //存储到数据库
        productDTO.setPropertySnapshot(JSON.toJSONString(cloudEcsResourceModel));
        nonStanderWorkOrderProductManager.update(productDTO);

    }
    //---------------------------------------------gcs-----------------------------------------------------------
    private void gcsFill(NonStanderWorkOrderResOpenFillHandleOpm opm){
        NonStanderWorkOrderProductDTO productDTO = opm.getDto();
        String propertySnapshot = productDTO.getPropertySnapshot();
        CpuEcsResourceModel ecsResourceModel = JSON.parseObject(propertySnapshot, CpuEcsResourceModel.class);
        //获取规格 规格通过
        FlavorQuery flavorQuery = new FlavorQuery().setRegionId(ecsResourceModel.getRegionId())
                .setTemplateCode(opm.getTemplateCode()).setName(ecsResourceModel.getFlavorName());
        //如果是创新池
        List<FlavorDTO> flavorDTOS = flavorModelManager.listFlavor(flavorQuery);
        Precondition.checkArgument(flavorDTOS,"规格不存在,请联系管理员");
        Precondition.checkArgument(flavorDTOS.size()==1,"规格不唯一,请联系管理员");
        ecsResourceModel.setTemplateCode(opm.getTemplateCode());
        ecsResourceModel.setFlavorCode(flavorDTOS.get(0).getId());
        ecsResourceModel.setFlavorName(flavorDTOS.get(0).getName());
        //获取镜像
        ImagesDTO imagesDTO = StreamUtils.findAny(imagesManager.list(new ImagesQuery()
                .setRegionId(ecsResourceModel.getRegionId())
                .setOsType(ecsResourceModel.getImageOs())
                .setVersion(ecsResourceModel.getImageVersion())));
        Precondition.checkArgument(flavorDTOS.size()==1,"规格不唯一,请联系管理员");
        Precondition.checkArgument(imagesDTO,"镜像不存在,请联系管理员");
        ecsResourceModel.setImageId(imagesDTO.getId());
        //----------------------设置网络---------------------------
        List<PlaneNetworkModel> planeNetworkModelList = opm.getPlaneNetworkModelList();
        planeNetworkModelHandle(planeNetworkModelList);
        ecsResourceModel.setPlaneNetworkModel(opm.getPlaneNetworkModelList());
        //查询到对应的子产品设置AZ和Region
        List<NonStanderWorkOrderProductDTO> productDTOList = nonStanderWorkOrderProductManager.list(new NonStanderWorkOrderProductQuery()
                .setParentId(productDTO.getId()));
        for(NonStanderWorkOrderProductDTO standardWorkOrderProductDTO : productDTOList) {
            String productType = standardWorkOrderProductDTO.getProductType();
            String propertyJson = standardWorkOrderProductDTO.getPropertySnapshot();
            if(productType.equals(ProductTypeEnum.EVS.getCode())){
                MountDataDiskModel mountDataDiskModel = JSON.parseObject(propertyJson, MountDataDiskModel.class);
                mountDataDiskModel.setRegionId(ecsResourceModel.getRegionId());
                mountDataDiskModel.setRegionCode(ecsResourceModel.getRegionCode());
                mountDataDiskModel.setRegionName(ecsResourceModel.getRegionName());
                mountDataDiskModel.setAzId(ecsResourceModel.getAzId());
                mountDataDiskModel.setAzCode(ecsResourceModel.getAzCode());
                mountDataDiskModel.setAzName(ecsResourceModel.getAzName());
                propertyJson=JSON.toJSONString(mountDataDiskModel);
            }
            if (productType.equals(ProductTypeEnum.EIP.getCode())) {
                EipModel eipModel = JSON.parseObject(propertyJson, EipModel.class);
                eipModel.setRegionId(ecsResourceModel.getRegionId());
                eipModel.setRegionCode(ecsResourceModel.getRegionCode());
                eipModel.setRegionName(ecsResourceModel.getRegionName());
                eipModel.setAzId(ecsResourceModel.getAzId());
                eipModel.setAzCode(ecsResourceModel.getAzCode());
                eipModel.setAzName(ecsResourceModel.getAzName());
                propertyJson=JSON.toJSONString(eipModel);
            }
            standardWorkOrderProductDTO.setPropertySnapshot(propertyJson);
            nonStanderWorkOrderProductManager.update(standardWorkOrderProductDTO);
        }
        //存储到数据库
        productDTO.setPropertySnapshot(JSON.toJSONString(ecsResourceModel));
        nonStanderWorkOrderProductManager.update(productDTO);
    }


    /**
     * 网络流向处理方法
     * 主要通过domainCode来判断是否需要走VPC
     * 设置好类型
     */
    public void planeNetworkModelHandle(List<PlaneNetworkModel> planeNetworkModelList){
        for(PlaneNetworkModel planeNetworkModel : planeNetworkModelList){
            planeNetworkModel.setType("vpc");
        }
    }
}

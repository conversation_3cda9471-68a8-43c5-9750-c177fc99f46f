package com.datatech.slgzt.controller;

import com.datatech.slgzt.convert.ImagesWebConvert;
import com.datatech.slgzt.enums.domain.CatalogueDomain;
import com.datatech.slgzt.manager.ImagesManager;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.dto.ImagesDTO;
import com.datatech.slgzt.model.query.ImagesQuery;
import com.datatech.slgzt.model.req.images.ImagesListReq;
import com.datatech.slgzt.model.vo.images.ImagesTreeVO;
import com.datatech.slgzt.model.vo.images.ImagesVO;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.StreamUtils;
import com.google.common.collect.ArrayListMultimap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 镜像控制器
 *
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月10日 16:59:23
 */
@RestController
@RequestMapping("/images")
public class ImagesController {

    @Resource
    private ImagesManager imagesManager;


    @Resource
    private ImagesWebConvert convert;

    /**
     * list
     */
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public CommonResult<List<ImagesVO>> list(@RequestBody ImagesListReq req) {
        ImagesQuery query = convert.convert(req);
        if(ObjNullUtils.isNull(query.getShares())){
            query.setShares(1);
        }
        List<ImagesDTO> list = imagesManager.list(query);
        return CommonResult.success(StreamUtils.mapArray(list, convert::convert));
    }


    /**
     * list
     */
    @RequestMapping(value = "/listTree", method = RequestMethod.POST)
    public CommonResult<List<ImagesTreeVO>> listTree(ImagesListReq req) {
        ImagesQuery query = convert.convert(req);
        if(ObjNullUtils.isNull(query.getShares())){
            query.setShares(1);
        }
        if (ObjNullUtils.isNull(query.getDomainCode())) {
            List<CatalogueDomain> allChildren = CatalogueDomain.getAllChildren();
            query.setDomainCodeList(StreamUtils.mapArray(allChildren, CatalogueDomain::getCode));
        }
        List<ImagesDTO> list = imagesManager.list(query);
        ArrayListMultimap<String, ImagesDTO> arrayListMultimap = StreamUtils.toArrayListMultimap(list, ImagesDTO::getOsType);
        List<ImagesTreeVO> pList = new ArrayList<>();
        arrayListMultimap.keySet().forEach(key -> {
            ImagesTreeVO imagesTreeVO = new ImagesTreeVO();
            imagesTreeVO.setName(key);
            Set<String> versionList = StreamUtils.mapSet(arrayListMultimap.get(key), ImagesDTO::getVersion);
            List<ImagesTreeVO> cList = new ArrayList<>();
            versionList.forEach(version -> {
                ImagesTreeVO treeVO = new ImagesTreeVO();
                treeVO.setName(version);
                cList.add(treeVO);
            });
            imagesTreeVO.setChildren(cList);
            pList.add(imagesTreeVO);
        });

        return CommonResult.success(pList);
    }

}

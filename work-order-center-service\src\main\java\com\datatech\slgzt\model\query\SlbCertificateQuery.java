package com.datatech.slgzt.model.query;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * SLB证书查询对象
 */
@Data
public class SlbCertificateQuery {

    private Integer pageNum;

    private Integer pageSize;

    /**
     * 证书名称
     */
    private String certificateName;

    /**
     * 证书类型
     */
    private String certificateType;

    /**
     * 域名
     */
    private String domainCode;


    /**
     * 创建时间开始
     */
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束
     */
    private LocalDateTime createTimeEnd;
} 
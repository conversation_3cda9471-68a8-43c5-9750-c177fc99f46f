package com.datatech.slgzt.impl.manager.xieyun;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.config.XieyunProperties;
import com.datatech.slgzt.convert.XieYunOpmManagerConvert;
import com.datatech.slgzt.enums.DeleteEnum;
import com.datatech.slgzt.helps.XieYunLoginHelper;
import com.datatech.slgzt.manager.xieyun.XieyunUserManager;
import com.datatech.slgzt.manager.xieyun.local.XieyunOrgLocalManager;
import com.datatech.slgzt.manager.xieyun.local.XieyunUserLocalManager;
import com.datatech.slgzt.model.dto.XieYunUserDTO;
import com.datatech.slgzt.model.query.container.XieYunUserQuery;
import com.datatech.slgzt.model.xieyun.XieYunUserCreateOpm;
import com.datatech.slgzt.utils.XieyunUnpackUtil;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;
import com.google.common.collect.Maps;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 04月10日 15:29:35
 */
@Service
@Slf4j
public class XieyunUserManagerImpl implements XieyunUserManager {

    @Resource
    private XieyunProperties xieyunProperties;

    @Resource
    private XieyunUserLocalManager userLocalManager;

    @Resource
    private XieyunOrgLocalManager orgLocalManager;

    @Resource
    private XieYunOpmManagerConvert opmManagerConvert;

    /**
     * 创建用户
     *
     * @param opm
     */
    @Override
    @Transactional
    public String createUser(XieYunUserCreateOpm opm) {
        // 校验用户是否存在，不存在则执行下面调用
        XieYunUserQuery query = new XieYunUserQuery().setName(opm.getName());
        List<XieYunUserDTO> list = userLocalManager.list(query);
        if (CollectionUtil.isNotEmpty(list)) {
            log.info("谐云用户：{} 在数据库中已存在，直接返回用户id：{}}", opm.getName(), list.get(0).getXieYunUserId());
            return list.get(0).getXieYunUserId();
        }
        String uriString = UriComponentsBuilder.fromPath("/caas-amp/users").toUriString();
        String adminToken = XieYunLoginHelper.INSTANCE.getAdminToken();
        Mapper mapper = OkHttps.sync(xieyunProperties.getXieyunUrl() + uriString)
                .addBodyPara(JSON.parseObject(JSON.toJSONString(opm)))
                .bodyType("json")
                .addHeader("Authorization", adminToken)
                .addHeader("Amp-Organ-Id", xieyunProperties.getAmpOrganId())
                .addHeader("Amp-App-Id", xieyunProperties.getAmpAppId())
                .post()
                .getBody()
                .toMapper();
        String data = XieyunUnpackUtil.unpackData(mapper, "调用底层创建用户接口失败");
        JSONObject jsonObject = JSON.parseObject(data);
        String xieyunUserId = jsonObject.getString("id");

        // 本地新增谐云用户
        XieYunUserDTO userDTO = opmManagerConvert.userOpm2dto(opm);
        userDTO.setXieYunUserId(xieyunUserId).setCreatedTime(LocalDateTime.now());
        userLocalManager.insert(userDTO);
        return xieyunUserId;
    }

    /**
     * 删除用户
     *
     * @param userId
     */
    @Override
    @Transactional
    public String deleteUser(String userId) {
        String uriString = UriComponentsBuilder.fromPath("/caas-amp/users/{userId}").buildAndExpand(userId).toUriString();
        Map<String, String> param = Maps.newHashMap();
        param.put("id", userId);
        String adminToken = XieYunLoginHelper.INSTANCE.getAdminToken();
        Mapper mapper = OkHttps.sync(xieyunProperties.getXieyunUrl() + uriString)
                .addUrlPara(param)
                .addHeader("Authorization", adminToken)
                .addHeader("Amp-Organ-Id", xieyunProperties.getAmpOrganId())
                .addHeader("Amp-App-Id", xieyunProperties.getAmpAppId())
                .delete()
                .getBody()
                .toMapper();
        String data = XieyunUnpackUtil.unpackData(mapper, "调用底层删除用户接口失败");
        // TODO 删除用户关联的数据
        userLocalManager.updateDeleteByUserId(userId, DeleteEnum.DELETE.code());
        return data;
    }

    /**
     * 用户加入组织
     */
    @Override
    @Transactional
    public String userJoinOrg(String orgId, String userId) {
        String uriString = UriComponentsBuilder.fromPath("/caas-amp/user-organ-project").toUriString();
        Map<String, String> param = Maps.newHashMap();
        param.put("userId", userId);
        param.put("roleId", xieyunProperties.getOrganManagerRoleId());
        List<Map<String, String>> paramList = Lists.newArrayList();
        paramList.add(param);
        String adminToken = XieYunLoginHelper.INSTANCE.getAdminToken();
        Mapper mapper = OkHttps.sync(xieyunProperties.getXieyunUrl() + uriString)
                .setBodyPara(JSON.toJSONString(paramList))
                .bodyType("json")
                .addHeader("Authorization", adminToken)
                .addHeader("Amp-Organ-Id", orgId)
                .addHeader("Amp-App-Id", xieyunProperties.getAmpAppId())
                .post()
                .getBody()
                .toMapper();
        String data = XieyunUnpackUtil.unpackData(mapper, "调用底层用户加入组织接口失败");
        // 将用户绑定到组织中
        orgLocalManager.updateUserIdByOrgId(orgId, userId);
        return data;
    }
}

package com.datatech.slgzt.task;

import com.datatech.slgzt.manager.ActiveStatisticsManager;
import com.datatech.slgzt.model.dto.ActiveStatisticsDTO;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Slf4j
@Component
public class ActiveStatisticsTask {

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private ActiveStatisticsManager activeStatisticsManager;

    private static final String KEY_PREFIX = "activeStatistics:";
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHH");

    @Scheduled(fixedRate = 300000) // 每5分钟执行一次
    public void updateStatistics() {
        try {
            // 获取当前时间和上一个小时
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime lastHour = now.minusHours(1);

            // 更新两个时间段的数据
            updateHourStatistics(now);
            updateHourStatistics(lastHour);
        } catch (Exception e) {
            log.error("更新活跃统计数据失败", e);
        }
    }

    private void updateHourStatistics(LocalDateTime dateTime) {
        String timeKey = dateTime.format(FORMATTER);
        LocalDate statDate = dateTime.toLocalDate();

        try {
            // 获取各项统计数据
            long userLoginCount = getRedisCount(timeKey, "userLoginCount");
            long userActiveCount = getRedisCount(timeKey, "userActiveCount");
            long apiCallCount = getRedisCount(timeKey, "apiCallCount");
            long clickCount = getRedisCount(timeKey, "clickCount");

            // 先删除该时间段的记录
            activeStatisticsManager.deleteByStatTime(timeKey);

            // 创建新记录
            ActiveStatisticsDTO statistics = new ActiveStatisticsDTO();
            statistics.setStatDate(statDate);
            statistics.setStatTime(timeKey);
            statistics.setUserLoginCount(userLoginCount);
            statistics.setActiveUserCount(userActiveCount);
            statistics.setApiAccessCount(apiCallCount);
            statistics.setClickCount(clickCount);
            statistics.setRemark("自动统计");

            // 保存新记录
            activeStatisticsManager.save(statistics);
        } catch (Exception e) {
            log.error("更新{}时间段的统计数据失败", timeKey, e);
        }
    }

    private long getRedisCount(String timeKey, String countType) {
        try {
            RAtomicLong counter = redissonClient.getAtomicLong(KEY_PREFIX + timeKey + ":" + countType);
            return counter.get();
        } catch (Exception e) {
            log.error("获取Redis计数失败: {} - {}", timeKey, countType, e);
            return 0;
        }
    }
} 
package com.datatech.slgzt.model.vo.resource;

import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import com.datatech.slgzt.model.vo.network.NetworkOrderResult;
import com.datatech.slgzt.model.vo.vpc.VpcOrderResult;
import lombok.Data;

import java.util.List;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/3/26
 */

@Data
public class ResourceTableResVO {

    private List<ResourceDetailDTO> ecsList;
    private List<ResourceDetailDTO> eipList;
    private List<ResourceDetailDTO> gcsList;
    private List<ResourceDetailDTO> mysqlList;
    private List<ResourceDetailDTO> redisList;
    private List<ResourceDetailDTO> evsList;
    private List<ResourceDetailDTO> slbList;
    private List<ResourceDetailDTO> obsList;
    private List<ResourceDetailDTO> natList;
    private List<VpcOrderResult> vpcList;
    private List<NetworkOrderResult> networkList;


}


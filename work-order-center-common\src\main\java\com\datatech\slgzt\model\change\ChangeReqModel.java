package com.datatech.slgzt.model.change;

import lombok.Data;

import java.util.List;

/**
 * 变更请求通用模型
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月31日 14:59:07
 */
@Data
public class ChangeReqModel {

    //资源的Id
    private Long resourceDetailId;

    //资源的类型
    private String resourceType;

    //变更类型
    /**
     * @see com.datatech.slgzt.enums.ChangeTypeEnum
     */
    private List<String> changeType;

    //规格名称
    private String flavorName;

    //flavorId--前端不需要传输
    private String flavorId;

    //规格类型
    private String flavorType;

    //eipId
    private String eipId;

    //eip大小
    private Integer eipBandwidth;

    //挂载盘存储大小
    private List<VolumeChangeReqModel> volumeChangeReqModels;

    //变更时间
    private String changeTime;

    private String templateCode;




    @Data
    public static class VolumeChangeReqModel {
        // id
        private String id;
        //存储容量
        private Integer volumeSize;
    }
}

package com.datatech.slgzt.config;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class StatisticsInterceptor implements HandlerInterceptor {

    @Autowired
    private RedissonClient redissonClient;

    private static final String KEY_PREFIX = "activeStatistics:";
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHH");

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        try {
            String timeKey = LocalDateTime.now().format(FORMATTER);
            String redisKey = KEY_PREFIX + timeKey + ":clickCount";
            
            RAtomicLong counter = redissonClient.getAtomicLong(redisKey);
            counter.incrementAndGet();
            
            // 设置过期时间（24小时）
            counter.expire(24, TimeUnit.HOURS);
        } catch (Exception e) {
            log.error("统计调用量失败", e);
        }
        return true;
    }
} 
package com.datatech.slgzt.impl.service.nostander;

import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.enums.OrderTypeEnum;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.ResOpenEnum;
import com.datatech.slgzt.impl.service.standard.StandardEcsCombinationResOpenStrategyServiceProvider;
import com.datatech.slgzt.manager.NonStanderWorkOrderManager;
import com.datatech.slgzt.manager.NonStanderWorkOrderProductManager;
import com.datatech.slgzt.model.dto.NonStanderWorkOrderDTO;
import com.datatech.slgzt.model.dto.NonStanderWorkOrderProductDTO;
import com.datatech.slgzt.model.layout.ResOpenReqModel;
import com.datatech.slgzt.model.nostander.*;
import com.datatech.slgzt.model.opm.ResOpenOpm;
import com.datatech.slgzt.model.query.NonStanderWorkOrderProductQuery;
import com.datatech.slgzt.service.PlatformService;
import com.datatech.slgzt.service.nostander.NonStanderResOpenService;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.utils.StreamUtils;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月13日 18:50:56
 */
@Slf4j
@Service
public class NonStanderResGcsOpenServiceImpl implements NonStanderResOpenService {

    @Resource
    private NonStanderWorkOrderProductManager productManager;

    @Resource
    private PlatformService platformService;

    @Resource
    private NonStanderWorkOrderManager nonStanderWorkOrderManager;



    @Resource
    private RedissonClient redissonClient;

    @Value("${http.layoutCenterUrl}")
    private String layoutCenter;

    private final String layoutTaskInitUrl = "v1/erm/wokeOrderLayoutTaskInit_subscribe";

    @Override
    public void openStandardResource(NonStanderWorkOrderProductDTO productDTO) {
        //不是只有ESC
        String productType = productDTO.getProductType();
        //开通资源类型要是GPS 或者是ESC
        Precondition.checkArgument(ProductTypeEnum.GCS.getCode().equals(productType)
                || ProductTypeEnum.ECS.getCode().equals(productType), "非标资源不支持该类型开通");
        //把开通的网络资源挂载到对应的附加字段里去
        NonStanderWorkOrderDTO orderDTO = nonStanderWorkOrderManager.getById(productDTO.getWorkOrderId());
        CloudEcsResourceModel ecsMdoel = JSON.parseObject(productDTO.getPropertySnapshot(), CloudEcsResourceModel.class);
        Long tenantId = platformService.getOrCreateTenantId(orderDTO.getBillId(), ecsMdoel.getRegionCode());
        //------------------基础参数设置----------------------------------------------------------
        ResOpenReqModel resOpenReqModel = new ResOpenReqModel();
        //--------------------基础部分设置----------------------------------------
        //设置计费号
        resOpenReqModel.setAccount(orderDTO.getBillId());
        //设置业务code;
        resOpenReqModel.setSourceExtType(OrderTypeEnum.NON_STANDARD.getCode());
        //设置业务code
        resOpenReqModel.setBusinessCode("ECS_ALL_COMBINATION_SUBSCRIBE");
        //设置业务系统code
        resOpenReqModel.setBusinessSystemCode(orderDTO.getBusinessSystemCode());
        //设置客户id
        resOpenReqModel.setCustomId(orderDTO.getCustomerId());
        //设置区域编码
        resOpenReqModel.setRegionCode(ecsMdoel.getRegionCode());
        //设置的是主产品的SubOrderId 这里适配任务中心回调
        resOpenReqModel.setSubOrderId(productDTO.getSubOrderId());
        //设置租户id 可能是需要传入底层租户id 应该要查询下 目前不知道查询的方式
        resOpenReqModel.setTenantId(tenantId);
        //设置userId
        resOpenReqModel.setUserId(orderDTO.getCreatedBy());
        //设置来源固定3这个是给任务中心用的来判断回调的
        resOpenReqModel.setTaskSource(3);
        //开通资源
        List<ResOpenReqModel.ProductOrder> reqProductList= Lists.newArrayList();
        //------------------先拿个esc部分------------------------------------------
        List<ResOpenReqModel.ProductOrder> escProduct = StandardEcsCombinationResOpenStrategyServiceProvider
                .INSTANCE.get(ProductTypeEnum.ECS).assembleParam(new ResOpenOpm()
                .setGId(productDTO.getGid())
                .setTenantId(tenantId)
                .setSubOrderId(productDTO.getSubOrderId().toString())
                .setEscModel(ecsMdoel));
        reqProductList.addAll(escProduct);
        //------------------如果有需要开通挂载硬盘部分------------------------------------------
        if(ecsMdoel.getMountDataDisk()){
            //获取父类id时
            List<NonStanderWorkOrderProductDTO> productDTOS = productManager.list(new NonStanderWorkOrderProductQuery()
                    .setProductType(ProductTypeEnum.EVS.getCode()).setParentId(productDTO.getId()));
            //转成EVS的model
            ArrayList<MountDataDiskModel> evsModleList = Lists.newArrayList();
            for (NonStanderWorkOrderProductDTO dto : productDTOS) {
                MountDataDiskModel mountDataDiskModel = JSON.parseObject(dto.getPropertySnapshot(), MountDataDiskModel.class);
                evsModleList.add(mountDataDiskModel);
            }
            List<ResOpenReqModel.ProductOrder> evsProduct = StandardEcsCombinationResOpenStrategyServiceProvider
                    .INSTANCE.get(ProductTypeEnum.EVS).assembleParam(new ResOpenOpm()
                    .setGId(productDTO.getGid())
                    .setTenantId(tenantId)
                    .setSubOrderId(productDTO.getSubOrderId().toString())
                    .setEvsModelList(evsModleList));
            reqProductList.addAll(evsProduct);
        }
        //------------------如果有需要eip部分------------------------------------------
        if(ecsMdoel.getBindPublicIp()){
            //获取父类id时
            List<NonStanderWorkOrderProductDTO> productDTOS = productManager.list(new NonStanderWorkOrderProductQuery()
                    .setProductType(ProductTypeEnum.EIP.getCode()).setParentId(productDTO.getId()));
            //转成EIP的model
            ArrayList<EipModel> eipModelList = Lists.newArrayList();
            for (NonStanderWorkOrderProductDTO dto : productDTOS) {
                EipModel eipModel = JSON.parseObject(dto.getPropertySnapshot(), EipModel.class);
                eipModelList.add(eipModel);
            }
            List<ResOpenReqModel.ProductOrder> eipProduct = StandardEcsCombinationResOpenStrategyServiceProvider
                    .INSTANCE.get(ProductTypeEnum.EIP).assembleParam(new ResOpenOpm()
                    .setGId(productDTO.getGid())
                    .setTenantId(tenantId)
                    .setSubOrderId(productDTO.getSubOrderId().toString())
                    .setEipModelList(eipModelList));
            reqProductList.addAll(eipProduct);
        }
        //------------------------------多平面网络----------------------------------------------
        //如果存在多平面网络 ----多平面不是产品是属性 数据来源于开通的时候传入的网络模型 也就是esc模型里的网络模型
        if (ecsMdoel.getPlaneNetworkModel().size()>1) {
            //获取父类id时
            //转成NETCARD的model
            ArrayList<NetcardModel> netcardModelList = Lists.newArrayList();
            for (PlaneNetworkModel networkModel : ecsMdoel.getPlaneNetworkModel()) {
                NetcardModel netcardModel = new NetcardModel();
                netcardModel.setId(networkModel.getId());
                netcardModel.setSubnetId(StreamUtils.findAny(networkModel.getSubnets()).getSubnetId());
                netcardModelList.add(netcardModel);
            }
            List<ResOpenReqModel.ProductOrder> eipProduct = StandardEcsCombinationResOpenStrategyServiceProvider
                    .INSTANCE.get(ProductTypeEnum.NETCARD).assembleParam(new ResOpenOpm()
                    .setGId(productDTO.getGid())
                    .setTenantId(tenantId)
                    .setSubOrderId(productDTO.getSubOrderId().toString())
                    .setNetcardModelList(netcardModelList));
            reqProductList.addAll(eipProduct);
        }
        //------------------如果还有其他产品要开通在这里补充------------------------------------------
        resOpenReqModel.setProductOrders(reqProductList);
        //------------------产品参数设置结束-------------------------------------------------------
        //把对应的产品都改成开通中状态
        productManager.updateStatusById(productDTO.getId(), ResOpenEnum.OPENING.getCode());
        productManager.updateStatusByParentId(productDTO.getId(), ResOpenEnum.OPENING.getCode());
        //------------------调用底层开通接口-------------------------------------------------------
        log.info("资源开通，callLayoutOrder--调用编排中心初始化start--goodsId={},request url={}",
                JSON.toJSON(orderDTO.getId()), layoutCenter + layoutTaskInitUrl);
        Mapper dataMapper= OkHttps.sync(layoutCenter + layoutTaskInitUrl)
                .bodyType(OkHttps.JSON)
                .setBodyPara(JSON.toJSONString(resOpenReqModel))
                .post()
                .getBody()
                .toMapper();
        String success = dataMapper.getString("success");
        Precondition.checkArgument("1".equals(success), "资源开通失败，callLayoutOrder--编排中心初始化返回结果失败");
        log.info("资源开通，callLayoutOrder--调用编排中心初始化end--goodsId={},response:{}",
                JSON.toJSON(orderDTO.getId()), JSON.toJSON(dataMapper));
    }

}

package com.datatech.slgzt.impl;

import com.datatech.slgzt.manager.ProductSpecSupportManager;
import com.datatech.slgzt.manager.RegionCapacityMapperManager;
import com.datatech.slgzt.manager.StaticProductStockManager;
import com.datatech.slgzt.config.XieyunProperties;
import com.datatech.slgzt.impl.manager.xieyun.XieyunOrgManagerImpl;
import com.datatech.slgzt.manager.FlavorModelManager;
import com.datatech.slgzt.manager.ImagesManager;
import com.datatech.slgzt.model.ProductCapacityCheckModel;
import com.datatech.slgzt.model.dto.ProductSpecSupportDTO;
import com.datatech.slgzt.model.dto.RegionCapacityDTO;
import com.datatech.slgzt.model.dto.ResourcePoolCapacityDTO;
import com.datatech.slgzt.model.dto.StaticProductStockDTO;
import com.datatech.slgzt.model.dto.xieyun.QuotaResultDTO;
import com.datatech.slgzt.model.dto.FlavorDTO;
import com.datatech.slgzt.model.dto.ImagesDTO;
import com.datatech.slgzt.model.nostander.*;
import com.datatech.slgzt.model.opm.ProductGeneralCheckOpm;
import com.datatech.slgzt.model.query.ProductSpecSupportQuery;
import com.datatech.slgzt.model.query.RegionCapacityQuery;
import com.datatech.slgzt.model.query.StaticProductStockQuery;
import com.datatech.slgzt.model.query.FlavorQuery;
import com.datatech.slgzt.model.query.ImagesQuery;
import com.datatech.slgzt.region.RegionCapacityHelper;
import com.datatech.slgzt.service.ProductGeneralCheckService;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.StreamUtils;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 05月06日 09:57:27
 */
@Service
public class ProductGeneralCheckServiceImpl implements ProductGeneralCheckService {

    @Resource
    private ProductSpecSupportManager productSpecSupportManager;

    @Resource
    private RegionCapacityMapperManager regionCapacityMapperManager;

    @Resource
    private StaticProductStockManager staticProductStockManager;

    @Resource
    private FlavorModelManager flavorModelManager;

    @Resource
    private ImagesManager imagesManager;

    @Resource
    private  XieyunOrgManagerImpl xieyunOrgManager;

    @Resource
    private XieyunProperties xieyunProperties;

    /**
     * 校验slb数量是否足够
     *
     * @param opm
     */
    @Override
    public void checkSlbCapacity(ProductGeneralCheckOpm opm) {
        HashMap<String, Integer> map = Maps.newHashMap();
        // 1. 整理slb产品
        organizeSlbProduct(opm);

    }

    @Override
    public void checkProductInAzPool(ProductGeneralCheckOpm opm) {
        // 1. 检查ecs
        checkEcsProductInResourcePool(opm.getEcsModelList(), opm.getErrorMessages(),true);
        // 2. 检查gcs
        checkEcsProductInResourcePool(opm.getGcsModelList(), opm.getErrorMessages(),true);
        // 3. 检查evs
        checkEvsProductInResourcePool(opm.getEvsModelList(), opm.getErrorMessages(),true);
        // 4. 检查eip
        checkEipProductInResourcePool(opm.getEipModelList(), opm.getErrorMessages(),true);
        // 5. 检查nat
        checkNatProductInResourcePool(opm.getNatModelList(), opm.getErrorMessages(),true);
        // 6. 检查slb
        checkSlbProductInResourcePool(opm.getSlbModelList(), opm.getErrorMessages(),true);
        // 7. 检查obs
        checkObsProductInResourcePool(opm.getObsModelList(), opm.getErrorMessages(),true);
    }

    /**
     * 检查产品是否在对应资源池中存在
     *
     * @param opm
     */
    @Override
    public void checkProductInResourcePool(ProductGeneralCheckOpm opm) {
        // 1. 检查ecs
        checkEcsProductInResourcePool(opm.getEcsModelList(), opm.getErrorMessages(),false);
        // 2. 检查gcs
        checkEcsProductInResourcePool(opm.getGcsModelList(), opm.getErrorMessages(),false);
        // 3. 检查evs
        checkEvsProductInResourcePool(opm.getEvsModelList(), opm.getErrorMessages(),false);
        // 4. 检查eip
        checkEipProductInResourcePool(opm.getEipModelList(), opm.getErrorMessages(),false);
        // 5. 检查nat
        checkNatProductInResourcePool(opm.getNatModelList(), opm.getErrorMessages(),false);
        // 6. 检查slb
        checkSlbProductInResourcePool(opm.getSlbModelList(), opm.getErrorMessages(),false);
        // 7. 检查obs
        checkObsProductInResourcePool(opm.getObsModelList(), opm.getErrorMessages(),false);
        // 8. 检查container
        checkCQProductInResourcePool(opm.getCqModelList(), opm.getErrorMessages(),false);
    }

    /**
     * 资源池维度检查是否余量足够
     * 校验部分包括：
     * 1. cpu 和内存检查是否余量足够
     * 2. eip数量检查是否余量足够 eip大小是否足够
     * 3. slb检查是否余量足够
     * 4. 数据盘大小是否足够
     *
     * @param opm
     */
    @Override
    public void checkProductInResourcePoolCapacity(ProductGeneralCheckOpm opm) {
        //1. 整理ecs产品
        organizeEcsProduct(opm);
        //2. 整理gcs产品
        organizeGcsProduct(opm);
        //3. 整理evs产品
        organizeEvsProduct(opm);
        //4. 整理eip产品
        organizeEipProduct(opm);
        //5. 整理nat产品
        organizeNatProduct(opm);
        //6. 整理slb产品
        organizeSlbProduct(opm);
        //拿出资源池维度的map
        Map<String, ProductCapacityCheckModel> regionCheckModelMap = opm.getRegionCheckModelMap();
        //去映射表找到资源池对应数据
        List<RegionCapacityDTO> regionCapacityDTOList = regionCapacityMapperManager.list(new RegionCapacityQuery()
                .setRegionCodeList(StreamUtils.set2List(regionCheckModelMap.keySet()))
        );
        Map<String, RegionCapacityDTO> capacityDTOMap = StreamUtils.toMap(regionCapacityDTOList, RegionCapacityDTO::getRegionCode);
        //遍历资源池维度的map
        for (Map.Entry<String, ProductCapacityCheckModel> entry : regionCheckModelMap.entrySet()) {
            String regionCode = entry.getKey();
            ProductCapacityCheckModel regionModel = entry.getValue();
            RegionCapacityDTO regionCapacityDTO = capacityDTOMap.get(regionCode);
            //如果regionCapacityDTO为空，说明没有找到对应的资源池，跳过
            if (ObjNullUtils.isNull(regionCapacityDTO)) {
                continue;
            }
            String regionName = regionCapacityDTO.getRegionName();
            //检查cpu和内存是否足够
            List<ResourcePoolCapacityDTO> regionCapacityByRegion = RegionCapacityHelper.getInstance()
                                                                                       .getRegionCapacityByRegion(regionCapacityDTO.getRegionGroupCode(), regionCapacityDTO.getRegionCode());

            ResourcePoolCapacityDTO resourcePoolCapacityDTO = RegionCapacityHelper.getInstance()
                                                                                 .aggregateRegionCapacity(regionCapacityByRegion);
            //检查cpu和内存是否足够
            if (resourcePoolCapacityDTO.getVcpuAviSale().compareTo(BigDecimal.valueOf(regionModel.getCpuNum())) < 0) {
                opm.getErrorMessages()
                   .add("资源池" + regionName + "cpu余量不足 余量剩余" + resourcePoolCapacityDTO.getVcpuAviSale() + "核，需要" + regionModel.getCpuNum() + "核");
            }
            if (resourcePoolCapacityDTO.getMemoryAviSale()
                                       .compareTo(BigDecimal.valueOf(regionModel.getMemorySize()))<0) {
                opm.getErrorMessages()
                   .add("资源池" + regionName + "内存余量不足 余量剩余" + resourcePoolCapacityDTO.getMemoryAviSale() + "GB，需要" + regionModel.getMemorySize() + "GB");
            }
            //检查ssd和hdd的大小是否足够
            if (resourcePoolCapacityDTO.getSsdStorageAviSale()
                                       .compareTo(BigDecimal.valueOf(regionModel.getSsdSize()))<0) {
                opm.getErrorMessages()
                   .add("资源池" + regionName + "ssd余量不足 余量剩余" + resourcePoolCapacityDTO.getSsdStorageAviSale() + "GB，需要" + regionModel.getSsdSize() + "GB");
            }
            if (resourcePoolCapacityDTO.getOtherStorageAviSale()
                                       .compareTo(BigDecimal.valueOf(regionModel.getHddOrSasSize())) < 0) {
                opm.getErrorMessages()
                   .add("资源池" + regionName + "hdd&SAS余量不足 余量剩余" + resourcePoolCapacityDTO.getOtherStorageAviSale() + "GB，需要" + regionModel.getHddOrSasSize() + "GB");
            }
            //校验EIP eip特殊一点需要按照groupCode来校验
            List<ResourcePoolCapacityDTO> regionCapacityByGroup = RegionCapacityHelper.getInstance()
                                                                                       .getRegionCapacityByGroup(regionCapacityDTO.getRegionGroupCode());
            ResourcePoolCapacityDTO groupCapacityDTO = RegionCapacityHelper.getInstance()
                                                                              .aggregateRegionCapacity(regionCapacityByGroup);
            if (groupCapacityDTO.getBandWidthSale().compareTo(BigDecimal.valueOf(regionModel.getEipBandwidth())) < 0) {
                opm.getErrorMessages()
                   .add("资源池" + regionName + "带宽余量不足 余量剩余" + groupCapacityDTO.getBandWidthSale() + "MB，需要" + regionModel.getEipBandwidth() + "MB");
            }
            //eip数量校验
            if (groupCapacityDTO.getIpAviSale().compareTo(BigDecimal.valueOf(regionModel.getEipNum())) < 0) {
                opm.getErrorMessages()
                   .add("资源池" + regionName + "eip数量余量不足 余量剩余" + groupCapacityDTO.getIpAviSale() + "个，需要" + regionModel.getEipNum() + "个");
            }
            //检查slb数量是否足够
            if (opm.getCheckSlbNum()) {
                //查询对应的静态数据资源池
                List<StaticProductStockDTO> slbStaticList = staticProductStockManager.list(new StaticProductStockQuery()
                        .setRegionCode(regionCode)
                        .setProductType("slb"));
                //把remainingAmount sum起来
                Integer slbNum = slbStaticList.stream()
                                              .map(StaticProductStockDTO::getRemainingAmount)
                                              .reduce(0, Integer::sum);
                //regionModel.getSlbNum()=0的时候可能是没有开通slb的，所以不校验
                if (slbNum > regionModel.getSlbNum()&&regionModel.getSlbNum()!=0) {
                    opm.getErrorMessages().add("资源池" + regionName + "slb数量余量不足 余量剩余" + slbNum + "个，需要" + regionModel.getSlbNum() + "个");
                }
            }
        }
        //特殊处理：检查容器配额
        checkContainerQuotaProductInResourcePoolCapacity(opm);
    }


    private void checkContainerQuotaProductInResourcePoolCapacity(ProductGeneralCheckOpm opm) {
        // 如果没有容器配额产品，直接返回
        if (ObjNullUtils.isNull(opm.getCqModelList()) || opm.getCqModelList().isEmpty()) {
            return;
        }

        // 整理容器配额产品资源需求
        organizeCQProduct(opm);

        // 获取资源池维度的需求汇总
        Map<String, ProductCapacityCheckModel> regionCheckModelMap = opm.getRegionCheckModelMap();

        // 遍历每个资源池，检查容器配额资源是否足够
        for (Map.Entry<String, ProductCapacityCheckModel> entry : regionCheckModelMap.entrySet()) {
            String regionCode = entry.getKey();
            ProductCapacityCheckModel regionModel = entry.getValue();

            // 只检查有容器配额需求的资源池
            if (regionModel.getGpuCoreSize() == 0 && regionModel.getGpuRatioSize() == 0 &&
                regionModel.getGpuVirtualCoreSize() == 0 && regionModel.getGpuVirtualMemorySize() == 0 &&
                regionModel.getCpuNum() == 0 && regionModel.getMemorySize() == 0) {
                continue;
            }

            try {
                // 查询协云当前配额情况
                // nodePoolName使用regionCode，orgId使用placeholder占位
                QuotaResultDTO quotaResult = xieyunOrgManager.selectOrgQuota(
                    xieyunProperties.getClusterNameByRegionCode(regionCode),
                    regionCode,
                    "placeholder"
                );

                // 检查CPU资源是否足够
                if (regionModel.getCpuNum() > 0) {
                    BigDecimal availableCpu = quotaResult.getCpu().getMax().subtract(quotaResult.getCpu().getQuota());
                    if (availableCpu.compareTo(BigDecimal.valueOf(regionModel.getCpuNum())) < 0) {
                        opm.getErrorMessages().add(String.format(
                            "资源池[%s]容器配额CPU资源不足，需要%d核，可用%s核",
                            regionCode, regionModel.getCpuNum(), availableCpu
                        ));
                    }
                }

                // 检查内存资源是否足够
                if (regionModel.getMemorySize() > 0) {
                    BigDecimal availableMemory = quotaResult.getMemory().getMax().subtract(quotaResult.getMemory().getQuota());
                    if (availableMemory.compareTo(BigDecimal.valueOf(regionModel.getMemorySize())) < 0) {
                        opm.getErrorMessages().add(String.format(
                            "资源池[%s]容器配额内存资源不足，需要%dGB，可用%sGB",
                            regionCode, regionModel.getMemorySize(), availableMemory
                        ));
                    }
                }

                // 检查GPU核心资源是否足够
                if (regionModel.getGpuCoreSize() > 0) {
                    BigDecimal availableGpuCore = quotaResult.getGpuCore().getMax().subtract(quotaResult.getGpuCore().getQuota());
                    if (availableGpuCore.compareTo(BigDecimal.valueOf(regionModel.getGpuCoreSize())) < 0) {
                        opm.getErrorMessages().add(String.format(
                            "资源池[%s]容器配额GPU核心资源不足，需要%d个，可用%s个",
                            regionCode, regionModel.getGpuCoreSize(), availableGpuCore
                        ));
                    }
                }

                // 检查GPU算力资源是否足够
                if (regionModel.getGpuRatioSize() > 0) {
                    BigDecimal availableGpuRatio = quotaResult.getGpuRatio().getMax().subtract(quotaResult.getGpuRatio().getQuota());
                    if (availableGpuRatio.compareTo(BigDecimal.valueOf(regionModel.getGpuRatioSize())) < 0) {
                        opm.getErrorMessages().add(String.format(
                            "资源池[%s]容器配额GPU算力资源不足，需要%d，可用%s",
                            regionCode, regionModel.getGpuRatioSize(), availableGpuRatio
                        ));
                    }
                }

                // 检查GPU虚拟核心资源是否足够
                if (regionModel.getGpuVirtualCoreSize() > 0) {
                    BigDecimal availableGpuVirtualCore = quotaResult.getGpuVirtualCore().getMax().subtract(quotaResult.getGpuVirtualCore().getQuota());
                    if (availableGpuVirtualCore.compareTo(BigDecimal.valueOf(regionModel.getGpuVirtualCoreSize())) < 0) {
                        opm.getErrorMessages().add(String.format(
                            "资源池[%s]容器配额GPU虚拟核心资源不足，需要%d个，可用%s个",
                            regionCode, regionModel.getGpuVirtualCoreSize(), availableGpuVirtualCore
                        ));
                    }
                }

                // 检查GPU虚拟内存资源是否足够
                if (regionModel.getGpuVirtualMemorySize() > 0) {
                    BigDecimal availableGpuVirtualMemory = quotaResult.getGpuVirtualMemory().getMax().subtract(quotaResult.getGpuVirtualMemory().getQuota());
                    if (availableGpuVirtualMemory.compareTo(BigDecimal.valueOf(regionModel.getGpuVirtualMemorySize())) < 0) {
                        opm.getErrorMessages().add(String.format(
                            "资源池[%s]容器配额GPU虚拟内存资源不足，需要%dGB，可用%sGB",
                            regionCode, regionModel.getGpuVirtualMemorySize(), availableGpuVirtualMemory
                        ));
                    }
                }

            } catch (Exception e) {
                // 如果查询协云配额失败，记录错误信息
                opm.getErrorMessages().add(String.format(
                    "资源池[%s]容器配额资源检查失败：%s",
                    regionCode, e.getMessage()
                ));
            }
        }
    }


    /**
     * az维度检查是否余量足够
     *
     * @param opm
     */
    @Override
    public void checkProductInAzCapacity(ProductGeneralCheckOpm opm) {
        //1. 整理ecs产品
        organizeEcsProduct(opm);
        //2. 整理gcs产品
        organizeGcsProduct(opm);
        //3. 整理evs产品
        organizeEvsProduct(opm);
        //4. 整理eip产品
        organizeEipProduct(opm);
        //5. 整理nat产品
        organizeNatProduct(opm);
        //6. 整理slb产品
        organizeSlbProduct(opm);
        //7. 整理container产品
        organizeCQProduct(opm);
        //拿出资源池维度的map
        Map<String, ProductCapacityCheckModel> azCheckModelMap = opm.getAzCheckModelMap();
        //去映射表找到资源池对应数据
        List<RegionCapacityDTO> regionCapacityDTOList = regionCapacityMapperManager.list(new RegionCapacityQuery()
                .setAzCodeList(StreamUtils.set2List(azCheckModelMap.keySet()))
        );
        Map<String, RegionCapacityDTO> capacityDTOMap = StreamUtils.toMap(regionCapacityDTOList, RegionCapacityDTO::getAzCode);
        //遍历资源池维度的map
        for (Map.Entry<String, ProductCapacityCheckModel> entry : azCheckModelMap.entrySet()) {
            String azCode = entry.getKey();
            ProductCapacityCheckModel regionModel = entry.getValue();
            RegionCapacityDTO regionCapacityDTO = capacityDTOMap.get(azCode);
            //如果regionCapacityDTO为空，说明没有找到对应的资源池，跳过
            if (ObjNullUtils.isNull(regionCapacityDTO)) {
                continue;
            }
            String azName = regionCapacityDTO.getAzName();
            //检查cpu和内存是否足够
            ResourcePoolCapacityDTO resourcePoolCapacityDTO = RegionCapacityHelper.getInstance()
                                                                                  .getRegionCapacity(regionCapacityDTO.getRegionGroupCode(), regionCapacityDTO.getRegionCode(), regionCapacityDTO.getAzCode());
            //检查cpu和内存是否足够
            if (resourcePoolCapacityDTO.getVcpuAviSale().compareTo(BigDecimal.valueOf(regionModel.getCpuNum())) < 0) {
                opm.getErrorMessages()
                   .add("可用区" + azName + "cpu余量不足 余量剩余" + resourcePoolCapacityDTO.getVcpuAviSale() + "核，需要" + regionModel.getCpuNum() + "核");
            }
            if (resourcePoolCapacityDTO.getMemoryAviSale()
                                       .compareTo(BigDecimal.valueOf(regionModel.getMemorySize())) < 0) {
                opm.getErrorMessages()
                   .add("可用区" + azName + "内存余量不足 余量剩余" + resourcePoolCapacityDTO.getMemoryAviSale() + "GB，需要" + regionModel.getMemorySize() + "GB");
            }
            //检查ssd和hdd的大小是否足够
            if (resourcePoolCapacityDTO.getSsdStorageAviSale()
                                       .compareTo(BigDecimal.valueOf(regionModel.getSsdSize()))< 0) {
                opm.getErrorMessages()
                   .add("可用区" + azName + "ssd余量不足 余量剩余" + resourcePoolCapacityDTO.getSsdStorageAviSale() + "GB，需要" + regionModel.getSsdSize() + "GB");
            }
            if (resourcePoolCapacityDTO.getOtherStorageAviSale()
                                       .compareTo(BigDecimal.valueOf(regionModel.getHddOrSasSize())) < 0) {
                opm.getErrorMessages()
                   .add("可用区" + azName + "hdd&SAS余量不足 余量剩余" + resourcePoolCapacityDTO.getOtherStorageAviSale() + "GB，需要" + regionModel.getHddOrSasSize() + "GB");
            }
            //校验EIP eip特殊一点需要按照groupCode来校验
            List<ResourcePoolCapacityDTO> regionCapacityByGroup = RegionCapacityHelper.getInstance()
                                                                                      .getRegionCapacityByGroup(regionCapacityDTO.getRegionGroupCode());
            ResourcePoolCapacityDTO groupCapacityDTO = RegionCapacityHelper.getInstance()
                                                                           .aggregateRegionCapacity(regionCapacityByGroup);
            if (groupCapacityDTO.getBandWidthSale().compareTo(BigDecimal.valueOf(regionModel.getEipBandwidth())) < 0) {
                opm.getErrorMessages()
                   .add("可用区" + azName + "带宽余量不足 余量剩余" + groupCapacityDTO.getBandWidthSale() + "MB，需要" + regionModel.getEipBandwidth() + "MB");
            }
            //eip数量校验
            if (groupCapacityDTO.getIpAviSale().compareTo(BigDecimal.valueOf(regionModel.getEipNum())) < 0) {
                opm.getErrorMessages()
                   .add("可用区" + azName + "eip数量余量不足 余量剩余" + groupCapacityDTO.getIpAviSale() + "个，需要" + regionModel.getEipNum() + "个");
            }
            //检查slb数量是否足够
            if (opm.getCheckSlbNum()) {
                //查询对应的静态数据资源池
                List<StaticProductStockDTO> slbStaticList = staticProductStockManager.list(new StaticProductStockQuery()
                        .setRegionCode(regionCapacityDTO.getRegionCode())
                        .setAzCode(regionCapacityDTO.getAzCode()));
                //把remainingAmount sum起来
                Integer slbNum = slbStaticList.stream()
                                              .map(StaticProductStockDTO::getRemainingAmount)
                                              .reduce(0, Integer::sum);
                //regionModel.getSlbNum()=0的时候可能是没有开通slb的，所以不校验
                if (slbNum > regionModel.getSlbNum()&&regionModel.getSlbNum()!=0) {
                    opm.getErrorMessages().add("可用区" + azName + "slb数量余量不足 余量剩余" + slbNum + "个，需要" + regionModel.getSlbNum() + "个");
                }
            }
        }
    }

    /**
     * 校验规格是否存在
     *
     * @param opm
     */
    @Override
    public void checkFlavorExist(ProductGeneralCheckOpm opm) {
        // Check ECS flavors
        if (ObjNullUtils.isNotNull(opm.getEcsModelList())) {
            for (EcsModel ecsModel : opm.getEcsModelList()) {
                FlavorQuery flavorQuery = new FlavorQuery()
                    .setRegionId(ecsModel.getRegionId())
                    .setName(ecsModel.getFlavorName());
                List<FlavorDTO> flavorDTOS = flavorModelManager.listFlavor(flavorQuery);
                if (!ObjNullUtils.isNotNull(flavorDTOS)) {
                    opm.getErrorMessages().add(String.format("规格[%s]在资源池[%s]中不存在", ecsModel.getFlavorName(), ecsModel.getRegionName()));
                }
            }
        }

        // Check GCS flavors
        if (ObjNullUtils.isNotNull(opm.getGcsModelList())) {
            for (EcsModel ecsModel : opm.getGcsModelList()) {
                FlavorQuery flavorQuery = new FlavorQuery()
                    .setRegionId(ecsModel.getRegionId())
                    .setName(ecsModel.getFlavorName());
                List<FlavorDTO> flavorDTOS = flavorModelManager.listFlavor(flavorQuery);
                if (!ObjNullUtils.isNotNull(flavorDTOS)) {
                    opm.getErrorMessages().add(String.format("规格[%s]在资源池[%s]中不存在", ecsModel.getFlavorName(), ecsModel.getRegionName()));
                }
            }
        }


        // Check mysql flavors
        if (ObjNullUtils.isNotNull(opm.getMysqlModelList())) {
            for (EcsModel ecsModel : opm.getMysqlModelList()) {
                FlavorQuery flavorQuery = new FlavorQuery()
                        .setRegionId(ecsModel.getRegionId())
                        .setName(ecsModel.getFlavorName());
                List<FlavorDTO> flavorDTOS = flavorModelManager.listFlavor(flavorQuery);
                if (!ObjNullUtils.isNotNull(flavorDTOS)) {
                    opm.getErrorMessages().add(String.format("规格[%s]在资源池[%s]中不存在", ecsModel.getFlavorName(), ecsModel.getRegionName()));
                }
            }
        }


        // Check redis flavors
        if (ObjNullUtils.isNotNull(opm.getRedisModelList())) {
            for (EcsModel ecsModel : opm.getRedisModelList()) {
                FlavorQuery flavorQuery = new FlavorQuery()
                        .setRegionId(ecsModel.getRegionId())
                        .setName(ecsModel.getFlavorName());
                List<FlavorDTO> flavorDTOS = flavorModelManager.listFlavor(flavorQuery);
                if (!ObjNullUtils.isNotNull(flavorDTOS)) {
                    opm.getErrorMessages().add(String.format("规格[%s]在资源池[%s]中不存在", ecsModel.getFlavorName(), ecsModel.getRegionName()));
                }
            }
        }

        // Check SLB flavors
        if (ObjNullUtils.isNotNull(opm.getSlbModelList())) {
            for (SlbModel slbModel : opm.getSlbModelList()) {
                FlavorQuery flavorQuery = new FlavorQuery()
                    .setRegionId(slbModel.getRegionId())
                    .setName(slbModel.getFlavorName());
                List<FlavorDTO> flavorDTOS = flavorModelManager.listFlavor(flavorQuery);
                if (!ObjNullUtils.isNotNull(flavorDTOS)) {
                    opm.getErrorMessages().add(String.format("规格[%s]在资源池[%s]中不存在", slbModel.getFlavorName(), slbModel.getRegionName()));
                }
            }
        }

        // Check NAT flavors
        if (ObjNullUtils.isNotNull(opm.getNatModelList())) {
            for (NatGatwayModel natModel : opm.getNatModelList()) {
                FlavorQuery flavorQuery = new FlavorQuery()
                    .setRegionId(natModel.getRegionId())
                    .setName(natModel.getFlavorName());
                List<FlavorDTO> flavorDTOS = flavorModelManager.listFlavor(flavorQuery);
                if (!ObjNullUtils.isNotNull(flavorDTOS)) {
                    opm.getErrorMessages().add(String.format("规格[%s]在资源池[%s]中不存在", natModel.getFlavorName(), natModel.getRegionName()));
                }
            }
        }
    }

    /**
     * 校验镜像是否存在
     *
     * @param opm
     */
    @Override
    public void checkImageExist(ProductGeneralCheckOpm opm) {
        // Check ECS images
        if (ObjNullUtils.isNotNull(opm.getEcsModelList())) {
            for (EcsModel ecsModel : opm.getEcsModelList()) {
                ImagesDTO imagesDTO = StreamUtils.findAny(imagesManager.list(new ImagesQuery()
                    .setRegionId(ecsModel.getRegionId())
                    .setOsType(ecsModel.getImageOs())
                    .setVersion(ecsModel.getImageVersion())));
                if (!ObjNullUtils.isNotNull(imagesDTO)) {
                    opm.getErrorMessages().add(String.format("镜像[%s-%s]在资源池[%s]中不存在",
                        ecsModel.getImageOs(), ecsModel.getImageVersion(), ecsModel.getRegionName()));
                }
            }
        }

        // Check GCS images
        if (ObjNullUtils.isNotNull(opm.getGcsModelList())) {
            for (EcsModel ecsModel : opm.getGcsModelList()) {
                ImagesDTO imagesDTO = StreamUtils.findAny(imagesManager.list(new ImagesQuery()
                    .setRegionId(ecsModel.getRegionId())
                    .setOsType(ecsModel.getImageOs())
                    .setVersion(ecsModel.getImageVersion())));
                if (!ObjNullUtils.isNotNull(imagesDTO)) {
                    opm.getErrorMessages().add(String.format("镜像[%s-%s]在资源池[%s]中不存在",
                        ecsModel.getImageOs(), ecsModel.getImageVersion(), ecsModel.getRegionName()));
                }
            }
        }
        // Check mysql images
        if (ObjNullUtils.isNotNull(opm.getMysqlModelList())) {
            for (EcsModel ecsModel : opm.getMysqlModelList()) {
                ImagesDTO imagesDTO = StreamUtils.findAny(imagesManager.list(new ImagesQuery()
                        .setRegionId(ecsModel.getRegionId())
                        .setOsType(ecsModel.getImageOs())
                        .setVersion(ecsModel.getImageVersion())));
                if (!ObjNullUtils.isNotNull(imagesDTO)) {
                    opm.getErrorMessages().add(String.format("镜像[%s-%s]在资源池[%s]中不存在",
                            ecsModel.getImageOs(), ecsModel.getImageVersion(), ecsModel.getRegionName()));
                }
            }
        }
        // Check redis images
        if (ObjNullUtils.isNotNull(opm.getRedisModelList())) {
            for (EcsModel ecsModel : opm.getRedisModelList()) {
                ImagesDTO imagesDTO = StreamUtils.findAny(imagesManager.list(new ImagesQuery()
                        .setRegionId(ecsModel.getRegionId())
                        .setOsType(ecsModel.getImageOs())
                        .setVersion(ecsModel.getImageVersion())));
                if (!ObjNullUtils.isNotNull(imagesDTO)) {
                    opm.getErrorMessages().add(String.format("镜像[%s-%s]在资源池[%s]中不存在",
                            ecsModel.getImageOs(), ecsModel.getImageVersion(), ecsModel.getRegionName()));
                }
            }
        }
    }


    private void organizeSlbProduct(ProductGeneralCheckOpm opm) {
        Map<String, ProductCapacityCheckModel> azCheckModelMap = opm.getAzCheckModelMap();
        Map<String, ProductCapacityCheckModel> regionCheckModelMap = opm.getRegionCheckModelMap();
        if (ObjNullUtils.isNotNull(opm.getSlbModelList())) {
            for (SlbModel slbModel : opm.getSlbModelList()) {
                //开通数量
                Integer openNum = opm.getCheckOpenNum() ? slbModel.getOpenNum() : 1;
                ProductCapacityCheckModel azModel = new ProductCapacityCheckModel();
                String azCode = slbModel.getAzCode();
                ProductCapacityCheckModel regionModel = new ProductCapacityCheckModel();
                String regionCode = slbModel.getRegionCode();
                //如果azCode不为空
                if (ObjNullUtils.isNotNull(azCode)) {
                    azModel = azCheckModelMap.getOrDefault(azCode, new ProductCapacityCheckModel());
                }
                //如果regionCode不为空
                if (ObjNullUtils.isNotNull(regionCode)) {
                    regionModel = regionCheckModelMap.getOrDefault(regionCode, new ProductCapacityCheckModel());
                }
                //获取是否有公网
                if (slbModel.getBindPublicIp()) {
                    for (EipModel eipModel : slbModel.getEipModelList()) {
                        //获取带宽的大小
                        Integer bandwidth = eipModel.getBandwidth();
                        azModel.setEipBandwidth(azModel.getEipBandwidth() + bandwidth * openNum);
                        regionModel.setEipBandwidth(regionModel.getEipBandwidth() + bandwidth * openNum);
                        //eip数量+1
                        azModel.setEipNum(azModel.getEipNum() + openNum);
                        regionModel.setEipNum(regionModel.getEipNum() + openNum);
                    }
                }
                azModel.setSlbNum(azModel.getSlbNum() + openNum);
                regionModel.setSlbNum(regionModel.getSlbNum() + openNum);
                //获取azCode和regionCode
                if (ObjNullUtils.isNotNull(azCode)) {
                    azCheckModelMap.put(azCode, azModel);
                }
                if (ObjNullUtils.isNotNull(regionCode)) {
                    regionCheckModelMap.put(regionCode, regionModel);
                }
            }
        }
    }


    private void organizeNatProduct(ProductGeneralCheckOpm opm) {
        Map<String, ProductCapacityCheckModel> azCheckModelMap = opm.getAzCheckModelMap();
        Map<String, ProductCapacityCheckModel> regionCheckModelMap = opm.getRegionCheckModelMap();
        if (ObjNullUtils.isNotNull(opm.getNatModelList())) {
            for (NatGatwayModel natModel : opm.getNatModelList()) {
                //开通数量
                Integer openNum = opm.getCheckOpenNum() ? natModel.getOpenNum() : 1;
                ProductCapacityCheckModel azModel = new ProductCapacityCheckModel();
                String azCode = natModel.getAzCode();
                ProductCapacityCheckModel regionModel = new ProductCapacityCheckModel();
                String regionCode = natModel.getRegionCode();
                //如果azCode不为空
                if (ObjNullUtils.isNotNull(azCode)) {
                    azModel = azCheckModelMap.getOrDefault(azCode, new ProductCapacityCheckModel());
                }
                //如果regionCode不为空
                if (ObjNullUtils.isNotNull(regionCode)) {
                    regionModel = regionCheckModelMap.getOrDefault(regionCode, new ProductCapacityCheckModel());
                }
                //获取是否有公网
                if (natModel.getBindPublicIp()) {
                    for (EipModel eipModel : natModel.getEipModelList()) {
                        //获取带宽的大小
                        Integer bandwidth = eipModel.getBandwidth();
                        azModel.setEipBandwidth(azModel.getEipBandwidth() + bandwidth * openNum);
                        regionModel.setEipBandwidth(regionModel.getEipBandwidth() + bandwidth * openNum);
                        //eip数量+1
                        azModel.setEipNum(azModel.getEipNum() + openNum);
                        regionModel.setEipNum(regionModel.getEipNum() + openNum);
                    }
                }
                //获取azCode和regionCode
                if (ObjNullUtils.isNotNull(azCode)) {
                    azCheckModelMap.put(azCode, azModel);
                }
                if (ObjNullUtils.isNotNull(regionCode)) {
                    regionCheckModelMap.put(regionCode, regionModel);
                }

            }
        }
    }

    private void organizeEipProduct(ProductGeneralCheckOpm opm) {
        Map<String, ProductCapacityCheckModel> azCheckModelMap = opm.getAzCheckModelMap();
        Map<String, ProductCapacityCheckModel> regionCheckModelMap = opm.getRegionCheckModelMap();
        if (ObjNullUtils.isNotNull(opm.getEipModelList())) {
            for (EipModel eipModel : opm.getEipModelList()) {
                //开通数量
                Integer openNum = opm.getCheckOpenNum() ? eipModel.getOpenNum() : 1;
                ProductCapacityCheckModel azModel = new ProductCapacityCheckModel();
                String azCode = eipModel.getAzCode();
                ProductCapacityCheckModel regionModel = new ProductCapacityCheckModel();
                String regionCode = eipModel.getRegionCode();
                //如果azCode不为空
                if (ObjNullUtils.isNotNull(azCode)) {
                    azModel = azCheckModelMap.getOrDefault(azCode, new ProductCapacityCheckModel());
                }
                //如果regionCode不为空
                if (ObjNullUtils.isNotNull(regionCode)) {
                    regionModel = regionCheckModelMap.getOrDefault(regionCode, new ProductCapacityCheckModel());
                }
                //获取带宽的大小
                Integer bandwidth = eipModel.getBandwidth();
                azModel.setEipBandwidth(azModel.getEipBandwidth() + bandwidth * openNum);
                regionModel.setEipBandwidth(regionModel.getEipBandwidth() + bandwidth * openNum);
                //eip数量+1
                azModel.setEipNum(azModel.getEipNum() + openNum);
                regionModel.setEipNum(regionModel.getEipNum() + openNum);
                //设置az和region的值
                if (ObjNullUtils.isNotNull(azCode)) {
                    azCheckModelMap.put(azCode, azModel);
                }
                if (ObjNullUtils.isNotNull(regionCode)) {
                    regionCheckModelMap.put(regionCode, regionModel);
                }
            }
        }
    }

    /**
     * 整理evs相关的产品
     *
     * @param opm
     */
    private void organizeEvsProduct(ProductGeneralCheckOpm opm) {
        Map<String, ProductCapacityCheckModel> azCheckModelMap = opm.getAzCheckModelMap();
        Map<String, ProductCapacityCheckModel> regionCheckModelMap = opm.getRegionCheckModelMap();
        if (ObjNullUtils.isNotNull(opm.getEvsModelList())) {
            for (MountDataDiskModel evsModel : opm.getEvsModelList()) {
                //开通数量
                Integer openNum = opm.getCheckOpenNum() ? evsModel.getOpenNum() : 1;
                ProductCapacityCheckModel azModel = new ProductCapacityCheckModel();
                String azCode = evsModel.getAzCode();
                ProductCapacityCheckModel regionModel = new ProductCapacityCheckModel();
                String regionCode = evsModel.getRegionCode();
                //如果azCode不为空
                if (ObjNullUtils.isNotNull(azCode)) {
                    azModel = azCheckModelMap.getOrDefault(azCode, new ProductCapacityCheckModel());
                }
                //如果regionCode不为空
                if (ObjNullUtils.isNotNull(regionCode)) {
                    regionModel = regionCheckModelMap.getOrDefault(regionCode, new ProductCapacityCheckModel());
                }
                //获取挂载盘的类型
                String dataDiskType = evsModel.getSysDiskType();
                Integer dataDiskSize = evsModel.getSysDiskSize();
                if (ObjNullUtils.isNotNull(dataDiskType)) {
                    //如果dataDiskType为SSD
                    if (dataDiskType.contains("SSD")) {
                        //获取SSD的大小
                        azModel.setSsdSize(azModel.getSsdSize() + dataDiskSize * openNum);
                        regionModel.setSsdSize(regionModel.getSsdSize() + dataDiskSize * openNum);
                    } else {
                        //获取HDD的大小
                        azModel.setHddOrSasSize(azModel.getHddOrSasSize() + dataDiskSize * openNum);
                        regionModel.setHddOrSasSize(regionModel.getHddOrSasSize() + dataDiskSize * openNum);

                    }
                }
                //设置az和region的值
                if (ObjNullUtils.isNotNull(azCode)) {
                    azCheckModelMap.put(azCode, azModel);
                }
                if (ObjNullUtils.isNotNull(regionCode)) {
                    regionCheckModelMap.put(regionCode, regionModel);
                }
            }
        }
    }

    /**
     * 整理gcs相关的产品
     *
     * @param opm
     */
    private void organizeGcsProduct(ProductGeneralCheckOpm opm) {
        Map<String, ProductCapacityCheckModel> azCheckModelMap = opm.getAzCheckModelMap();
        Map<String, ProductCapacityCheckModel> regionCheckModelMap = opm.getRegionCheckModelMap();
        //1. 整理ecs
        if (ObjNullUtils.isNotNull(opm.getGcsModelList())) {
            for (EcsModel ecsModel : opm.getGcsModelList()) {
                //开通数量
                Integer openNum = opm.getCheckOpenNum() ? ecsModel.getOpenNum() : 1;
                ProductCapacityCheckModel azModel = new ProductCapacityCheckModel();
                String azCode = ecsModel.getAzCode();
                ProductCapacityCheckModel regionModel = new ProductCapacityCheckModel();
                String regionCode = ecsModel.getRegionCode();
                //如果azCode不为空
                if (ObjNullUtils.isNotNull(azCode)) {
                    azModel = azCheckModelMap.getOrDefault(azCode, new ProductCapacityCheckModel());
                }
                //如果regionCode不为空
                if (ObjNullUtils.isNotNull(regionCode)) {
                    regionModel = regionCheckModelMap.getOrDefault(regionCode, new ProductCapacityCheckModel());
                }
                //获取规格 gcs的规格比如 16C32GB/1T4 这样的要提取出来
                String flavorName = ecsModel.getFlavorName();
                //获取cpu和内存
                if (flavorName != null && flavorName.contains("C")) {
                    // GCS规格格式为：16C32GB/1T4，先分离 CPU和内存 部分
                    String cpuMemPart = flavorName;
                    if (flavorName.contains("/")) {
                        cpuMemPart = flavorName.split("/")[0];
                    }

                    String[] parts = cpuMemPart.split("C");
                    // 提取CPU核数
                    Integer cpuNum = Integer.parseInt(parts[0]);
                    azModel.setCpuNum(azModel.getCpuNum() + cpuNum * openNum);
                    regionModel.setCpuNum(regionModel.getCpuNum() + cpuNum * openNum);
                    // 提取内存大小
                    String memoryStr = parts[1].replace("GB", "");
                    Integer memorySize = Integer.parseInt(memoryStr);
                    azModel.setMemorySize(azModel.getMemorySize() + memorySize * openNum);
                    regionModel.setMemorySize(regionModel.getMemorySize() + memorySize * openNum);
                }
                //---------------获取系统盘的大小---------------------
                String systemDiskType = ecsModel.getSysDiskType();
                Integer sysDiskSize = ecsModel.getSysDiskSize();
                if (ObjNullUtils.isNotNull(systemDiskType)) {
                    //如果sysDiskType为SSD
                    if (systemDiskType.contains("SSD")) {
                        //获取SSD的大小
                        azModel.setSsdSize(azModel.getSsdSize() + sysDiskSize * openNum);
                        regionModel.setSsdSize(regionModel.getSsdSize() + sysDiskSize * openNum);
                    } else {
                        //获取HDD的大小
                        azModel.setHddOrSasSize(azModel.getHddOrSasSize() + sysDiskSize * openNum);
                        regionModel.setHddOrSasSize(regionModel.getHddOrSasSize() + sysDiskSize * openNum);
                    }
                }
                //---------------获取挂载盘的大小---------------------
                if (ecsModel.getMountDataDisk()) {
                    for (MountDataDiskModel mountDataDisk : ecsModel.getMountDataDiskList()) {
                        //获取挂载盘的类型
                        String dataDiskType = mountDataDisk.getSysDiskType();
                        Integer dataDiskSize = mountDataDisk.getSysDiskSize();
                        if (ObjNullUtils.isNotNull(dataDiskType)) {
                            //如果dataDiskType为SSD
                            if (dataDiskType.contains("SSD")) {
                                //获取SSD的大小
                                azModel.setSsdSize(azModel.getSsdSize() + dataDiskSize * openNum);
                                regionModel.setSsdSize(regionModel.getSsdSize() + dataDiskSize * openNum);
                            } else {
                                //获取HDD的大小
                                azModel.setHddOrSasSize(azModel.getHddOrSasSize() + dataDiskSize * openNum);
                                regionModel.setHddOrSasSize(regionModel.getHddOrSasSize() + dataDiskSize * openNum);
                            }
                        }
                    }
                }
                //---------------获取带宽的大小---------------------
                if (ecsModel.getBindPublicIp()) {
                    for (EipModel eipModel : ecsModel.getEipModelList()) {
                        //获取带宽的大小
                        azModel.setEipBandwidth(azModel.getEipBandwidth() + eipModel.getBandwidth());
                        regionModel.setEipBandwidth(regionModel.getEipBandwidth() + eipModel.getBandwidth());
                        //eip数量+1
                        azModel.setEipNum(azModel.getEipNum() + openNum);
                        regionModel.setEipNum(regionModel.getEipNum() + openNum);

                    }
                }
                //设置az和region的值
                if (ObjNullUtils.isNotNull(azCode)) {
                    azCheckModelMap.put(azCode, azModel);
                }
                if (ObjNullUtils.isNotNull(regionCode)) {
                    regionCheckModelMap.put(regionCode, regionModel);
                }
            }
        }

    }

    /**
     * 整理ecs相关的产品
     *
     * @param opm
     */
    private void organizeEcsProduct(ProductGeneralCheckOpm opm) {
        Map<String, ProductCapacityCheckModel> azCheckModelMap = opm.getAzCheckModelMap();
        Map<String, ProductCapacityCheckModel> regionCheckModelMap = opm.getRegionCheckModelMap();
        //1. 整理ecs
        if (ObjNullUtils.isNotNull(opm.getEcsModelList())) {
            for (EcsModel ecsModel : opm.getEcsModelList()) {
                //开通数量
                Integer openNum = opm.getCheckOpenNum() ? ecsModel.getOpenNum() : 1;
                ProductCapacityCheckModel azModel = new ProductCapacityCheckModel();
                String azCode = ecsModel.getAzCode();
                ProductCapacityCheckModel regionModel = new ProductCapacityCheckModel();
                String regionCode = ecsModel.getRegionCode();
                //如果azCode不为空
                if (ObjNullUtils.isNotNull(azCode)) {
                    azModel = azCheckModelMap.getOrDefault(azCode, new ProductCapacityCheckModel());
                }
                //如果regionCode不为空
                if (ObjNullUtils.isNotNull(regionCode)) {
                    regionModel = regionCheckModelMap.getOrDefault(regionCode, new ProductCapacityCheckModel());
                }
                //获取规格 gcs的规格比如 16C32GB/1T4 这样的要提取出来
                String flavorName = ecsModel.getFlavorName();
                //获取cpu和内存
                if (flavorName != null && flavorName.contains("C")) {
                    // ECS规格格式为：16C32GB，先分离 CPU和内存 部分
                    String[] parts = flavorName.split("C");
                    // 提取CPU核数
                    Integer cpuNum = Integer.parseInt(parts[0]);
                    azModel.setCpuNum(azModel.getCpuNum() + cpuNum * openNum);
                    regionModel.setCpuNum(regionModel.getCpuNum() + cpuNum * openNum);
                    // 提取内存大小
                    String memoryStr = parts[1].replace("GB", "");
                    Integer memorySize = Integer.parseInt(memoryStr);
                    azModel.setMemorySize(azModel.getMemorySize() + memorySize * openNum);
                    regionModel.setMemorySize(regionModel.getMemorySize() + memorySize * openNum);
                }
                //---------------获取系统盘的大小---------------------
                String systemDiskType = ecsModel.getSysDiskType();
                Integer sysDiskSize = ecsModel.getSysDiskSize();
                if (ObjNullUtils.isNotNull(systemDiskType)) {
                    //如果sysDiskType为SSD
                    if (systemDiskType.contains("SSD")) {
                        //获取SSD的大小
                        azModel.setSsdSize(azModel.getSsdSize() + sysDiskSize * openNum);
                        regionModel.setSsdSize(regionModel.getSsdSize() + sysDiskSize * openNum);
                    } else {
                        //获取HDD的大小
                        azModel.setHddOrSasSize(azModel.getHddOrSasSize() + sysDiskSize * openNum);
                        regionModel.setHddOrSasSize(regionModel.getHddOrSasSize() + sysDiskSize * openNum);
                    }
                }
                //---------------获取挂载盘的大小---------------------
                if (ecsModel.getMountDataDisk()) {
                    for (MountDataDiskModel mountDataDisk : ecsModel.getMountDataDiskList()) {
                        //获取挂载盘的类型
                        String dataDiskType = mountDataDisk.getSysDiskType();
                        Integer dataDiskSize = mountDataDisk.getSysDiskSize();
                        if (ObjNullUtils.isNotNull(dataDiskType)) {
                            //如果dataDiskType为SSD
                            if (dataDiskType.contains("SSD")) {
                                //获取SSD的大小
                                azModel.setSsdSize(azModel.getSsdSize() + dataDiskSize * openNum);
                                regionModel.setSsdSize(regionModel.getSsdSize() + dataDiskSize * openNum);
                            } else {
                                //获取HDD的大小
                                azModel.setHddOrSasSize(azModel.getHddOrSasSize() + dataDiskSize * openNum);
                                regionModel.setHddOrSasSize(regionModel.getHddOrSasSize() + dataDiskSize * openNum);
                            }
                        }
                    }
                }
                //---------------获取带宽的大小---------------------
                if (ecsModel.getBindPublicIp()) {
                    for (EipModel eipModel : ecsModel.getEipModelList()) {
                        //获取带宽的大小
                        azModel.setEipBandwidth(azModel.getEipBandwidth() + eipModel.getBandwidth());
                        regionModel.setEipBandwidth(regionModel.getEipBandwidth() + eipModel.getBandwidth());
                        //eip数量+1
                        azModel.setEipNum(azModel.getEipNum() + openNum);
                        regionModel.setEipNum(regionModel.getEipNum() + openNum);

                    }
                }
                //设置az和region的值
                if (ObjNullUtils.isNotNull(azCode)) {
                    azCheckModelMap.put(azCode, azModel);
                }
                if (ObjNullUtils.isNotNull(regionCode)) {
                    regionCheckModelMap.put(regionCode, regionModel);
                }
            }
        }
    }


    private <T extends EcsModel> void checkEcsProductInResourcePool(List<T> ecsModelList, List<String> errorMessages,Boolean isAz) {
        //如果为空直接返回
        if (ObjNullUtils.isNotNull(ecsModelList)) {
            for (EcsModel ecsModel : ecsModelList) {
                //获取产品RegionId和名称
                Long regionId = ecsModel.getRegionId();
                String regionName = ecsModel.getRegionName(); // 获取资源池名称
                //产品类型
                String productType = ecsModel.getProductType();

                List<ProductSpecSupportDTO> ecsList = productSpecSupportManager.list(new ProductSpecSupportQuery()
                        .setRegionId(String.valueOf(regionId))
                        .setProductType(productType)
                        .setAzId(isAz?ecsModel.getAzId().toString():null)
                );
                if (!ObjNullUtils.isNotNull(ecsList)) {
                    errorMessages.add(String.format("产品[云主机产品%s]在资源池[%s]中不支持",productType, regionName));
                }

                //获取产品系统盘的类型
                String systemDiskType = ecsModel.getSysDiskType();
                List<ProductSpecSupportDTO> sysdiskList = productSpecSupportManager.list(new ProductSpecSupportQuery()
                        .setRegionId(String.valueOf(regionId))
                        .setProductType(systemDiskType.toLowerCase())
                        .setAzId(isAz?ecsModel.getAzId().toString():null)
                );
                if (!ObjNullUtils.isNotNull(sysdiskList)) {
                    errorMessages.add(String.format("产品[系统盘产品:%s]在资源池[%s]中不支持", systemDiskType, regionName));
                }

                //如果存在挂载盘
                if (ObjNullUtils.isNotNull(ecsModel.getMountDataDisk()) && ObjNullUtils.isNotNull(ecsModel.getMountDataDiskList())) {
                    List<MountDataDiskModel> mountDataDiskList = ecsModel.getMountDataDiskList();
                    for (MountDataDiskModel mountDataDisk : mountDataDiskList) {
                        //获取挂载盘的类型
                        String dataDiskType = mountDataDisk.getSysDiskType();
                        List<ProductSpecSupportDTO> datadiskList = productSpecSupportManager.list(new ProductSpecSupportQuery()
                                .setRegionId(String.valueOf(regionId))
                                .setProductType(dataDiskType.toLowerCase())
                                .setAzId(isAz?ecsModel.getAzId().toString():null)
                        );
                        if (!ObjNullUtils.isNotNull(datadiskList)) {
                            errorMessages.add(String.format("产品[数据盘产品:%s]在资源池[%s]中不支持", dataDiskType, regionName));
                        }
                    }
                }
            }
        }
    }

    private void checkEvsProductInResourcePool(List<MountDataDiskModel> ecsModelList, List<String> errorMessages,Boolean isAz) {
        //如果为空直接返回
        if (ObjNullUtils.isNotNull(ecsModelList)) {
            for (MountDataDiskModel ecsModel : ecsModelList) {
                //获取产品RegionId和名称
                Long regionId = ecsModel.getRegionId();
                String regionName = ecsModel.getRegionName(); // 获取资源池名称
                //获取挂载盘的类型
                String dataDiskType = ecsModel.getSysDiskType();
                List<ProductSpecSupportDTO> datadiskList = productSpecSupportManager.list(new ProductSpecSupportQuery()
                        .setRegionId(String.valueOf(regionId))
                        .setProductType(dataDiskType.toLowerCase())
                        .setAzId(isAz?ecsModel.getAzId().toString():null)
                );
                if (!ObjNullUtils.isNotNull(datadiskList)) {
                    errorMessages.add(String.format("产品[数据盘产品:%s]在资源池[%s]中不支持", dataDiskType, regionName));
                }
            }
        }
    }

    private void checkEipProductInResourcePool(List<EipModel> eipModelList, List<String> errorMessages,Boolean isAz) {

    }

    private void checkNatProductInResourcePool(List<NatGatwayModel> ecsModelList, List<String> errorMessages,Boolean isAz) {
        //如果为空直接返回
        if (ObjNullUtils.isNotNull(ecsModelList)) {
            for (NatGatwayModel ecsModel : ecsModelList) {
                //获取产品RegionId和名称
                Long regionId = ecsModel.getRegionId();
                String regionName = ecsModel.getRegionName(); // 获取资源池名称
                //获取产品类型
                String productType = ecsModel.getProductType();

                List<ProductSpecSupportDTO> ecsList = productSpecSupportManager.list(new ProductSpecSupportQuery()
                        .setRegionId(String.valueOf(regionId))
                        .setProductType(productType)
                        .setAzId(isAz?ecsModel.getAzId().toString():null)
                );
                if (!ObjNullUtils.isNotNull(ecsList)) {
                    errorMessages.add(String.format("产品[nat网关产品:%s]在资源池[%s]中不支持", productType, regionName));
                }
            }
        }
    }

    private void checkSlbProductInResourcePool(List<SlbModel> slbModels, List<String> errorMessages,Boolean isAz) {
        //如果为空直接返回
        if (ObjNullUtils.isNotNull(slbModels)) {
            for (SlbModel slbModel : slbModels) {
                //获取产品RegionId和名称
                Long regionId = slbModel.getRegionId();
                String regionName = slbModel.getRegionName(); // 获取资源池名称
                //获取产品类型
                String productType = slbModel.getProductType();
                List<ProductSpecSupportDTO> ecsList = productSpecSupportManager.list(new ProductSpecSupportQuery()
                        .setRegionId(String.valueOf(regionId))
                        .setProductType(productType)
                        .setAzId(isAz?slbModel.getAzId().toString():null)
                );
                if (!ObjNullUtils.isNotNull(ecsList)) {
                    errorMessages.add(String.format("产品[slb产品:%s]在资源池[%s]中不支持", productType, regionName));
                }
            }

        }
    }

    private void checkObsProductInResourcePool(List<ObsModel> obsModels, List<String> errorMessages,Boolean isAz) {
        //如果为空直接返回
        if (ObjNullUtils.isNotNull(obsModels)) {
            for (ObsModel obsModel : obsModels) {
                //获取产品RegionId和名称
                Long regionId = obsModel.getRegionId();
                String regionName = obsModel.getRegionName(); // 获取资源池名称
                //获取产品类型
                String productType = obsModel.getProductType();
                List<ProductSpecSupportDTO> ecsList = productSpecSupportManager.list(new ProductSpecSupportQuery()
                        .setRegionId(String.valueOf(regionId))
                        .setProductType(productType)
                        .setAzId(isAz?obsModel.getAzId().toString():null)
                );
                if (!ObjNullUtils.isNotNull(ecsList)) {
                    errorMessages.add(String.format("产品[obs产品:%s]在资源池[%s]中不支持", productType, regionName));
                }
            }
        }
    }

    /**
     * 检查容器产品是否在对应资源池中存在
     */
    private void checkCQProductInResourcePool(List<CQModel> containerModels, List<String> errorMessages, Boolean isAz) {
        //如果为空直接返回
        if (ObjNullUtils.isNotNull(containerModels)) {
            for (CQModel containerModel : containerModels) {
                //获取产品RegionId和名称
                Long regionId = containerModel.getRegionId();
                String regionName = containerModel.getRegionName(); // 获取资源池名称
                //获取产品类型
                String productType = containerModel.getProductType();
                List<ProductSpecSupportDTO> containerList = productSpecSupportManager.list(new ProductSpecSupportQuery()
                        .setRegionId(String.valueOf(regionId))
                        .setProductType(productType)
                        .setAzId(isAz ? containerModel.getAzId().toString() : null)
                );
                if (!ObjNullUtils.isNotNull(containerList)) {
                    errorMessages.add(String.format("产品[容器产品:%s]在资源池[%s]中不支持", productType, regionName));
                }
            }
        }
    }

    /**
     * 整理容器相关的产品
     */
    private void organizeCQProduct(ProductGeneralCheckOpm opm) {
        Map<String, ProductCapacityCheckModel> azCheckModelMap = opm.getAzCheckModelMap();
        Map<String, ProductCapacityCheckModel> regionCheckModelMap = opm.getRegionCheckModelMap();
        if (ObjNullUtils.isNotNull(opm.getCqModelList())) {
            for (CQModel cqModel : opm.getCqModelList()) {
                //开通数量
                Integer openNum = opm.getCheckOpenNum() ? cqModel.getOpenNum() : 1;
                ProductCapacityCheckModel azModel = new ProductCapacityCheckModel();
                String azCode = cqModel.getAzCode();
                ProductCapacityCheckModel regionModel = new ProductCapacityCheckModel();
                String regionCode = cqModel.getRegionCode();
                //如果azCode不为空
                if (ObjNullUtils.isNotNull(azCode)) {
                    azModel = azCheckModelMap.getOrDefault(azCode, new ProductCapacityCheckModel());
                }
                //如果regionCode不为空
                if (ObjNullUtils.isNotNull(regionCode)) {
                    regionModel = regionCheckModelMap.getOrDefault(regionCode, new ProductCapacityCheckModel());
                }

                // 容器资源消耗CPU和内存
                Integer cpuCores = cqModel.getVCpus();
                Integer memorySize = cqModel.getRam();
                Integer gpuCore = cqModel.getGpuCore();
                Integer gpuRatio = cqModel.getGpuRatio();
                Integer gpuVirtualCore = cqModel.getGpuVirtualCore();
                Integer gpuVirtualMemory = cqModel.getGpuVirtualMemory();

                azModel.setCpuNum(azModel.getCpuNum() + cpuCores * openNum);
                regionModel.setCpuNum(regionModel.getCpuNum() + cpuCores * openNum);

                azModel.setMemorySize(azModel.getMemorySize() + memorySize * openNum);
                regionModel.setMemorySize(regionModel.getMemorySize() + memorySize * openNum);

                if (ObjNullUtils.isNotNull(gpuCore)) {
                    azModel.setGpuCoreSize(azModel.getGpuCoreSize() + gpuCore * openNum);
                    regionModel.setGpuCoreSize(regionModel.getGpuCoreSize() + gpuCore * openNum);
                }
                if (ObjNullUtils.isNotNull(gpuRatio)) {
                    azModel.setGpuRatioSize(azModel.getGpuRatioSize() + gpuRatio * openNum);
                    regionModel.setGpuRatioSize(regionModel.getGpuRatioSize() + gpuRatio * openNum);
                }
                if (ObjNullUtils.isNotNull(gpuVirtualCore)) {
                    azModel.setGpuVirtualCoreSize(azModel.getGpuVirtualCoreSize() + gpuVirtualCore * openNum);
                    regionModel.setGpuVirtualCoreSize(regionModel.getGpuVirtualCoreSize() + gpuVirtualCore * openNum);
                }
                if (ObjNullUtils.isNotNull(gpuVirtualMemory)) {
                    azModel.setGpuVirtualMemorySize(azModel.getGpuVirtualMemorySize() + gpuVirtualMemory * openNum);
                    regionModel.setGpuVirtualMemorySize(regionModel.getGpuVirtualMemorySize() + gpuVirtualMemory * openNum);
                }

                //设置az和region的值
                if (ObjNullUtils.isNotNull(azCode)) {
                    azCheckModelMap.put(azCode, azModel);
                }
                if (ObjNullUtils.isNotNull(regionCode)) {
                    regionCheckModelMap.put(regionCode, regionModel);
                }
            }
        }
    }



}

package com.datatech.slgzt.model.req.vip;

import lombok.Data;

/**
 * 新增虚拟IP请求
 */
@Data
public class VirtualIpAddReq {
    /** 虚拟IP名称 */
    private String vipName;
    /** 云平台编码 */
    private String domainCode;
    /** 云平台名称 */
    private String domainName;
    /** VPC/网络ID */
    private String vpcId;
    private String vpcName;
    /** 业务系统ID */
    private String businessSystemId;
    /** 业务系统名称 */
    private String businessSystemName;
    /** 资源池ID */
    private String regionId;
    /** 资源池编码 */
    private String regionCode;
    /** 资源池名称 */
    private String regionName;
    /** 可用区ID */
    private String azId;
    /** 可用区名称 */
    private String azName;
    /** 子网ID */
    private String subnetId;
    /** 子网名称 */
    private String subnetName;
    /** 云类型编码 */
    private String catalogueDomainCode;
    /** 云类型名称 */
    private String catalogueDomainName;
    /** IP地址 */
    private String ipAddress;
    /** 子网网段 */
    private String cidr;
    /** 描述 */
    private String description;
} 
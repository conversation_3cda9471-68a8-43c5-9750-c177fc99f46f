package com.datatech.slgzt.impl.service.standard;

import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.ResOpenEnum;
import com.datatech.slgzt.manager.StandardWorkOrderManager;
import com.datatech.slgzt.manager.StandardWorkOrderProductManager;
import com.datatech.slgzt.model.dto.OrderStatusNoticeDTO;
import com.datatech.slgzt.model.dto.StandardWorkOrderDTO;
import com.datatech.slgzt.model.dto.StandardWorkOrderProductDTO;
import com.datatech.slgzt.model.nostander.CQModel;
import com.datatech.slgzt.service.standard.StandardResOpenService;
import com.datatech.slgzt.service.xieyun.XieyunEnvironmentService;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.Precondition;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 容器配额(CQ)资源开通服务实现
 * <AUTHOR>
 * @description 容器配额资源开通，调用协云平台创建基础环境
 * @date 2025年05月27日
 */
@Slf4j
@Service
public class StandardResCQOpenServiceImpl implements StandardResOpenService {

    @Resource
    private StandardWorkOrderManager standardWorkOrderManager;
    
    @Resource
    private StandardWorkOrderProductManager productManager;
    
    @Resource
    private XieyunEnvironmentService xieyunEnvironmentService;

    @Override
    public void openStandardResource(StandardWorkOrderProductDTO productDTO) {
        log.info("开始开通容器配额资源，productId: {}", productDTO.getId());
        
        try {
            // 获取工单信息
            StandardWorkOrderDTO orderDTO = standardWorkOrderManager.getById(productDTO.getWorkOrderId());
            Precondition.checkArgument(ObjNullUtils.isNotNull(orderDTO), "工单信息不存在");
            
            // 解析容器配额模型
            CQModel cqModel = JSON.parseObject(productDTO.getPropertySnapshot(), CQModel.class);
            Precondition.checkArgument(ObjNullUtils.isNotNull(cqModel), "容器配额配置信息不存在");
            
            // 校验必要参数
            validateCQModel(cqModel);
            
            // 设置默认值
            setDefaultValues(cqModel);
            
            // 更新产品状态为开通中
            productManager.updateStatusById(productDTO.getId(), ResOpenEnum.OPENING.getCode());
            productManager.updateStatusByParentId(productDTO.getId(), ResOpenEnum.OPENING.getCode());
            
            // 调用协云平台创建基础环境
            String executionId = xieyunEnvironmentService.createBaseEnvironment(productDTO.getSubOrderId(), null, cqModel);
            log.info("容器配额资源开通请求已提交，productId: {}, executionId: {}", productDTO.getId(), executionId);
            
        } catch (Exception e) {
            log.error("容器配额资源开通失败，productId: {}", productDTO.getId(), e);
            // 更新状态为开通失败
            productManager.updateStatusById(productDTO.getId(), ResOpenEnum.OPEN_FAIL.getCode());
            productManager.updateStatusByParentId(productDTO.getId(), ResOpenEnum.OPEN_FAIL.getCode());
            throw new RuntimeException("容器配额资源开通失败: " + e.getMessage(), e);
        }
    }

    /**
     * 校验CQ模型必要参数
     */
    private void validateCQModel(CQModel cqModel) {
        Precondition.checkArgument(ObjNullUtils.isNotNull(cqModel.getVCpus()) && cqModel.getVCpus() > 0, 
            "容器配额-核心数必须大于0");
        Precondition.checkArgument(ObjNullUtils.isNotNull(cqModel.getRam()) && cqModel.getRam() > 0, 
            "容器配额-内存大小必须大于0");
        Precondition.checkArgument(ObjNullUtils.isNotNull(cqModel.getA4Account()), 
            "4A账号不能为空");
        Precondition.checkArgument(ObjNullUtils.isNotNull(cqModel.getA4Phone()), 
            "4A账号绑定的手机不能为空");
    }

    /**
     * 设置默认值
     */
    private void setDefaultValues(CQModel cqModel) {
//        // 设置协云用户信息，如果没有提供则使用4A账号信息
//        if (ObjNullUtils.isNull(cqModel.getXieyunUserName())) {
//            cqModel.setXieyunUserName(cqModel.getA4Account());
//        }
//        if (ObjNullUtils.isNull(cqModel.getXieyunDisplayName())) {
//            cqModel.setXieyunDisplayName(cqModel.getA4Account());
//        }
//        if (ObjNullUtils.isNull(cqModel.getXieyunEmail())) {
//            cqModel.setXieyunEmail(cqModel.getA4Account() + "@example.com");
//        }
//
//        // 设置协云组织信息，如果没有提供则使用容器名称
//        if (ObjNullUtils.isNull(cqModel.getXieyunOrgName())) {
//            cqModel.setXieyunOrgName(cqModel.getCqName() + "-org");
//        }
//        if (ObjNullUtils.isNull(cqModel.getXieyunOrgDesc())) {
//            cqModel.setXieyunOrgDesc(cqModel.getCqName() + " 组织");
//        }
//
//        // 设置默认集群和节点池
//        if (ObjNullUtils.isNull(cqModel.getXieyunClusterName())) {
//            cqModel.setXieyunClusterName("eic-wz-cluster");
//        }
//        if (ObjNullUtils.isNull(cqModel.getXieyunNodePoolName())) {
//            cqModel.setXieyunNodePoolName("default");
//        }
//
//        // 设置默认制品ID
//        if (ObjNullUtils.isNull(cqModel.getXieyunRegistryId())) {
//            cqModel.setXieyunRegistryId("4940087269a6443b");
//        }
    }

    @Override
    public ProductTypeEnum registerOpenService() {
        return ProductTypeEnum.CQ;
    }

    @Override
    public void layoutTaskNotify(OrderStatusNoticeDTO dto) {
//        log.info("收到容器配额资源开通状态通知: {}", JSON.toJSONString(dto));
//
//        try {
//            // 根据通知更新产品状态
//            String status = dto.getOverallStatus();
//            String productId = dto.getId();
//
//            if ("SUCCESS".equals(status)) {
//                // 开通成功
//                productManager.updateStatusById(productId, ResOpenEnum.OPEN_SUCCESS.getCode());
//                productManager.updateStatusByParentId(productId, ResOpenEnum.OPEN_SUCCESS.getCode());
//                log.info("容器配额资源开通成功，productId: {}", productId);
//            } else if ("FAILED".equals(status)) {
//                // 开通失败
//                productManager.updateStatusById(productId, ResOpenEnum.OPEN_FAIL.getCode());
//                productManager.updateStatusByParentId(productId, ResOpenEnum.OPEN_FAIL.getCode());
//                log.error("容器配额资源开通失败，productId: {}, message: {}", productId, dto.getMessage());
//            } else {
//                // 其他状态，记录日志
//                log.info("容器配额资源开通状态更新，productId: {}, status: {}, message: {}",
//                    productId, status, dto.getMessage());
//            }
//
//        } catch (Exception e) {
//            log.error("处理容器配额资源开通状态通知失败: {}", JSON.toJSONString(dto), e);
//        }
    }
}

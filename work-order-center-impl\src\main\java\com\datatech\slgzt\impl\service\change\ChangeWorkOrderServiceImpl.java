package com.datatech.slgzt.impl.service.change;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.convert.ChangeWorkOrderServiceConvert;
import com.datatech.slgzt.enums.*;
import com.datatech.slgzt.enums.bpmn.ActivityEnum;
import com.datatech.slgzt.enums.bpmn.RecoveryOrderNodeEnum;
import com.datatech.slgzt.enums.domain.CatalogueDomain;
import com.datatech.slgzt.exception.UniversalException;
import com.datatech.slgzt.helps.UserHelper;
import com.datatech.slgzt.manager.*;
import com.datatech.slgzt.model.baseconfig.OacConfig;
import com.datatech.slgzt.model.bpmn.ActivityTaskVo;
import com.datatech.slgzt.model.bpmn.InnerTask;
import com.datatech.slgzt.model.change.*;
import com.datatech.slgzt.model.bpmn.TaskNodeDTO;
import com.datatech.slgzt.model.dto.ChangeWorkOrderProductDTO;
import com.datatech.slgzt.model.dto.FlavorDTO;
import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import com.datatech.slgzt.model.dto.WorkOrderAuthLogDTO;
import com.datatech.slgzt.model.dto.change.ChangeAuditWorkOrderDTO;
import com.datatech.slgzt.model.dto.change.ChangeWorkOrderDTO;
import com.datatech.slgzt.model.home.AuditCountVo;
import com.datatech.slgzt.model.opm.ChangeWorkOrderCreateOpm;
import com.datatech.slgzt.model.query.*;
import com.datatech.slgzt.model.sms.SmsSendModel;
import com.datatech.slgzt.model.usercenter.UserCenterUserDTO;
import com.datatech.slgzt.service.OrderDataProvideService;
import com.datatech.slgzt.service.UserService;
import com.datatech.slgzt.service.bpmn.BaseActivity;
import com.datatech.slgzt.service.change.ChangeWorkOrderService;
import com.datatech.slgzt.service.cmdb.CmdbReportService;
import com.datatech.slgzt.service.config.ConfigService;
import com.datatech.slgzt.service.order.ChangeWorkOrderTempSaveService;
import com.datatech.slgzt.service.producer.SmsProducer;
import com.datatech.slgzt.utils.*;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月31日 15:34:02
 */
@Service
@Slf4j
public class ChangeWorkOrderServiceImpl implements ChangeWorkOrderService {

    @Resource
    private UserService userService;

    @Resource
    private WorkOrderAuthLogManager workOrderAuthLogManager;

    @Resource
    private ResourceDetailManager resourceDetailManager;

    @Resource
    private ChangeWorkOrderProductManager productManager;

    @Resource
    private ChangeWorkOrderManager changeWorkOrderManager;

    @Resource
    private ChangeWorkOrderServiceConvert convert;

    @Resource
    private BaseActivity baseActivity;

    @Resource
    private CmdbReportService cmdbReportService;

    @Resource
    private ConfigService configService;

    @Resource
    private ChangeWorkOrderTempSaveService changeWorkOrderTempSaveService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private FlavorModelManager flavorModelManager;

    private final String CHANGE_ORDER_DRAFT = "change_order_draft:";


    @Resource
    private UserHelper userHelper;

    @Resource
    private SmsProducer smsProducer;

    @Resource
    private OrderDataProvideService orderDataProvideService;


    @Override
    public String createChangeWorkOrder(ChangeWorkOrderCreateOpm opm) {
        // 预先处理工单
        ChangeWorkOrderDTO orderDTO = preprocessingOrderData(opm);
        //创建各自的部分
        String orderId = orderDTO.getId();
        if (CollectionUtil.isEmpty(opm.getEcsPropertyList()) && CollectionUtil.isEmpty(opm.getGcsPropertyList())) {
            opm.setVariablesValue(2);
        }
        //----------------ecs-------------------------------------------
        createEcs(opm, "ecs", orderId);
        //创建ecs
        //----------------gsc-------------------------------------------
        createEcs(opm, "gcs", orderId);
        // ----------------evs------------------------------------------
        createEvs(opm, orderId);
        // ----------------slb------------------------------------------
        createSlb(opm, orderId);
        // ----------------nat------------------------------------------
        createNat(opm, orderId);
        // ----------------eip------------------------------------------
        createEip(opm, orderId);
        // ----------------obs------------------------------------------
        createObs(opm, orderId);
        // 执行工单流程并进行工单修改
        processChangeWorkOrderActivityAndUpdateOrder(orderDTO);
//        skipAlarmAndShutdown
//         0 - 存在属性变更，有ecs/gcs存在任意一个已交维 - 正常进入屏蔽告警环节
//         1 - 存在属性变更，有ecs/gcs都是未交维       - 直接进入云主机关机
//         2 - 没有ecs，gcs                         - 直接进入资源变更环节
        baseActivity.setVariable(orderDTO.getActivitiId(), "skipAlarmAndShutdown", opm.getVariablesValue());
        baseActivity.setVariable(orderDTO.getActivitiId(), "changeWorkOrderId", orderId);
        baseActivity.setVariable(orderDTO.getActivitiId(), "businessArchitecture", Boolean.TRUE);
        baseActivity.setVariable(orderDTO.getActivitiId(), "businessPlanning", Boolean.TRUE);
        baseActivity.setVariable(orderDTO.getActivitiId(), "cloudArchitecture", Boolean.TRUE);
        baseActivity.setVariable(orderDTO.getActivitiId(), "cloudResources", Boolean.TRUE);
        // draftDel(orderDTO.getCreatedBy(), orderId);
        changeWorkOrderTempSaveService.handleDelete(orderDTO.getCreatedBy());
        return orderId;
    }

    private void createEip(ChangeWorkOrderCreateOpm opm, String orderId) {
        List<String> lockDeviceIds = Lists.newArrayList();
        List<ChangeReqModel> changeReqModels = opm.getEipPropertyList();
        if (CollectionUtil.isNotEmpty(changeReqModels)) {
            changeReqModels.forEach(eip -> {
                ResourceDetailDTO resourceDetailDTO = resourceDetailManager.getById(eip.getResourceDetailId());
                //设置参数
                ChangeEipProductModel changeEipProductModel = new ChangeEipProductModel();
                changeEipProductModel.setResourceDetailId(eip.getResourceDetailId());
                changeEipProductModel.setEip(resourceDetailDTO.getEip());
                changeEipProductModel.setBandwidth(resourceDetailDTO.getBandWidth());
                changeEipProductModel.setEipId(resourceDetailDTO.getDeviceId());
                changeEipProductModel.setRelatedDeviceId(resourceDetailDTO.getRelatedDeviceId());
                changeEipProductModel.setRelatedDeviceName(resourceDetailDTO.getRelatedDeviceName());
                changeEipProductModel.setRelatedDeviceType(resourceDetailDTO.getRelatedDeviceType());
                changeEipProductModel.setEipName(resourceDetailDTO.getDeviceName());
                changeEipProductModel.setDomainCode(resourceDetailDTO.getDomainCode());
                changeEipProductModel.setDomainName(resourceDetailDTO.getDomainName());
                changeEipProductModel.setProductOrderId(IdUtil.getSnowflake().nextId());
                changeEipProductModel.setChangeType(eip.getChangeType());
                //--------------------eip设置部分------------------------------------------------------------
                if (changeEipProductModel.getChangeType().contains(ChangeTypeEnum.BANDWIDTH_EXPAND.getCode())) {
                    ChangeEipModel eipModel = new ChangeEipModel();
                    eipModel.setChangeType(eip.getChangeType());
                    eipModel.setProductOrderId(IdUtil.getSnowflake().nextId());
                    if (resourceDetailDTO.getBandWidth().contains("Mbps")) {
                        String bandWidth = resourceDetailDTO.getBandWidth().substring(0, resourceDetailDTO.getBandWidth().indexOf("Mbps"));
                        eipModel.setEipBandwidth(Integer.valueOf(bandWidth));
                    } else {
                        eipModel.setEipBandwidth(Integer.valueOf(resourceDetailDTO.getBandWidth()));
                    }
                    eipModel.setEipId(resourceDetailDTO.getEipId());
                    eipModel.setEip(resourceDetailDTO.getEip());
                    //只有更变属性有的时候才会设置
                    eipModel.setChangeBandwidth(eip.getEipBandwidth());
                    changeEipProductModel.setEipModel(eipModel);
                }
                //-------------------延期设置部分------------------------------------------------------------
                if (changeEipProductModel.getChangeType().contains(ChangeTypeEnum.DELAY.getCode())) {
                    changeEipProductModel.setChangeTime(eip.getChangeTime());
                    changeEipProductModel.setExpireTime(resourceDetailDTO.getExpireTime());
                }
                commonFillParam(changeEipProductModel, resourceDetailDTO, opm);
                //存入product表
                createProduct(changeEipProductModel, ProductTypeEnum.EIP.getCode(), orderId);
                lockDeviceIds.add(changeEipProductModel.getEipId());
            });
        }
        //------------创建完后如果lockResIds不为空则进行锁定操作----------------------
        if (CollectionUtil.isNotEmpty(lockDeviceIds)) {
            resourceDetailManager.updateChangeStatusByDeviceIds(lockDeviceIds, ChangeTypeResourceDetailStatusEnum.BE_CHANGING.getType());
        }
    }

    private void createObs(ChangeWorkOrderCreateOpm opm, String orderId) {
        List<String> lockDeviceIds = Lists.newArrayList();
        List<ChangeReqModel> changeReqModels = opm.getObsPropertyList();
        if (CollectionUtil.isNotEmpty(changeReqModels)) {
            changeReqModels.forEach(obs -> {
                //----------------------------------------obs设置部分------------------------------------------------------------
                ResourceDetailDTO resourceDetailDTO = resourceDetailManager.getById(obs.getResourceDetailId());
                //设置参数----先设置规格更变部分
                ChangeObsModel changeObsModel = new ChangeObsModel();
                changeObsModel.setResourceDetailId(obs.getResourceDetailId());
                changeObsModel.setProductOrderId(IdUtil.getSnowflake().nextId());
                changeObsModel.setCreateWorkOrderId(resourceDetailDTO.getOrderId());
                changeObsModel.setChangeType(obs.getChangeType());
                changeObsModel.setObsName(resourceDetailDTO.getDeviceName());
                changeObsModel.setDomainCode(resourceDetailDTO.getDomainCode());
                changeObsModel.setDomainName(resourceDetailDTO.getDomainName());
                changeObsModel.setObsSpec(resourceDetailDTO.getSpec());
                //-----------------------------------扩容-----------------------------------
                if (changeObsModel.getChangeType().contains(ChangeTypeEnum.STORAGE_EXPAND.getCode())) {
                    if (CollectionUtil.isNotEmpty(obs.getVolumeChangeReqModels())) {
                        changeObsModel.setChangeVolumeSize(obs.getVolumeChangeReqModels().get(0).getVolumeSize());
                    }
                }
                //-----------------------------------延期-----------------------------------
                if (changeObsModel.getChangeType().contains(ChangeTypeEnum.DELAY.getCode())) {
                    changeObsModel.setChangeTime(obs.getChangeTime());
                    changeObsModel.setExpireTime(resourceDetailDTO.getExpireTime());
                }
                commonFillParam(changeObsModel, resourceDetailDTO, opm);
                //存入product表
                createProduct(changeObsModel, ProductTypeEnum.OBS.getCode(), orderId);
            });
        }
        //------------创建完后如果lockResIds不为空则进行锁定操作----------------------
        if (CollectionUtil.isNotEmpty(lockDeviceIds)) {
            resourceDetailManager.updateChangeStatusByDeviceIds(lockDeviceIds, ChangeTypeResourceDetailStatusEnum.BE_CHANGING.getType());
        }
    }

    private void createNat(ChangeWorkOrderCreateOpm opm, String orderId) {
        List<String> lockDeviceIds = Lists.newArrayList();
        List<ChangeReqModel> changeReqModels = opm.getNatPropertyList();
        if (CollectionUtil.isNotEmpty(changeReqModels)) {
            changeReqModels.forEach(nat -> {
                //----------------------------------------nat设置部分------------------------------------------------------------
                ResourceDetailDTO resourceDetailDTO = resourceDetailManager.getById(nat.getResourceDetailId());
                //设置参数----先设置规格更变部分
                ChangeNatModel changeNatModel = new ChangeNatModel();
                changeNatModel.setSpec(resourceDetailDTO.getSpec());
                changeNatModel.setEip(resourceDetailDTO.getEip());
                changeNatModel.setBandwidth(resourceDetailDTO.getBandWidth());
                changeNatModel.setNatName(resourceDetailDTO.getDeviceName());
                changeNatModel.setResourceDetailId(nat.getResourceDetailId());
                changeNatModel.setProductOrderId(IdUtil.getSnowflake().nextId());
                changeNatModel.setCreateWorkOrderId(resourceDetailDTO.getOrderId());
                changeNatModel.setChangeType(nat.getChangeType());
                changeNatModel.setDomainCode(resourceDetailDTO.getDomainCode());
                changeNatModel.setDomainName(resourceDetailDTO.getDomainName());
                //-----------------------------------变更规格名称和类型-----------------------------------
                if (changeNatModel.getChangeType().contains(ChangeTypeEnum.INSTANCE_SPEC_CHANGE.getCode())) {
                    //规格id在校验的时候被设置
                    changeNatModel.setChangeFlavorName(nat.getFlavorName());
                    changeNatModel.setChangeFlavorType(nat.getFlavorType());
                    changeNatModel.setNatSpec(resourceDetailDTO.getSpec());
                }
                //--------------------eip设置部分------------------------------------------------------------
                if (changeNatModel.getChangeType().contains(ChangeTypeEnum.BANDWIDTH_EXPAND.getCode())) {
                    ResourceDetailDTO eipDTO = resourceDetailManager.getByDeviceId(resourceDetailDTO.getEipId());
                    Precondition.checkArgument(eipDTO, String.format("变更时带宽变更的eipId：%s在资源表中不存在", resourceDetailDTO.getEipId()));
                    ChangeEipModel eipModel = new ChangeEipModel();
                    eipModel.setChangeType(nat.getChangeType());
                    eipModel.setProductOrderId(IdUtil.getSnowflake().nextId());
                    if (resourceDetailDTO.getBandWidth().contains("Mbps")) {
                        String bandWidth = resourceDetailDTO.getBandWidth().substring(0, resourceDetailDTO.getBandWidth().indexOf("Mbps"));
                        eipModel.setEipBandwidth(Integer.valueOf(bandWidth));
                    } else {
                        eipModel.setEipBandwidth(Integer.valueOf(resourceDetailDTO.getBandWidth()));
                    }
                    eipModel.setEipId(resourceDetailDTO.getEipId());
                    eipModel.setEip(resourceDetailDTO.getEip());
                    eipModel.setResourceDetailId(eipDTO.getId());
                    changeNatModel.setEipModel(eipModel);
                }
                //-------------------延期设置部分------------------------------------------------------------
                if (changeNatModel.getChangeType().contains(ChangeTypeEnum.DELAY.getCode())) {
                    changeNatModel.setChangeTime(nat.getChangeTime());
                    changeNatModel.setExpireTime(resourceDetailDTO.getExpireTime());
                }
                commonFillParam(changeNatModel, resourceDetailDTO, opm);
                //存入product表
                createProduct(changeNatModel, ProductTypeEnum.NAT.getCode(), orderId);
                lockDeviceIds.add(changeNatModel.getNatId());
            });
        }
        //------------创建完后如果lockResIds不为空则进行锁定操作----------------------
        if (CollectionUtil.isNotEmpty(lockDeviceIds)) {
            resourceDetailManager.updateChangeStatusByDeviceIds(lockDeviceIds, ChangeTypeResourceDetailStatusEnum.BE_CHANGING.getType());
        }
    }

    private void createSlb(ChangeWorkOrderCreateOpm opm, String orderId) {
        List<String> lockDeviceIds = Lists.newArrayList();
        List<ChangeReqModel> changeReqModels = opm.getSlbPropertyList();
        if (CollectionUtil.isNotEmpty(changeReqModels)) {
            changeReqModels.forEach(slb -> {
                //----------------------------------------slb设置部分------------------------------------------------------------
                ResourceDetailDTO resourceDetailDTO = resourceDetailManager.getById(slb.getResourceDetailId());
                //设置参数----先设置规格更变部分
                ChangeSlbModel changeSlbModel = new ChangeSlbModel();
                changeSlbModel.setSpec(resourceDetailDTO.getSpec());
                changeSlbModel.setEip(resourceDetailDTO.getEip());
                changeSlbModel.setBandwidth(resourceDetailDTO.getBandWidth());
                changeSlbModel.setResourceDetailId(slb.getResourceDetailId());
                changeSlbModel.setProductOrderId(IdUtil.getSnowflake().nextId());
                changeSlbModel.setSlbId(resourceDetailDTO.getDeviceId());
                changeSlbModel.setCreateWorkOrderId(resourceDetailDTO.getOrderId());
                changeSlbModel.setChangeType(slb.getChangeType());
                changeSlbModel.setSlbName(resourceDetailDTO.getDeviceName());
                changeSlbModel.setDomainCode(resourceDetailDTO.getDomainCode());
                changeSlbModel.setDomainName(resourceDetailDTO.getDomainName());
                //-----------------------------------变更规格名称和类型-----------------------------------
                if (changeSlbModel.getChangeType().contains(ChangeTypeEnum.INSTANCE_SPEC_CHANGE.getCode())) {
                    //规格id在校验的时候被设置
                    changeSlbModel.setChangeFlavorName(slb.getFlavorName());
                    changeSlbModel.setChangeFlavorType(slb.getFlavorType());
                    changeSlbModel.setChangeFlavorId(slb.getFlavorId());
                    changeSlbModel.setSlbSpec(resourceDetailDTO.getSpec());
                }
                //--------------------eip设置部分------------------------------------------------------------
                if (changeSlbModel.getChangeType().contains(ChangeTypeEnum.BANDWIDTH_EXPAND.getCode())) {
                    ResourceDetailDTO eipDTO = resourceDetailManager.getByDeviceId(resourceDetailDTO.getEipId());
                    Precondition.checkArgument(eipDTO, String.format("变更时带宽变更的eipId：%s在资源表中不存在", resourceDetailDTO.getEipId()));
                    ChangeEipModel eipModel = new ChangeEipModel();
                    eipModel.setChangeType(slb.getChangeType());
                    eipModel.setProductOrderId(IdUtil.getSnowflake().nextId());
                    if (resourceDetailDTO.getBandWidth().contains("Mbps")) {
                        String bandWidth = resourceDetailDTO.getBandWidth().substring(0, resourceDetailDTO.getBandWidth().indexOf("Mbps"));
                        eipModel.setEipBandwidth(Integer.valueOf(bandWidth));
                    } else {
                        eipModel.setEipBandwidth(Integer.valueOf(resourceDetailDTO.getBandWidth()));
                    }
                    eipModel.setEipId(resourceDetailDTO.getEipId());
                    eipModel.setEip(resourceDetailDTO.getEip());
                    eipModel.setChangeBandwidth(slb.getEipBandwidth());
                    eipModel.setChangeTime(slb.getChangeTime());
                    eipModel.setResourceDetailId(eipDTO.getId());
                    changeSlbModel.setEipModel(eipModel);
                }
                //-------------------延期设置部分------------------------------------------------------------
                if (changeSlbModel.getChangeType().contains(ChangeTypeEnum.DELAY.getCode())) {
                    changeSlbModel.setChangeTime(slb.getChangeTime());
                    changeSlbModel.setExpireTime(resourceDetailDTO.getExpireTime());
                }
                commonFillParam(changeSlbModel, resourceDetailDTO, opm);
                //存入product表
                createProduct(changeSlbModel, ProductTypeEnum.SLB.getCode(), orderId);
                lockDeviceIds.add(changeSlbModel.getSlbId());
                //如果eip创建
                if (slb.getChangeType().contains(ChangeTypeEnum.BANDWIDTH_EXPAND.getCode())) {
                    createEipProduct(Collections.singletonList(changeSlbModel.getEipModel()), changeSlbModel.getProductOrderId(), orderId);
                    lockDeviceIds.add(changeSlbModel.getEipModel().getEipId());
                }
            });
        }
        //------------创建完后如果lockResIds不为空则进行锁定操作----------------------
        if (CollectionUtil.isNotEmpty(lockDeviceIds)) {
            resourceDetailManager.updateChangeStatusByDeviceIds(lockDeviceIds, ChangeTypeResourceDetailStatusEnum.BE_CHANGING.getType());
        }
    }

    private void createProduct(ChangeBaseModel model, String productType, String workOrderId) {
        ChangeWorkOrderProductDTO product = new ChangeWorkOrderProductDTO();
        product.setId(model.getProductOrderId());
        product.setCreateWorkOrderId(model.getCreateWorkOrderId());
        product.setProductType(productType);
        product.setWorkOrderId(workOrderId);
        product.setChangeType(model.getChangeType());
        product.setParentProductId(0L);
        product.setChangeStatus(ChangeTypeProductStatusEnum.WAIT_CHANGE.getCode());
        product.setGid(UuidUtil.getGid(productType));
        product.setSubOrderId(IdUtil.getSnowflake().nextId());
        product.setPropertySnapshot(JSON.toJSONString(model));
        product.setResourceDetailId(model.getResourceDetailId().toString());
        productManager.insert(product);
    }

    private void createEvs(ChangeWorkOrderCreateOpm opm, String id) {
        List<String> lockDeviceIds = Lists.newArrayList();
        List<ChangeReqModel> changeReqModels = opm.getEvsPropertyList();
        if (CollectionUtil.isNotEmpty(changeReqModels)) {
            changeReqModels.forEach(evs -> {
                ResourceDetailDTO resourceDetailDTO = resourceDetailManager.getById(evs.getResourceDetailId());
                //设置参数
                ChangeEvsModel changeEvsModel = new ChangeEvsModel();

                changeEvsModel.setResourceDetailId(evs.getResourceDetailId());
                changeEvsModel.setProductOrderId(IdUtil.getSnowflake().nextId());
                changeEvsModel.setChangeType(evs.getChangeType());
                changeEvsModel.setEvsId(resourceDetailDTO.getDeviceId());
                changeEvsModel.setVmId(resourceDetailDTO.getVmId());
                changeEvsModel.setVmName(resourceDetailDTO.getEcsName());
                changeEvsModel.setCreateWorkOrderId(resourceDetailDTO.getOrderId());
                changeEvsModel.setDomainCode(resourceDetailDTO.getDomainCode());
                changeEvsModel.setDomainName(resourceDetailDTO.getDomainName());
                changeEvsModel.setSpec(resourceDetailDTO.getDataDisk());
                if (evs.getChangeType().contains(ChangeTypeEnum.STORAGE_EXPAND.getCode())) {
                    changeEvsModel.setChangeVolumeSize(evs.getVolumeChangeReqModels().get(0).getVolumeSize());
                }
                if (evs.getChangeType().contains(ChangeTypeEnum.DELAY.getCode())) {
                    changeEvsModel.setChangeTime(evs.getChangeTime());
                }
                commonFillParam(changeEvsModel, resourceDetailDTO, opm);
                //存入product表
                ChangeWorkOrderProductDTO product = new ChangeWorkOrderProductDTO();
                product.setId(changeEvsModel.getProductOrderId());
                product.setProductType(ProductTypeEnum.EVS.getCode());
                product.setWorkOrderId(id);
                product.setChangeStatus(ChangeTypeProductStatusEnum.WAIT_CHANGE.getCode());
                product.setChangeType(changeEvsModel.getChangeType());
                product.setPropertySnapshot(JSON.toJSONString(changeEvsModel));
                product.setSubOrderId(IdUtil.getSnowflake().nextId());
                product.setParentProductId(0L);
                product.setResourceDetailId(evs.getResourceDetailId().toString());
                productManager.insert(product);
                lockDeviceIds.add(changeEvsModel.getEvsId());
            });
        }
        //------------创建完后如果lockResIds不为空则进行锁定操作----------------------
        if (CollectionUtil.isNotEmpty(lockDeviceIds)) {
            resourceDetailManager.updateChangeStatusByDeviceIds(lockDeviceIds, ChangeTypeResourceDetailStatusEnum.BE_CHANGING.getType());
        }
    }


    private void createEcs(ChangeWorkOrderCreateOpm opm, String productType, String id) {
        List<String> lockDeviceIds = Lists.newArrayList();
        List<ChangeReqModel> changeReqModels = Lists.newArrayList();
        if (productType.equals("ecs")) {
            changeReqModels = opm.getEcsPropertyList();
        }
        if (productType.equals("gcs")) {
            changeReqModels = opm.getGcsPropertyList();
        }
        if (CollectionUtil.isNotEmpty(changeReqModels)) {
            changeReqModels.forEach(model -> {
                //----------------------------------------ecs/gcs设置部分------------------------------------------------------------
                ResourceDetailDTO resourceDetailDTO = resourceDetailManager.getById(model.getResourceDetailId());
                //skipAlarmAndShutdown
                // 0 - 存在属性变更，有ecs/gcs存在任意一个已交维 - 正常进入屏蔽告警环节
                // 1 - 存在属性变更，有ecs/gcs都是未交维       - 直接进入云主机关机
                // 2 - 没有ecs，gcs                         - 直接进入资源变更环节
                // 250512需求更改：正常进入屏蔽告警环节 不需要，直接过滤；即skipAlarmAndShutdown永远不会设置成0
                if (Objects.equals(1, opm.getVariablesValue())) {
                    // 已经存在，一个有属性变更的了，不用再判断了
                }else{
                    boolean onlyDelay = model.getChangeType().size() == 1 && ChangeTypeEnum.DELAY.getCode().equals(model.getChangeType().get(0));
                    if (!onlyDelay) {
                        opm.setVariablesValue(1);
                    } else {
                        opm.setVariablesValue(2);
                    }
                }
                //设置参数----先设置规格更变部分
                ChangeEcsModel changeEcsModel = new ChangeEcsModel();
                changeEcsModel.setEip(resourceDetailDTO.getEip());
                changeEcsModel.setBandwidth(resourceDetailDTO.getBandWidth());
                changeEcsModel.setSpec(resourceDetailDTO.getSpec());
                changeEcsModel.setDataDisk(resourceDetailDTO.getDataDisk());
                changeEcsModel.setResourceDetailId(model.getResourceDetailId());
                changeEcsModel.setProductOrderId(IdUtil.getSnowflake().nextId());
                changeEcsModel.setCreateWorkOrderId(resourceDetailDTO.getOrderId());
                changeEcsModel.setChangeType(model.getChangeType());
                changeEcsModel.setVmId(resourceDetailDTO.getDeviceId());
                changeEcsModel.setVmName(resourceDetailDTO.getDeviceName());
                changeEcsModel.setDomainCode(resourceDetailDTO.getDomainCode());
                changeEcsModel.setDomainName(resourceDetailDTO.getDomainName());
                //规格id在校验的时候被设置
                if (model.getChangeType().contains(ChangeTypeEnum.INSTANCE_SPEC_CHANGE.getCode())) {
                    changeEcsModel.setChangeFlavorName(model.getFlavorName());
                    changeEcsModel.setChangeFlavorType(model.getFlavorType());
                    changeEcsModel.setChangeFlavorId(model.getFlavorId());
                    changeEcsModel.setVmSpec(resourceDetailDTO.getSpec());
                    changeEcsModel.setTemplateCode(model.getTemplateCode());
                }
                //--------------------eip设置部分------------------------------------------------------------
                if (model.getChangeType().contains(ChangeTypeEnum.BANDWIDTH_EXPAND.getCode())) {
                    ResourceDetailDTO eipDTO = resourceDetailManager.getByDeviceId(resourceDetailDTO.getEipId());
                    Precondition.checkArgument(eipDTO, String.format("变更时带宽变更的eipId：%s在资源表中不存在", resourceDetailDTO.getEipId()));
                    ChangeEipModel changeEipModel = new ChangeEipModel();
                    changeEipModel.setChangeType(model.getChangeType());
                    changeEipModel.setProductOrderId(IdUtil.getSnowflake().nextId());
                    if (resourceDetailDTO.getBandWidth().contains("Mbps")) {
                        String bandWidth = resourceDetailDTO.getBandWidth().substring(0, resourceDetailDTO.getBandWidth().indexOf("Mbps"));
                        changeEipModel.setEipBandwidth(Integer.valueOf(bandWidth));
                    } else {
                        changeEipModel.setEipBandwidth(Integer.valueOf(resourceDetailDTO.getBandWidth()));
                    }

                    changeEipModel.setEipId(resourceDetailDTO.getEipId());
                    changeEipModel.setEip(resourceDetailDTO.getEip());
                    changeEipModel.setResourceDetailId(eipDTO.getId());
                    //只有更变属性有的时候才会设置
                    changeEipModel.setChangeBandwidth(model.getEipBandwidth());
                    changeEcsModel.setEipModel(changeEipModel);
                }
                //-------------------evs设置部分------------------------------------------------------------
                if (model.getChangeType().contains(ChangeTypeEnum.STORAGE_EXPAND.getCode())) {
                    String volumeId = resourceDetailDTO.getVolumeId();
                    String dataDisk = resourceDetailDTO.getDataDisk();
                    //volumeId 和 dataDisk 都是逗号分隔的字符串,顺序是一样的，要保持和前端传过来的顺序一样
                    List<String> volumeIdList = Arrays.asList(volumeId.split(","));
                    List<String> dataDiskList = Arrays.asList(dataDisk.split(","));
                    // 创建一个映射关系以便快速查找
                    Map<String, String> volumeIdToDataDiskMap = new HashMap<>();
                    for (int i = 0; i < volumeIdList.size(); i++) {
                        // 假设两个列表长度相同且顺序对应
                        volumeIdToDataDiskMap.put(volumeIdList.get(i), dataDiskList.get(i));
                    }
                    List<ChangeReqModel.VolumeChangeReqModel> volumeChangeReqModels = model.getVolumeChangeReqModels();
                    Map<String, ChangeReqModel.VolumeChangeReqModel> reqModelMap = StreamUtils.toMap(volumeChangeReqModels, ChangeReqModel.VolumeChangeReqModel::getId);
                    List<ChangeEvsModel> changeEvsModels = Lists.newArrayList();
                    volumeIdList.forEach(volume -> {
                        ResourceDetailDTO evsDTO = resourceDetailManager.getByDeviceId(volume);
                        Precondition.checkArgument(evsDTO, String.format("变更时存储扩容的volumeId：%s在资源表中不存在", volume));
                        ChangeEvsModel changeEvsModel = new ChangeEvsModel();
                        changeEvsModel.setResourceDetailId(evsDTO.getId());
                        changeEvsModel.setChangeType(model.getChangeType());
                        changeEvsModel.setProductOrderId(IdUtil.getSnowflake().nextId());
                        changeEvsModel.setEvsId(volume);
                        changeEvsModel.setSpec(volumeIdToDataDiskMap.get(volume));
                        changeEvsModel.setChangeVolumeSize(reqModelMap.getOrDefault(volume, new ChangeReqModel.VolumeChangeReqModel()).getVolumeSize());
                        changeEvsModels.add(changeEvsModel);
                    });
                    changeEcsModel.setEvsModelList(changeEvsModels);
                }
                //如果包含延期
                if (model.getChangeType().contains(ChangeTypeEnum.DELAY.getCode())) {
                    changeEcsModel.setChangeTime(model.getChangeTime());
                }
                commonFillParam(changeEcsModel, resourceDetailDTO, opm);
                //存入product表
                createProduct(changeEcsModel, productType, id);
                lockDeviceIds.add(changeEcsModel.getVmId());
                //如果evs创建
                if (model.getChangeType().contains(ChangeTypeEnum.STORAGE_EXPAND.getCode())) {
                    createEvsProduct(changeEcsModel.getEvsModelList(), changeEcsModel.getProductOrderId(), id);
                    //整理出所有的EVSID
                    lockDeviceIds.addAll(StreamUtils.mapArray(changeEcsModel.getEvsModelList(), ChangeEvsModel::getEvsId));
                }
                //如果eip创建
                if (model.getChangeType().contains(ChangeTypeEnum.BANDWIDTH_EXPAND.getCode())) {
                    createEipProduct(Collections.singletonList(changeEcsModel.getEipModel()), changeEcsModel.getProductOrderId(), id);
                    lockDeviceIds.add(changeEcsModel.getEipModel().getEipId());
                }
            });
        }
        //------------创建完后如果lockResIds不为空则进行锁定操作----------------------
        if (CollectionUtil.isNotEmpty(lockDeviceIds)) {
            resourceDetailManager.updateChangeStatusByDeviceIds(lockDeviceIds, ChangeTypeResourceDetailStatusEnum.BE_CHANGING.getType());
        }
    }

    private void createEvsProduct(List<ChangeEvsModel> models, Long parentId, String workOrderId) {
        if (ObjNullUtils.isNull(models)) return;

        models.stream().map(model -> {
            ChangeWorkOrderProductDTO product = new ChangeWorkOrderProductDTO();
            product.setResourceDetailId(model.getResourceDetailId().toString());
            product.setId(model.getProductOrderId());
            product.setProductType(ProductTypeEnum.EVS.getCode());
            product.setWorkOrderId(workOrderId);
            product.setChangeStatus(ChangeTypeProductStatusEnum.WAIT_CHANGE.getCode());
            product.setChangeType(model.getChangeType());
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            product.setPropertySnapshot(JSON.toJSONString(model));
            product.setParentProductId(parentId);
            return product;
        }).forEach(productManager::insert);
    }

    private void createEipProduct(List<ChangeEipModel> models, Long parentId, String workOrderId) {
        if (ObjNullUtils.isNull(models)) return;
        models.stream().map(model -> {
            ChangeWorkOrderProductDTO product = new ChangeWorkOrderProductDTO();
            product.setResourceDetailId(model.getResourceDetailId().toString());
            product.setId(model.getProductOrderId());
            product.setProductType(ProductTypeEnum.EIP.getCode());
            product.setWorkOrderId(workOrderId);
            product.setChangeStatus(ChangeTypeProductStatusEnum.WAIT_CHANGE.getCode());
            product.setChangeType(model.getChangeType());
            product.setPropertySnapshot(JSON.toJSONString(model));
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            product.setParentProductId(parentId);
            return product;
        }).forEach(productManager::insert);
    }

    private void commonFillParam(ChangeBaseModel model, ResourceDetailDTO resourceDetailDTO, ChangeWorkOrderCreateOpm opm) {
        model.setRegionCode(resourceDetailDTO.getResourcePoolCode());
        model.setRegionId(resourceDetailDTO.getResourcePoolId());
        model.setRegionName(resourceDetailDTO.getResourcePoolName());
        model.setAzCode(resourceDetailDTO.getAzCode());
        model.setAzId(resourceDetailDTO.getAzId());
        model.setAzName(resourceDetailDTO.getAzName());
        model.setBillId(opm.getBillId());
        model.setCustomNo(opm.getCustomNo());
        model.setTenantId(opm.getTenantId());
        model.setTenantName(opm.getTenantName());
        model.setBusinessSystemId(opm.getBusinessSystemId());
        model.setExpireTime(resourceDetailDTO.getExpireTime());
        model.setResourceId(resourceDetailDTO.getDeviceId());
    }

    @Override
    public PageResult<ChangeWorkOrderDTO> page(ChangeWorkOrderQuery query, Long userId) {
        if (QueryParamCheckUtil.containsPercentage(query)) {
            return new PageResult<>();
        }

        // 判断查询的是待审批 还是已审批 还是驳回的工单
        UserCenterUserDTO user = userService.getUserById(userId);
        List<String> ruleCodeList = UserServiceExt.processRoleData_(user.getOacRoles());
        switch (query.getApprovalCode()) {
            case "pending":
                // 订单创建者是自己，并且assignee是creator_and_开头
                query.setCreator(userId.toString());
                //关联的运行时任务表 需要当前用户的角色code和用户id
                query.setRoleCodeList(ruleCodeList);
                query.setUserId(userId.toString());
                query.setApprovalCode("pending");
                break;
            case "approved":
                // 订单创建者是自己，并且assignee是creator_and_开头
                query.setCreator(userId.toString());
                query.setUserId(userId.toString());
                query.setApprovalCode("approved");
                query.setRoleCodeList(ruleCodeList);
                break;
            case "rejected":
                query.setUserId(userId.toString());
                query.setApprovalCode("rejected");
                query.setRoleCodeList(ruleCodeList);
                // 需要查询用户历史里有过审批拒绝相关的流程id
                List<String> processIds =
                        workOrderAuthLogManager.groupByProcessInstanceId(new WorkOrderAuthLogQuery().setUserId(userId).setAuditResult(OrderStatusEnum.REJECT.getCode()));
                //如果processIds为空则直接返回空直接返回空
                if (CollectionUtil.isEmpty(processIds)) {
                    return new PageResult<>();
                }
                query.setProcessIds(processIds);
                break;
            default:
                break;
        }

        return changeWorkOrderManager.page(query);
    }


    @Override
    public String draft(ChangeWorkOrderCreateOpm opm) {
        ChangeWorkOrderDTO orderDTO = preprocessingOrderData(opm);
        processChangeWorkOrderActivityAndUpdateOrder(orderDTO);
        draftSave(orderDTO);
        return orderDTO.getId();
    }

    @Override
    public void checkAndSendSMS(Long resourceDetailId) {
        List<String> workOrderIds = productManager.selectWorkOrderIdsByResourceDetailId(resourceDetailId,
                ActivitiStatusEnum.RESOURCE_CHANGE.getNode());
        if (CollectionUtils.isEmpty(workOrderIds)) {
            return;
        }
        if (workOrderIds.size() > 1) {
            log.warn("duplicate workOrderIds of resourceDetailId:{}", resourceDetailId);
            return;
        }

        String changeWorkOrderId = workOrderIds.get(0);
        RBucket<String> bucket = redissonClient.getBucket(changeWorkOrderId + "SmsSent");
        String smsSent = bucket.get();
        if ("true".equals(smsSent)) {
            log.debug("sms has been sent, change work id:{}", changeWorkOrderId);
            return;
        }
        ChangeWorkOrderDTO changeWorkOrder = changeWorkOrderManager.getById(changeWorkOrderId);
        if (changeWorkOrder == null) {
            log.warn("cannot find changed work order by id:{}", changeWorkOrderId);
            return;
        }
        List<ChangeWorkOrderProductDTO> productDTOs = productManager.listByWorkOrderId(changeWorkOrderId);
        boolean allValid = true;
        boolean allSuccess = true;
        log.debug("checkAndSendSMS productDTOs:{}", productDTOs);
        for (ChangeWorkOrderProductDTO productDTO : productDTOs) {
            ResourceDetailDTO resourceDTO = resourceDetailManager.getById(Long.valueOf(productDTO.getResourceDetailId()));
            // 检查条件1：变更状态必须是CHANGE_SUCCESS
            // change工单，不会在layoutTaskNotify中更新detail表，只会在工单结束时更新，修改成根据product判断
            if (productDTO.getChangeStatus().equals(ChangeTypeProductStatusEnum.CHANGE_FAIL.getCode())) {
                allSuccess = false;
            } else if (!productDTO.getChangeStatus().equals(ChangeTypeProductStatusEnum.CHANGE_SUCCESS.getCode())) {
                allValid = false;
                break;
            }
            // 检查条件2：ECS/GCS类型时需要检查设备状态
            if (resourceDTO.getType().equals(ProductTypeEnum.ECS.getCode())
                    || resourceDTO.getType().equals(ProductTypeEnum.GCS.getCode())) {
                if (!"RUNING".equals(resourceDTO.getDeviceStatus())) {
                    allValid = false;
                    break;
                }
            }
        }
        if (allValid) {
            SmsSendModel smsSendModel = new SmsSendModel();
            smsSendModel.setRoles(Collections.singletonList(AuthorityCodeEnum.OPERATION_GROUP.code()));
            smsSendModel.setOrderType(allSuccess ? "change_success" : "change_fail");
            smsSendModel.setOrderCode(changeWorkOrder.getOrderCode());
            smsSent = bucket.get();
            if ("true".equals(smsSent)) {
                log.debug("sms has been sent, change work id:{}", changeWorkOrderId);
                return;
            }
            smsProducer.sendMessage(changeWorkOrderId, smsSendModel);
            RBucket<Boolean> setBucket = redissonClient.getBucket(changeWorkOrderId + "SmsSent");
            setBucket.set(true, 120, TimeUnit.SECONDS);
        }

    }

    public void draftSave(ChangeWorkOrderDTO dto) {
        String id = dto.getId();
        RBucket<String> bucket = redissonClient.getBucket(CHANGE_ORDER_DRAFT + dto.getCreatedBy() + ":" + id);
        bucket.set(JSON.toJSONString(dto), 30, TimeUnit.DAYS);
    }

    public void draftDel(Long userId, String orderId) {
        Precondition.checkArgument(userId, "userId不能为空");
        RBucket<String> bucket = redissonClient.getBucket(CHANGE_ORDER_DRAFT + userId + ":" + orderId);
        bucket.delete();
    }

    /**
     * 获取草稿
     */
    @Override
    public ChangeWorkOrderDTO getDraft(Long userId, String orderId) {
        Precondition.checkArgument(userId, "userId不能为空");
        RBucket<String> bucket = redissonClient.getBucket(CHANGE_ORDER_DRAFT + userId + ":" + orderId);
        String json = bucket.get();
        return JSON.parseObject(json, ChangeWorkOrderDTO.class);
    }

    @Override
    public void checkDraftChangeOrderCanCreate(ChangeWorkOrderCreateOpm opm) {
        List<ChangeReqModel> evsList = opm.getEvsPropertyList();
        List<Long> evsResourceIds = evsList.stream()
                .map(ChangeReqModel::getResourceDetailId)
                .distinct()
                .collect(Collectors.toList());
        //eipId
        List<Long> eipResourceIds = opm.getEipPropertyList().stream()
                .map(ChangeReqModel::getResourceDetailId)
                .distinct()
                .collect(Collectors.toList());
        //ecsId
        List<Long> ecsResourceIds = opm.getEcsPropertyList().stream()
                .map(ChangeReqModel::getResourceDetailId)
                .distinct()
                .collect(Collectors.toList());
        //gcsId
        List<Long> gcsResourceIds = opm.getGcsPropertyList().stream()
                .map(ChangeReqModel::getResourceDetailId)
                .distinct()
                .collect(Collectors.toList());
        //slbId
        List<Long> slbResourceIds = opm.getSlbPropertyList().stream()
                .map(ChangeReqModel::getResourceDetailId)
                .distinct()
                .collect(Collectors.toList());
        //obsId
        List<Long> obsResourceIds = opm.getObsPropertyList().stream()
                .map(ChangeReqModel::getResourceDetailId)
                .distinct()
                .collect(Collectors.toList());
        //natId
        List<Long> natResourceIds = opm.getNatPropertyList().stream()
                .map(ChangeReqModel::getResourceDetailId)
                .distinct()
                .collect(Collectors.toList());
        List<Long> mergeList = StreamUtils.mergeList(evsResourceIds, eipResourceIds, ecsResourceIds, gcsResourceIds, slbResourceIds, obsResourceIds, natResourceIds);
        Precondition.checkArgument(CollectionUtil.isNotEmpty(mergeList), "变更时资源id不能为空");
        checkUnRecoveryOrUnChange(mergeList);
    }

    @Override
    public void checkChangeOrderCanCreate(ChangeWorkOrderCreateOpm opm) {
        List<ChangeReqModel> evsList = opm.getEvsPropertyList();
        List<Long> evsResourceIds = evsList.stream()
                .map(ChangeReqModel::getResourceDetailId)
                .distinct()
                .collect(Collectors.toList());
        //eipId
        List<Long> eipResourceIds = opm.getEipPropertyList().stream()
                .map(ChangeReqModel::getResourceDetailId)
                .distinct()
                .collect(Collectors.toList());
        //ecsId
        List<Long> ecsResourceIds = opm.getEcsPropertyList().stream()
                .map(ChangeReqModel::getResourceDetailId)
                .distinct()
                .collect(Collectors.toList());
        //gcsId
        List<Long> gcsResourceIds = opm.getGcsPropertyList().stream()
                .map(ChangeReqModel::getResourceDetailId)
                .distinct()
                .collect(Collectors.toList());
        //slbId
        List<Long> slbResourceIds = opm.getSlbPropertyList().stream()
                .map(ChangeReqModel::getResourceDetailId)
                .distinct()
                .collect(Collectors.toList());
        //obsId
        List<Long> obsResourceIds = opm.getObsPropertyList().stream()
                .map(ChangeReqModel::getResourceDetailId)
                .distinct()
                .collect(Collectors.toList());
        //natId
        List<Long> natResourceIds = opm.getNatPropertyList().stream()
                .map(ChangeReqModel::getResourceDetailId)
                .distinct()
                .collect(Collectors.toList());
        List<Long> mergeList = StreamUtils.mergeList(evsResourceIds, eipResourceIds, ecsResourceIds, gcsResourceIds, slbResourceIds, obsResourceIds, natResourceIds);
        Precondition.checkArgument(CollectionUtil.isNotEmpty(mergeList), "变更时资源id不能为空");
        if (StringUtils.isEmpty(opm.getId())) {
            checkUnRecoveryOrUnChange(mergeList);
        }
        //----------------------ecs校验----------------------------
        checkFillEcs(opm, evsResourceIds, eipResourceIds);
        //----------------------gcs校验------------------------------
        checkFillGcs(opm, evsResourceIds, eipResourceIds);
        //----------------------evs校验------------------------------
        checkFillEvs(opm);
        //----------------------slb校验------------------------------
        checkFillSlb(opm, eipResourceIds);
        //----------------------Eip校验------------------------------
        checkFillEip(opm);

    }

    @Override
    public ActivityTaskVo getTaskNodes(String orderId) {
        ChangeWorkOrderDTO orderDTO = changeWorkOrderManager.getById(orderId);
        Precondition.checkArgument(orderDTO, "工单不存在");
        ActivityTaskVo taskNode = baseActivity.taskNodes(orderDTO.getActivitiId(), ActivityEnum.ActivityProcessEnum.RESOURCE_CHANGE_PROCESS);
        if (taskNode != null) {
            String remark = com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum.getNodeRemarkByNode(taskNode.getCurrentTask(), null);
            taskNode.setCurrentTaskName(remark);
            List<InnerTask> tasks = taskNode.getAllTasks();
            tasks.forEach(task -> {
                String taskName = com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum.getNodeRemarkByNode(task.getTask(), null);
                task.setTaskName(taskName);
            });
        }
        return taskNode;
    }

    public static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Set<Object> seen = ConcurrentHashMap.newKeySet();
        return t -> seen.add(keyExtractor.apply(t));
    }

    @Override
    public void getNextTaskNodes(String orderId, List<WorkOrderAuthLogDTO> authLogDTOS, String currentTaskName) {
        ChangeWorkOrderDTO orderDTO = changeWorkOrderManager.getById(orderId);
        Precondition.checkArgument(orderDTO, "工单不存在");
        TaskNodeDTO nextTaskNode = baseActivity.nextTaskNodes(orderDTO.getActivitiId(), ActivityEnum.ActivityProcessEnum.RESOURCE_CHANGE_PROCESS);
        if (nextTaskNode == null) {
            return;
        }
        String assignee = nextTaskNode.getAssignee();
        if (assignee == null) {
            return;
        }
        String name = "";
        String email = "";
        if (assignee.matches("[-+]?\\d+(\\.\\d+)?")) {
            UserCenterUserDTO userCenterUserDTO = userService.getUserById(Long.valueOf(assignee));
            name = userCenterUserDTO.getUserName();
            email = userCenterUserDTO.getUserEmail();
        } else {
            List<UserCenterUserDTO> list;
            if (AuthorityCodeEnum.NETWORK_PROVISIONING.code().equals(assignee)
                    || AuthorityCodeEnum.RESOURCE_CREATION.code().equals(assignee)
                    || AuthorityCodeEnum.RESOURCE_CHANGE.code().equals(assignee)) {
                list = userService.getUserListByRoleCode(AuthorityCodeEnum.OPERATION_GROUP.code());
            } else if (AuthorityCodeEnum.SHUTDOWN.code().equals(assignee)) {
//                List<UserCenterUserDTO> list1 = userService.getUserListByRoleCode(AuthorityCodeEnum.PROFESSIONAL_GROUP.code());
                List<UserCenterUserDTO> list2 = userService.getUserListByRoleCode(AuthorityCodeEnum.OPERATION_GROUP.code());
//                UserCenterUserDTO userCenterUserDTO = userService.getUserById(orderDTO.getCreatedBy());
//                list = Stream.of(list1.stream(), list2.stream(), Stream.of(userCenterUserDTO))
//                        .flatMap(Function.identity())
//                        .filter(distinctByKey(UserCenterUserDTO::getId))
//                        .collect(Collectors.toList());
                list = list2;
            } else {
                list = userService.getUserListByRoleCode(assignee);
            }
            name = String.join(",", list.stream().map(UserCenterUserDTO::getUserName).collect(Collectors.toList()));
            email = String.join(",", list.stream().map(UserCenterUserDTO::getUserEmail).collect(Collectors.toList()));

        }
        WorkOrderAuthLogDTO workOrderAuthLogDTO = new WorkOrderAuthLogDTO();
        workOrderAuthLogDTO.setWorkOrderId(orderId);
        workOrderAuthLogDTO.setUserName(name);
        workOrderAuthLogDTO.setUserEmail(email);
        workOrderAuthLogDTO.setAuditNodeName(currentTaskName);
        workOrderAuthLogDTO.setAuditNodeCode(nextTaskNode.getBpmnName());
        workOrderAuthLogDTO.setAuditResultDesc("待审核");
        authLogDTOS.add(workOrderAuthLogDTO);
    }

    @Override
    public void alarmSuppress(List<Long> productIds) {
        productManager.updateBlockWarningByIds(productIds, true);
    }

    @Override
    public void tenantConfirm(List<Long> productIds) {
        productManager.updateTenantConfirmByIds(productIds, true);
    }

    @Override
    public ChangeAuditWorkOrderDTO getSchemaInfo(String workOrderId) {
        ChangeWorkOrderDTO orderDTO = changeWorkOrderManager.getById(workOrderId);
        Precondition.checkArgument(orderDTO, "当前工单不存在");

        ChangeAuditWorkOrderDTO dto = new ChangeAuditWorkOrderDTO();
        Object businessArchitecture = baseActivity.getVariable(orderDTO.getActivitiId(), "businessArchitecture");
        if (businessArchitecture != null) {
            dto.setBusinessArchitecture((Boolean) businessArchitecture);
        }
        Object businessPlanning = baseActivity.getVariable(orderDTO.getActivitiId(), "businessPlanning");
        if (businessPlanning != null) {
            dto.setBusinessPlanning((Boolean) businessPlanning);
        }
        Object cloudArchitecture = baseActivity.getVariable(orderDTO.getActivitiId(), "cloudArchitecture");
        if (cloudArchitecture != null) {
            dto.setCloudArchitecture((Boolean) cloudArchitecture);
        }
        Object cloudResources = baseActivity.getVariable(orderDTO.getActivitiId(), "cloudResources");
        if (cloudResources != null) {
            dto.setCloudResources((Boolean) cloudResources);
        }
        return dto;
    }

    @Override
    public void tryStartEcs(ResourceDetailDTO detailDTO) {
        if (detailDTO != null && "STOPED".equals(detailDTO.getDeviceStatus())
                && (ProductTypeEnum.ECS.getCode().equals(detailDTO.getType()) || ProductTypeEnum.GCS.getCode().equals(detailDTO.getType()))) {
            try {
                VmOperateQuery operate = new VmOperateQuery();
                operate.setId(detailDTO.getId());
                operate.setOperationType(VmOperationEnum.START.getAlias());
                operate.setOrderId(detailDTO.getOrderId());
                resourceDetailManager.operateVm(operate);
            } catch (Exception e) {
                log.warn("开启失败，detailDTO:{}, msg:{}", detailDTO, ExceptionUtils.getStackTrace(e));
            }
        }
    }


    private void checkFillEip(ChangeWorkOrderCreateOpm opm) {
        //获取Eip对象
        List<ChangeReqModel> eipList = opm.getEipPropertyList();
        eipList.forEach(eip -> {
            //判断是否包含bandwidth_change
            if (eip.getChangeType().contains(ChangeTypeEnum.BANDWIDTH_EXPAND.getCode())) {
                //校验带宽不能为空
                Precondition.checkArgument(eip.getEipBandwidth(), String.format("变更时带宽变更的eipId：%s在请求中不能为空", eip.getEipId()));
            }
        });
    }

    private void checkFillSlb(ChangeWorkOrderCreateOpm opm, List<Long> eipResourceIds) {
        //获取Slb对象
        List<ChangeReqModel> slbList = opm.getSlbPropertyList();
        slbList.forEach(slb -> {
            //判断是否包含storage_expand
            if (slb.getChangeType().contains(ChangeTypeEnum.INSTANCE_SPEC_CHANGE.getCode())) {
                ResourceDetailDTO resourceDetailDTO = resourceDetailManager.getById(slb.getResourceDetailId());
                //需要校验flavorId 并且设置flavorId
                FlavorQuery query = new FlavorQuery()
                        .setName(slb.getFlavorName())
                        .setRegionId(Long.valueOf(resourceDetailDTO.getResourcePoolId()));
                List<FlavorDTO> flavorDTOS = flavorModelManager.listFlavor(query);
                Precondition.checkArgument(flavorDTOS, String.format("变更时实例规格：%s在规格表中不存在", slb.getFlavorName()));
                //不能存在多个
                Precondition.checkArgument(flavorDTOS.size() == 1, String.format("变更时实例规格：%s在规格表中存在多个", slb.getFlavorName()));
                //设置规格id
                slb.setFlavorId(flavorDTOS.get(0).getId());
            }
            //判断是否包含bandwidth_change
            if (slb.getChangeType().contains(ChangeTypeEnum.BANDWIDTH_EXPAND.getCode())) {
                //获取eipId
                String eipId = slb.getEipId();
                //校验带宽不能为空
                Precondition.checkArgument(eipId, String.format("变更时带宽变更的eipId：%s在请求中不能为空", eipId));
                //校验带宽不能为空
                Precondition.checkArgument(slb.getEipBandwidth(), String.format("变更时带宽变更的eipId：%s在请求中不能为空", eipId));
                //校验是否存在
                ResourceDetailDTO eipDTO = resourceDetailManager.getByDeviceId(eipId);
                Precondition.checkArgument(eipDTO, String.format("变更时带宽变更的eipId：%s在资源表中不存在", eipId));
                //校验是否存在
                Precondition.checkArgument(!eipResourceIds.contains(eipDTO.getId()), String.format("变更时带宽变更的eipId：%s在SLB中已经存在", eipId));
            }
        });
    }


    private void checkFillEvs(ChangeWorkOrderCreateOpm opm) {
        //先获取ecs要变更的对象
        List<ChangeReqModel> evsList = opm.getEvsPropertyList();
        evsList.forEach(evs -> {
            //判断是否包含storage_expand
            if (evs.getChangeType().contains(ChangeTypeEnum.STORAGE_EXPAND.getCode())) {
                List<ChangeReqModel.VolumeChangeReqModel> volumeChangeReqModels = evs.getVolumeChangeReqModels();
                volumeChangeReqModels.forEach(i -> {
                    //获取volumeId不能为空
                    Precondition.checkArgument(evs.getResourceDetailId(), String.format("变更时存储扩容的volumeId：%s在请求中不能为空", evs.getResourceDetailId()));
                    //校验大小不能为空
                    Precondition.checkArgument(i.getVolumeSize(), String.format("变更时存储扩容的volumeId：%s在请求中不能为空", evs.getResourceDetailId()));
                });
            }
        });
    }

    private void checkFillEcs(ChangeWorkOrderCreateOpm opm, List<Long> evsResourceIds, List<Long> eipResourceIds) {
        //先获取ecs要变更的对象
        List<ChangeReqModel> ecsList = opm.getEcsPropertyList();
        ecsList.forEach(ecs -> {
            ResourceDetailDTO detailDTO = resourceDetailManager.getById(ecs.getResourceDetailId());
            //判断ChangeType是否包含instance_spec_change
            if (ecs.getChangeType().contains(ChangeTypeEnum.INSTANCE_SPEC_CHANGE.getCode())) {
                //需要校验flavorId 并且设置flavorId
                FlavorQuery query = new FlavorQuery()
                        .setName(ecs.getFlavorName())
                        .setRegionId(Long.valueOf(detailDTO.getResourcePoolId()));
                //如果是创新池
                if (CatalogueDomain.INNOVATION.getCode().equals(detailDTO.getDomainCode())) {
                    query.setAzId(Long.valueOf(detailDTO.getAzId()));
                    query.setTemplateCode(ecs.getTemplateCode());
                }
                List<FlavorDTO> flavorDTOS = flavorModelManager.listFlavor(query);
                Precondition.checkArgument(flavorDTOS, String.format("变更时实例规格：%s在规格表中不存在", ecs.getFlavorName()));
                //不能存在多个
                Precondition.checkArgument(flavorDTOS.size() == 1, String.format("变更时实例规格：%s在规格表中存在多个", ecs.getFlavorName()));
                //设置规格id
                ecs.setFlavorId(flavorDTOS.get(0).getId());
            }
            //判断是否包含storage_expand
            if (ecs.getChangeType().contains(ChangeTypeEnum.STORAGE_EXPAND.getCode())) {
                //需要校验volumeId 并且设置volumeId
                List<ChangeReqModel.VolumeChangeReqModel> volumeChangeReqModels = ecs.getVolumeChangeReqModels();
                //提交请求的时候需要传入volumeId 不能存在于 提交的evs产品列表里
                volumeChangeReqModels.forEach(evs -> {
                    //获取volumeId不能为空
                    Precondition.checkArgument(evs.getId(), String.format("变更时存储扩容的volumeId：%s在请求中不能为空", evs.getId()));
                    //校验大小不能为空
                    Precondition.checkArgument(evs.getVolumeSize(), String.format("变更时存储扩容的volumeId：%s在请求中不能为空", evs.getId()));
                    ResourceDetailDTO deviceDTO = resourceDetailManager.getByDeviceId(evs.getId());
                    Precondition.checkArgument(deviceDTO, String.format("变更时存储扩容的volumeId：%s在资源表中不存在", evs.getId()));
                    //不能在提交的evs列表里
                    Precondition.checkArgument(!evsResourceIds.contains(deviceDTO.getId()), String.format("变更时存储扩容的volumeId：%s在ECS中已经存在", evs.getId()));
                });
            }
            //判断是否包含bandwidth_change
            if (ecs.getChangeType().contains(ChangeTypeEnum.BANDWIDTH_EXPAND.getCode())) {
                //获取eipId
                String eipId = ecs.getEipId();
                //校验带宽不能为空
                Precondition.checkArgument(eipId, String.format("变更时带宽变更的eipId：%s在请求中不能为空", eipId));
                //校验带宽不能为空
                Precondition.checkArgument(ecs.getEipBandwidth(), String.format("变更时带宽变更的eipId：%s在请求中不能为空", eipId));
                ResourceDetailDTO eipDTO = resourceDetailManager.getByDeviceId(eipId);
                Precondition.checkArgument(eipDTO, String.format("变更时带宽变更的eipId：%s在资源表中不存在", eipId));
                Precondition.checkArgument(!eipResourceIds.contains(eipDTO.getId()), String.format("变更时带宽变更的eipId：%s在ECS中已经存在", eipId));
            }
        });
    }

    private void checkFillGcs(ChangeWorkOrderCreateOpm opm, List<Long> evsResourceIds, List<Long> eipResourceIds) {
        //先获取ecs要变更的对象
        List<ChangeReqModel> gcsList = opm.getGcsPropertyList();
        gcsList.forEach(ecs -> {
            ResourceDetailDTO detailDTO = resourceDetailManager.getById(ecs.getResourceDetailId());
            //判断ChangeType是否包含instance_spec_change
            if (ecs.getChangeType().contains(ChangeTypeEnum.INSTANCE_SPEC_CHANGE.getCode())) {
                //需要校验flavorId 并且设置flavorId
                FlavorQuery query = new FlavorQuery()
                        .setName(ecs.getFlavorName())
                        .setRegionId(Long.valueOf(detailDTO.getResourcePoolId()));
                //如果是创新池
                if (CatalogueDomain.INNOVATION.getCode().equals(detailDTO.getDomainCode())) {
                    query.setAzId(Long.valueOf(detailDTO.getAzId()));
                    query.setTemplateCode(ecs.getTemplateCode());
                }
                List<FlavorDTO> flavorDTOS = flavorModelManager.listFlavor(query);
                Precondition.checkArgument(flavorDTOS, String.format("变更时实例规格：%s在规格表中不存在", ecs.getFlavorName()));
                //不能存在多个
                Precondition.checkArgument(flavorDTOS.size() == 1, String.format("变更时实例规格：%s在规格表中存在多个", ecs.getFlavorName()));
                //设置规格id
                ecs.setFlavorId(flavorDTOS.get(0).getId());
            }
            //判断是否包含storage_expand
            if (ecs.getChangeType().contains(ChangeTypeEnum.STORAGE_EXPAND.getCode())) {
                //需要校验volumeId 并且设置volumeId
                List<ChangeReqModel.VolumeChangeReqModel> volumeChangeReqModels = ecs.getVolumeChangeReqModels();
                //提交请求的时候需要传入volumeId 不能存在于 提交的evs产品列表里
                volumeChangeReqModels.forEach(evs -> {
                    //获取volumeId不能为空
                    Precondition.checkArgument(evs.getId(), String.format("变更时存储扩容的volumeId：%s在请求中不能为空", evs.getId()));
                    //校验大小不能为空
                    Precondition.checkArgument(evs.getVolumeSize(), String.format("变更时存储扩容的volumeId：%s在请求中不能为空", evs.getId()));
                    ResourceDetailDTO deviceDTO = resourceDetailManager.getByDeviceId(evs.getId());
                    Precondition.checkArgument(deviceDTO, String.format("变更时存储扩容的volumeId：%s在资源表中不存在", evs.getId()));
                    //不能在提交的evs列表里
                    Precondition.checkArgument(!evsResourceIds.contains(deviceDTO.getId()), String.format("变更时存储扩容的volumeId：%s在ECS中已经存在", evs.getId()));
                });
            }
            //判断是否包含bandwidth_change
            if (ecs.getChangeType().contains(ChangeTypeEnum.BANDWIDTH_EXPAND.getCode())) {
                //获取eipId
                String eipId = ecs.getEipId();
                //校验带宽不能为空
                Precondition.checkArgument(eipId, String.format("变更时带宽变更的eipId：%s在请求中不能为空", eipId));
                //校验带宽不能为空
                Precondition.checkArgument(ecs.getEipBandwidth(), String.format("变更时带宽变更的eipId：%s在请求中不能为空", eipId));
                ResourceDetailDTO eipDTO = resourceDetailManager.getByDeviceId(eipId);
                Precondition.checkArgument(eipDTO, String.format("变更时带宽变更的eipId：%s在资源表中不存在", eipId));
                Precondition.checkArgument(!eipResourceIds.contains(eipDTO.getId()), String.format("变更时带宽变更的eipId：%s在ECS中已经存在", eipId));
            }
        });
    }


    private void checkUnRecoveryOrUnChange(List<Long> resourceIds) {
        ResourceDetailQuery query = new ResourceDetailQuery();
        query.setIds(resourceIds);
        List<ResourceDetailDTO> detailDTOS = resourceDetailManager.list(query);
        // 排除回收中的资源
        long unRecoveryCount = detailDTOS.stream().filter(item -> RecoveryStatusEnum.ORDER_TO_BE_RECOVERED.getType().equals(item.getRecoveryStatus())).count();
        Precondition.checkArgument(unRecoveryCount == resourceIds.size(), "变更产品中传入的资源id中存在回收中的资源，该次变更失败，请确认");
        // 排除变更中的资源
        long canChangeCount = detailDTOS.stream().filter(item -> ChangeTypeResourceDetailStatusEnum.UN_CHANGE.getType().equals(item.getChangeStatus())
                || ChangeTypeProductStatusEnum.CHANGE_SUCCESS.getCode().equals(item.getChangeStatus())).count();
        Precondition.checkArgument(canChangeCount == resourceIds.size(), "变更产品中传入的资源id中存在变更中的资源，该次变更失败，请确认");
        detailDTOS.forEach(resourceDetailDTO -> {
            boolean orderComplete = orderDataProvideService.isOrderComplete(resourceDetailDTO.getOrderId());
            Precondition.checkArgument(orderComplete, "当前资源存在未完成的工单 工单id为" + resourceDetailDTO.getOrderId());
        });
    }


    private void processChangeWorkOrderActivityAndUpdateOrder(ChangeWorkOrderDTO orderDTO) {
        String inputOrderId = orderDTO.getId();
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        Precondition.checkArgument(currentUser, "获取当前用户信息失败");
        orderDTO.setCreatedBy(currentUser.getId());
        orderDTO.setCreatedUserName(currentUser.getUserName());
        orderDTO.setCreateUserId(currentUser.getId());
        // 创建审批日志
        LocalDateTime now = LocalDateTime.now();
        workOrderAuthLogManager.createWorkOrderAuthLog(new WorkOrderAuthLogDTO()
                .setCreateTime(now)
                .setModifyTime(now)
                .setWorkOrderId(inputOrderId)
                .setProcessInstanceId(orderDTO.getActivitiId()).setAuditNodeCode(ActivitiStatusEnum.USER_TASK.getNode()).setAuditNodeName(com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum.USER_TASK.getNodeRemark()).setUserId(orderDTO.getCreatedBy()).setUserName(currentUser.getUserName()).setUserPhone(currentUser.getPhone()).setUserEmail(currentUser.getUserEmail()).setAuditResult(orderDTO.getAuditResult()));

        if (!orderDTO.getCanDraft()) {
            baseActivity.complete(orderDTO, ActivityEnum.ActivityStatusEnum.PASS, orderDTO.getActivitiId(), null, ActivityEnum.ActivityProcessEnum.RESOURCE_CHANGE_PROCESS, currentUser.getId(), null, null);
        }
        ActivityTaskVo taskVo = baseActivity.taskNodes(orderDTO.getActivitiId(), ActivityEnum.ActivityProcessEnum.RESOURCE_CHANGE_PROCESS);
        orderDTO.setCurrentNodeCode(taskVo.getCurrentTask());
        orderDTO.setCurrentNodeName(com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum.getNodeRemarkByNode(taskVo.getCurrentTask(), RecoveryOrderNodeEnum.EXAMINING.getCode()));
        orderDTO.setWorkOrderStartTime(now);
        orderDTO.setCurrentNodeStartTime(now);
        updateOrderStatus(orderDTO, orderDTO.getCreatedBy(), OrderStatusEnum.EXAMINING.getCode());
        // 提交后删除购物车中的暂存数据
        changeWorkOrderTempSaveService.handleDeleteAll(orderDTO.getCreatedBy());
    }

    private ChangeWorkOrderDTO preprocessingOrderData(ChangeWorkOrderCreateOpm opm) {
        ChangeWorkOrderDTO orderDTO = convert.convert(opm);
        if (StringUtils.isEmpty(opm.getId())) {
            //先创建id
            fillChangeOrderBaseData(orderDTO);
            changeWorkOrderManager.create(orderDTO);
            orderDTO.setCreatedBy(userHelper.getCurrentUser().getId());
            String activityId = baseActivity.instanceRun(orderDTO, ActivityEnum.ActivityProcessEnum.RESOURCE_CHANGE_PROCESS, AuthorityCodeEnum.USER_TASK.code(), null);
            orderDTO.setActivitiId(activityId);
            if (StringUtils.isEmpty(activityId)) {
                throw UniversalException.build(GlobalExceptionEnum.ACTIVITY_DEFINITION_IS_NOT_FOUND);
            }
        } else {
            // 重新提交删除工单对应的产品数据
            List<ChangeWorkOrderProductDTO> productDTOS = productManager.listByWorkOrderId(opm.getId());
            List<Long> ids = productDTOS.stream().map(productDTO -> Long.valueOf(productDTO.getResourceDetailId())).collect(Collectors.toList());
            resourceDetailManager.updateChangeStatusByIds(ids, ChangeTypeResourceDetailStatusEnum.UN_CHANGE.getType());
            productManager.deleteByWorkOrderId(opm.getId());
            orderDTO.setAuditResult(OrderLogStatusEnum.RESUBMIT.getCode());
            ChangeWorkOrderDTO order = changeWorkOrderManager.getById(opm.getId());
            orderDTO.setActivitiId(order.getActivitiId());
            // 填写工单编号，发短信时候需要
            orderDTO.setOrderCode(order.getOrderCode());
        }
        return orderDTO;
    }

    private void fillChangeOrderBaseData(ChangeWorkOrderDTO orderDTO) {
        orderDTO.setOrderCode(CodeUtil.getOrderCode(CodePrefixEnum.BG.getCode()));
        orderDTO.setOrderType(OrderTypeEnum.CHANGE.getCode());
        orderDTO.setOrderStatus(OrderStatusEnum.EXAMINING.getCode());
        orderDTO.setAuditResult(OrderLogStatusEnum.CREATE.getCode());
        orderDTO.setCreateTime(LocalDateTime.now());
        orderDTO.setActiviteKey(ActivityEnum.ActivityProcessEnum.RESOURCE_CHANGE_PROCESS.getCode());
    }


    @Override
    public AuditCountVo orderCount(ChangeWorkOrderQuery query) {
        Long userId = Long.valueOf(query.getUserId());
        query.setApprovalCode(ApprovalTypeEnum.TODO_TYPE.getType());
        PageResult<ChangeWorkOrderDTO> page = page(query, userId);
        Long todoSize = Optional.ofNullable(page.getTotal()).orElse(0L);
        query.setApprovalCode(ApprovalTypeEnum.DONE_TYPE.getType());
        page = page(query, userId);
        Long doneSize = Optional.ofNullable(page.getTotal()).orElse(0L);
        query.setApprovalCode(ApprovalTypeEnum.REJECT_TYPE.getType());
        page = page(query, userId);
        Long rejectSize = Optional.ofNullable(page.getTotal()).orElse(0L);
        return new AuditCountVo().setPendingCount(todoSize).setApprovedCount(doneSize).setRejectedCount(rejectSize);
    }

    @Override
    public void cancel(String workOrderId) {
        ChangeWorkOrderDTO orderDTO = changeWorkOrderManager.getById(workOrderId);
        Precondition.checkArgument(orderDTO, "当前工单不存在");
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        // 进行撤销工单权限校验
        preCheckCancelAuthority(orderDTO, currentUser);
        baseActivity.stop(orderDTO, currentUser.getId(), ActivityEnum.ActivityProcessEnum.RESOURCE_CHANGE_PROCESS);
        // 更新工单状态

        LocalDateTime now = LocalDateTime.now();
        workOrderAuthLogManager.createWorkOrderAuthLog(new WorkOrderAuthLogDTO().setCreateTime(now).setModifyTime(now).setWorkOrderId(workOrderId).setProcessInstanceId(orderDTO.getActivitiId()).setAdvice(ActivitiStatusEnum.ORDER_CANCEL.getNodeRemark()).setAuditNodeCode(ActivitiStatusEnum.ORDER_CANCEL.getNode()).setAuditNodeName(ActivitiStatusEnum.ORDER_CANCEL.getNodeRemark()).setUserId(orderDTO.getCreatedBy()).setUserName(currentUser.getUserName()).setUserPhone(currentUser.getPhone()).setUserEmail(currentUser.getUserEmail()).setAuditResult(ActivitiStatusEnum.ORDER_CANCEL.getNodeRemark()));

        orderDTO.setCurrentNodeCode(ActivitiStatusEnum.ORDER_CANCEL.getNode());
        orderDTO.setCurrentNodeName(ActivitiStatusEnum.ORDER_CANCEL.getNodeRemark());
        orderDTO.setWorkOrderEndTime(now);
        orderDTO.setCurrentNodeStartTime(now);
        updateOrderStatus(orderDTO, currentUser.getId(), OrderStatusEnum.CLOSE.getCode());
        // 工单撤销后将资源变更状态重置到初始化状态
        afterCancelUpdateData(workOrderId);
    }

    private void afterCancelUpdateData(String workOrderId) {
        List<ChangeWorkOrderProductDTO> orderProductDTOS = productManager.listByWorkOrderId(workOrderId);
        //取出统一的产品id
        List<String> resDeatils = StreamUtils.mapArrayFilterNull(orderProductDTOS, ChangeWorkOrderProductDTO::getResourceDetailId);
        List<Long> resIds = resDeatils.stream()
                .map(Long::valueOf)
                .collect(Collectors.toList());
        //如果resIds不为空 更新状态
        if (CollectionUtil.isNotEmpty(resIds)) {
            resourceDetailManager.updateChangeStatusByIds(resIds, ChangeTypeResourceDetailStatusEnum.UN_CHANGE.getType());
        }
    }

    @Override
    public void audit(ChangeAuditWorkOrderDTO auditDTO) {
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        Precondition.checkArgument(currentUser, "获取当前用户信息失败");
        ChangeWorkOrderDTO orderDTO = changeWorkOrderManager.getById(auditDTO.getOrderId());
        Precondition.checkArgument(orderDTO, "当前工单不存在");
        ActivityTaskVo taskVo = baseActivity.taskNodes(orderDTO.getActivitiId(), ActivityEnum.ActivityProcessEnum.RESOURCE_CHANGE_PROCESS);
        Precondition.checkArgument(taskVo, "已无审核流程节点！");
        orderDTO.setCurrentNodeCode(taskVo.getCurrentTask());

        if (auditDTO.getCloudLeaderId() != null && auditDTO.getSecondLevelLeaderId() != null) {
            orderDTO.setThreeLevelCloudLeaderId(auditDTO.getCloudLeaderId());
            baseActivity.setVariable(orderDTO.getActivitiId(), "cloud", auditDTO.getCloudLeaderId());
            try {
                Thread.sleep(200);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            orderDTO.setSecondLevelCloudLeaderId(auditDTO.getSecondLevelLeaderId());
            baseActivity.setVariable(orderDTO.getActivitiId(), "cloud_2", auditDTO.getSecondLevelLeaderId());
        }

        setSchemaInfo(orderDTO.getActivitiId(), auditDTO);

        // 审核状态
        ActivityEnum.ActivityProcessEnum activityProcessEnum = ActivityEnum.ActivityProcessEnum.getByCode(orderDTO.getActiviteKey());
        ConfigTypeEnum configTypeEnum = ConfigTypeEnum.findByActivityEnum(activityProcessEnum);
        OacConfig oldTaskCode = configService.getByCode(taskVo.getCurrentTask(), configTypeEnum.getCode(), null, null);
        ActivityEnum.ActivityStatusEnum auditStatus = ActivityEnum.ActivityStatusEnum.getByCode(auditDTO.getActiviteStatus());

        String orderStatus = null;
        orderDTO.setCurrentNodeStartTime(LocalDateTime.now());
        switch (auditStatus) {
            case PASS:
                log.debug("taskVo.getCurrentTask()：{}", taskVo.getCurrentTask());
                if (!ActivitiStatusEnum.TENANT_TASK.getNode().equals(taskVo.getCurrentTask())) {
                    orderStatus = OrderStatusEnum.EXAMINING.getCode();
                    insertAuthLog(taskVo.getCurrentTask(), orderDTO, auditDTO.getAuditAdvice(), OrderLogStatusEnum.PASS.getCode());
                } else {
                    //到这里已经是工单完成节点了
                    orderDTO.setWorkOrderEndTime(LocalDateTime.now());
                    insertAuthLog(taskVo.getCurrentTask(), orderDTO, auditDTO.getAuditAdvice(), OrderLogStatusEnum.PASS.getCode());
                    orderStatus = OrderStatusEnum.END.getCode();
                    log.info("cmdb start");
                    cmdbReportService.updateInstanceEcs(orderDTO.getId());
                    List<ChangeWorkOrderProductDTO> changeWorkOrderProductDTOS = productManager.listByWorkOrderId(orderDTO.getId());
                    for (ChangeWorkOrderProductDTO changeProductDTO : changeWorkOrderProductDTOS) {
                        // 交维状态存档
                        if (ProductTypeEnum.ECS.getCode().equals(changeProductDTO.getProductType()) || ProductTypeEnum.GCS.getCode().equals(changeProductDTO.getProductType())) {
                            ChangeWorkOrderProductDTO updateDto = new ChangeWorkOrderProductDTO();
                            updateDto.setId(changeProductDTO.getId());
                            ResourceDetailDTO detailDTO = resourceDetailManager.getById(Long.valueOf(changeProductDTO.getResourceDetailId()));
                            ChangeEcsModel changeEcsModel = JSONObject.parseObject(changeProductDTO.getPropertySnapshot(), ChangeEcsModel.class);
                            changeEcsModel.setHandoverStatus(detailDTO.getHandoverStatus());
                            updateDto.setPropertySnapshot(JSON.toJSONString(changeEcsModel));
                            productManager.update(updateDto);
                        }
                    }
                    List<Long> resourceDetailIds = changeWorkOrderProductDTOS.stream()
                            .map(ChangeWorkOrderProductDTO::getResourceDetailId)
                            .filter(Objects::nonNull)
                            .map(Long::parseLong)
                            .collect(Collectors.toList());
                    log.debug("resourceDetailIds:{}", resourceDetailIds);
                    resourceDetailManager.updateChangeStatusByIds(resourceDetailIds, ChangeTypeResourceDetailStatusEnum.UN_CHANGE.getType());
                }
                break;
            case REJECT:
                insertAuthLog(null, orderDTO, auditDTO.getAuditAdvice(), OrderLogStatusEnum.REJECT.getCode());
                orderStatus = OrderStatusEnum.findDescByAuditStatus(auditStatus).getCode();
                break;
            default:
        }
        baseActivity.complete(orderDTO, auditStatus, orderDTO.getActivitiId(), auditDTO.getAuditAdvice(), activityProcessEnum, currentUser.getId(), oldTaskCode,
                auditDTO.getNodeCode());

        taskVo = baseActivity.taskNodes(orderDTO.getActivitiId(), ActivityEnum.ActivityProcessEnum.RESOURCE_CHANGE_PROCESS);
        orderDTO.setCurrentNodeCode(taskVo.getCurrentTask());
        orderDTO.setCurrentNodeName(com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum.getNodeRemarkByNode(taskVo.getCurrentTask(), null));
        orderDTO.setThreeLevelCloudLeaderId(auditDTO.getCloudLeaderId());
        orderDTO.setSecondLevelCloudLeaderId(auditDTO.getSecondLevelLeaderId());
        updateOrderStatus(orderDTO, currentUser.getId(), orderStatus);
    }

    private void setSchemaInfo(String activitiId, ChangeAuditWorkOrderDTO auditDTO) {
        if (auditDTO.getBusinessArchitecture() != null) {
            baseActivity.setVariable(activitiId, "businessArchitecture", auditDTO.getBusinessArchitecture());
        }
        if (auditDTO.getBusinessPlanning() != null) {
            baseActivity.setVariable(activitiId, "businessPlanning", auditDTO.getBusinessPlanning());
        }
        if (auditDTO.getCloudArchitecture() != null) {
            baseActivity.setVariable(activitiId, "cloudArchitecture", auditDTO.getCloudArchitecture());
        }
        if (auditDTO.getCloudResources() != null) {
            baseActivity.setVariable(activitiId, "cloudResources", auditDTO.getCloudResources());
        }
    }

    private void updateOrderStatus(ChangeWorkOrderDTO orderDTO, Long userId, String orderStatus) {
        //1. 修改操作订单
        orderDTO.setOrderStatus(orderStatus);
        orderDTO.setModifyTime(LocalDateTime.now());
        orderDTO.setUpdatedBy(userId);
        changeWorkOrderManager.update(orderDTO);
    }

    private void preCheckCancelAuthority(ChangeWorkOrderDTO orderDTO, UserCenterUserDTO currentUser) {
        Precondition.checkArgument(currentUser, "获取当前用户信息失败");
        //判断当前工单是不是当前用户创建不然不能撤销
        Precondition.checkArgument(currentUser.getId().equals(orderDTO.getCreatedBy()), "当前用户不是工单创建人，不能撤销");
        // 获取工单当前审核节点
        ActivityTaskVo taskVo = baseActivity.taskNodes(orderDTO.getActivitiId(), ActivityEnum.ActivityProcessEnum.RESOURCE_CHANGE_PROCESS);
        Precondition.checkArgument(taskVo.getCurrentTask() != null, "已无审核流程节点！");
        List<String> codeList = Arrays.asList(
                com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum.ALARM_SUPPRESSION.getNode(),
                com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum.SHUTDOWN.getNode(),
                com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum.RESOURCE_CHANGE.getNode(),
                com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum.TENANT_TASK.getNode());
        Precondition.checkArgument(!codeList.contains(taskVo.getCurrentTask()), "当前节点不允许撤销");
    }

    private void insertAuthLog(String currentTask, ChangeWorkOrderDTO orderDTO, String auditAdvice, String auditResult) {
        UserCenterUserDTO currentUser = UserHelper.INSTANCE.getCurrentUser();
        String orderStatus = currentTask;
        if (StringUtils.isEmpty(currentTask)) {
            if (OrderLogStatusEnum.REJECT.getCode().equalsIgnoreCase(auditResult)) {
                orderStatus = RecoveryOrderNodeEnum.REJECT.getCode();
            } else {
                orderStatus = RecoveryOrderNodeEnum.END.getCode();
            }
        }

        WorkOrderAuthLogDTO logDTO =
                new WorkOrderAuthLogDTO().setCreateTime(orderDTO.getCurrentNodeStartTime()).setModifyTime(orderDTO.getCurrentNodeStartTime()).setWorkOrderId(orderDTO.getId()).setProcessInstanceId(orderDTO.getActivitiId()).setUserId(currentUser.getId()).setUserName(currentUser.getUserName()).setUserPhone(currentUser.getPhone()).setUserEmail(currentUser.getUserEmail()).setAdvice(auditAdvice).setAuditNodeCode(currentTask).setAuditResult(auditResult).setAuditResultDesc(OrderLogStatusEnum.getByCode(auditResult).getDesc()).setAuditNodeName(com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum.getNodeRemarkByNode(currentTask, orderStatus));
        workOrderAuthLogManager.createWorkOrderAuthLog(logDTO);
    }
}

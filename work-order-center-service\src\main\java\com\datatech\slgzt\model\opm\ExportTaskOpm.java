package com.datatech.slgzt.model.opm;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 05月26日 14:59:33
 */
@Data
public class ExportTaskOpm {
    /**
     * 报表类型
     */
    private String reportType;

    /**
     * 资源池id
     */
    private Long regionId;

    /**
     * 统计类型（HOUR/DAY/MONTH）
     */
    private String statType;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 导出字段
     */
    private List<String> exportFields;

}

package com.datatech.slgzt.model.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月06日 17:32:05
 */
@Data
public class StandardWorkOrderProductDTO {

    private Long id;

    //工单Id
    private String workOrderId;

    /**
     * 产品类型
     * GoodsTypeEnum
     */
    private String productType;


    //属性快照
    private String propertySnapshot;

    //父类产品id 可以为空
    private Long parentProductId;

    //开通状态
    private String openStatus;

    //消息
    private String message;

    private String gid;

    private String ext;
    private Long subOrderId;

    //Job执行ID
    private Long jobExecutionId;


}

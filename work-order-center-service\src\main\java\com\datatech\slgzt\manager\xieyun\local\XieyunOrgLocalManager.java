package com.datatech.slgzt.manager.xieyun.local;

import java.util.List;

import com.datatech.slgzt.model.dto.XieYunOrgDTO;
import com.datatech.slgzt.model.query.container.XieYunOrgQuery;

/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/4/15
 */
public interface XieyunOrgLocalManager {

    void insert(XieYunOrgDTO xieYunOrgDTO);

    void updateUserIdByOrgId(String orgId, String userId);

    void updateOrgQuotaByOrgId(XieYunOrgDTO orgDTO);

    List<XieYunOrgDTO> list(XieYunOrgQuery query);
}

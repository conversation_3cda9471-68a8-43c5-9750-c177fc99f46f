<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.datatech.slgzt.dao.mapper.WocSecurityGroupMapper">

    <select id="selectWocSecurities" resultType="com.datatech.slgzt.dao.model.security.WocSecurityGroupDO">
        SELECT a.ID, a.NAME, a.REGION_NAME, b.VPC_NAME, c.SYSTEM_NAME, a.CREATED_TIME, a.DESCRIPTION, a.RULE_SNAPSHOT
        FROM WOC_SECURITY_GROUP a
        INNER JOIN WOC_VPC_ORDER b ON a.VPC_ID = b.ID
        INNER JOIN CMP_APP_T c ON a.BUSINESS_SYSTEM_ID = c.ID
        WHERE a.STATUS = 1
        <if test="query.name != null and query.name != ''">
            AND a.NAME LIKE CONCAT(CONCAT('%',#{query.name}),'%')
        </if>
        <if test="query.regionId != null">
            AND a.REGION_ID = #{query.regionId}
        </if>
        <if test="query.vpcName != null and query.vpcName != ''">
            AND b.VPC_NAME LIKE CONCAT(CONCAT('%',#{query.vpcName}),'%')
        </if>
        <if test="query.sysBusinessId != null ">
            AND a.BUSINESS_SYSTEM_ID = #{query.sysBusinessId}
        </if>
        <if test="query.createTimeStart != null">
            AND a.CREATED_TIME &gt;= #{query.createTimeStart}
        </if>
        <if test="query.createTimeEnd != null">
            AND a.CREATED_TIME &lt;= #{query.createTimeEnd}
        </if>
        <if test="query.description != null and query.description != ''">
            AND a.DESCRIPTION LIKE CONCAT(CONCAT('%',#{query.description}),'%')
        </if>
        <if test="query.ids != null">
            AND a.ID IN
            <foreach collection="query.ids" separator="," item="id" open="(" close=")" index="index">
                #{id}
            </foreach>
        </if>
        ORDER BY a.CREATED_TIME DESC
    </select>

    <select id="selectBySecurityGroupId" resultType="com.datatech.slgzt.dao.model.security.WocSecurityGroupDO">
    SELECT a.ID, a.NAME, a.REGION_NAME, b.VPC_NAME, c.SYSTEM_NAME, a.CREATED_TIME, a.DESCRIPTION, a.RULE_SNAPSHOT, a.RESOURCE_ID
    FROM WOC_SECURITY_GROUP a
    INNER JOIN WOC_VPC_ORDER b ON a.VPC_ID = b.ID
    INNER JOIN CMP_APP_T c ON a.BUSINESS_SYSTEM_ID = c.ID
    WHERE a.STATUS = 1 AND A.id = #{id}
    </select>
</mapper>
package com.datatech.slgzt.model.req.container;

import lombok.Data;

/**
 * 容器配额分页查询请求
 * <AUTHOR>
 * @description 容器配额分页查询参数
 * @date 2025年05月27日
 */
@Data
public class ContainerQuotaPageReq {

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 页大小
     */
    private Integer pageSize = 10;

    /**
     * 配额名称（支持模糊查询）
     */
    private String cqName;

    /**
     * 业务系统ID
     */
    private Long businessSystemId;

    /**
     * 业务系统名称（支持模糊查询）
     */
    private String businessSystemName;

    /**
     * 工单ID
     */
    private String workOrderId;

    /**
     * 子订单ID
     */
    private String subOrderId;

    /**
     * 状态
     */
    private String status;

    /**
     * 云平台编码
     */
    private String domainCode;

    /**
     * 云平台名称
     */
    private String domainName;
    /**
     * 资源池ID
     */
    private String regionId;
    /**
     * 资源池编码
     */
    private String regionCode;

    /**
     * 资源池名称
     */
    private String regionName;

    /**
     * 4A账号
     */
    private String a4Account;

    /**
     * 创建时间开始
     */
    private String createTimeStart;

    /**
     * 创建时间结束
     */
    private String createTimeEnd;
}

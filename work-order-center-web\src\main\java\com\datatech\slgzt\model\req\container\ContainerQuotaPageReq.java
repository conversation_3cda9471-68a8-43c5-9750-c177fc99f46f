package com.datatech.slgzt.model.req.container;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

/**
 * 容器配额分页查询请求
 * <AUTHOR>
 * @description 容器配额分页查询参数
 * @date 2025年05月27日
 */
@Data
public class ContainerQuotaPageReq {

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 页大小
     */
    private Integer pageSize = 10;

    /**
     * 配额名称（支持模糊查询）
     */
    private String cqName;

    /**
     * 业务系统ID
     */
    private Long businessSystemId;

    /**
     * 业务系统名称（支持模糊查询）
     */
    private String businessSystemName;

    /**
     * 工单ID
     */
    private String workOrderId;

    /**
     * 子订单ID
     */
    private String subOrderId;

    /**
     * 状态
     */
    private String status;

    /**
     * 云平台编码
     */
    private String domainCode;

    /**
     * 云平台名称
     */
    private String domainName;
    /**
     * 资源池ID
     */
    // todo 支持精准匹配
    private String regionId;
    /**
     * 资源池编码
     */
    private String regionCode;

    /**
     * 资源池名称
     */
    private String regionName;

    /**
     * 云类型编码
     */
    // todo 支持精准匹配
    private String catalogueDomainCode;

    /**
     * 4A账号
     */
    // todo 支持模糊匹配
    private String a4Account;

    /**
     * 4A账号绑定的手机
     */
    // todo 支持模糊匹配
    private String a4Phone;

    /**
     * 申请用户ID
     */
    private Long applyUserId;

    /**
     * 申请用户名称（模糊查询）
     */
    private String applyUserName;

    /**
     * 申请时长
     */
    // todo 支持精确匹配
    private String applyTime;

    /**
     * 创建时间开始
     */
    // todo 不好使
    private String createTimeStart;

    /**
     * 创建时间结束
     */
    // todo 不好使
    private String createTimeEnd;

    /**
     * 容器配额-核心数
     */
    @JsonProperty("vCpus")
    // todo 需要支持精准匹配
    private Integer vCpus;

    /**
     * 容器配额-内存大小，单位G
     */
    // todo 需要支持精准匹配
    private Integer ram;
    /**
     * GPU算力
     */
    // todo 需要支持精准匹配
    private Integer gpuRatio;

    /**
     * GPU显存大小，单位GB
     */
    // todo 需要支持精准匹配
    private Integer gpuVirtualMemory;

    /**
     * 物理GPU卡(个)
     */
    @JsonProperty("gpuVirtualCore")
    // todo 需要支持精准匹配
    private Integer gpuCore;

    /**
     * 虚拟GPU卡(个)
     */
    @JsonProperty("gpuCore")
    // todo 需要支持精准匹配
    private Integer gpuVirtualCore;
    
}

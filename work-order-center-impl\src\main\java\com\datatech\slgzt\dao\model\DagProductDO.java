package com.datatech.slgzt.dao.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 模版产品创建的表
 * <AUTHOR>
 * @description TODO
 * @date 2025年 05月20日 14:25:58
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("WOC_DAG_PRODUCT")
public class DagProductDO extends BaseDO {

    @TableField(value = "ID")
    private String id;

    @TableField(value = "CONFIG_ID")
    private String configId;

    //产品类型
    @TableField(value = "PRODUCT_TYPE")
    private String productType;

    //产品创建状态
    @TableField(value = "STATUS")
    private String status;

    //消息
    @TableField(value = "MESSAGE")
    private String message;

    //产品创建参数
    @TableField(value = "PRODUCT_PARAM")
    private String productParam;

    //产品创建全局参数 可能包含用户信息等
    @TableField(value = "GLOBAL_PARAM")
    private String globalParam;
}

package com.datatech.slgzt.utils;

import com.datatech.slgzt.exception.BusinessException;
import com.ejlchina.data.Mapper;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 04月09日 15:09:48
 */
@Slf4j
public class XieyunUnpackUtil {

    /**
     * 谐云接口拆包工具
     * 返回data 数据
     */
    public static String unpackData(Mapper mapper, String errorMsg) {
        //正常会返回success true ,但是可能会是另一对象，没有data这个属性直接就是500错
        try {
            boolean success = mapper.getBool("success");
            if (!success) {
                log.warn("{}", mapper);
                //如果返回的不是success true，直接返回500
                throw new BusinessException("调用底层失败 失败内容为：" + mapper.getString("errorMsg"));
            }
            return mapper.getString("data");
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            // 如果没有data这个属性，直接返回500
            throw new BusinessException(errorMsg);
        }

    }

}

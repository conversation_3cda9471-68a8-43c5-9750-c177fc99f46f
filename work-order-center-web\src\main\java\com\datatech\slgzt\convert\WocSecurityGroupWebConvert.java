package com.datatech.slgzt.convert;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.model.dto.SecurityGroupOperateDTO;
import com.datatech.slgzt.model.dto.SecurityGroupRuleOperateDTO;
import com.datatech.slgzt.model.dto.WocSecurityGroupDTO;
import com.datatech.slgzt.model.query.SecurityGroupQuery;
import com.datatech.slgzt.model.req.security.*;
import com.datatech.slgzt.model.security.SecurityRuleModel;
import com.datatech.slgzt.model.vo.security.WocSecurityGroupVO;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @program: workordercenterproject
 * @description:
 * @author: LK
 * @create: 2025-04-29 10:38
 **/
@Mapper(componentModel = "spring")
public interface WocSecurityGroupWebConvert {

    @Mapping(target = "ruleSnapshot", expression = "java(convertRuleListToJson(req.getRuleList()))")
    @Mapping(target = "regionId", source = "resourcePoolId")
    WocSecurityGroupDTO convert(WocSecurityGroupCreateReq req);

    // 默认方法：将 List<SecurityRuleModel> 转为 JSON 字符串
    default String convertRuleListToJson(List<SecurityRuleModel> ruleList) {
        if (ruleList == null || ruleList.isEmpty()) {
            return null;
        }
        JSONArray jsonArray = new JSONArray();
        for (SecurityRuleModel rule : ruleList) {
            JSONObject ruleJson = new JSONObject();
            ruleJson.put("id", IdUtil.getSnowflake().nextId());
            ruleJson.put("direction", rule.getDirection());
            ruleJson.put("accessStatus", rule.getAccessStatus());
            ruleJson.put("priority", rule.getPriority());
            ruleJson.put("protocol", rule.getProtocol());
            ruleJson.put("portRange", rule.getPortRange());
            ruleJson.put("accreditIp", rule.getAccreditIp());
            ruleJson.put("description", rule.getDescription());
            jsonArray.add(ruleJson);
        }
        return jsonArray.toString();
    }

    @Mapping(target = "regionId", source = "resourcePoolId")
    @Mapping(target = "sysBusinessId", source = "businessSysId")
    @Mapping(target = "ids", expression = "java(convertIdToList(req.getSecurityGroupIds()))")
    SecurityGroupQuery convert(WocSecurityGroupPageReq req);

    default List<Long> convertIdToList(String ids) {
        List<Long> idList = new ArrayList<>();
        if (StringUtils.isEmpty(ids)) {
            return null;
        }
        for (String s : ids.split(",")) {
            idList.add(Long.parseLong(s));
        }
        return idList;
    }

    @Mapping(target = "ruleList", expression = "java(convertJsonToList(dto.getRuleSnapshot()))")
    @Mapping(target = "createTime", source = "createdTime")
    @Mapping(target = "resourcePoolId", source = "regionId")
    WocSecurityGroupVO convert(WocSecurityGroupDTO dto);

    default List<SecurityRuleModel> convertJsonToList(String ruleSnapshot) {
        if (StringUtils.isEmpty(ruleSnapshot)) {
            return null;
        }
        return JSON.parseArray(ruleSnapshot, SecurityRuleModel.class);
    }

    @Mapping(target = "securityGroupIds", expression = "java(convertIds(req.getSecurityGroupIds()))")
    SecurityGroupOperateDTO convert(WocSecurityGroupOperateReq req);

    default List<Long> convertIds(String securityGroupIds) {
        if (StringUtils.isEmpty(securityGroupIds)) {
            return null;
        }
        return ListUtil.toList(Arrays.stream(securityGroupIds.split(",")).map(Long::valueOf).collect(Collectors.toList()));
    }

    SecurityGroupRuleOperateDTO convert(WocSecurityGroupRuleReq req);
}

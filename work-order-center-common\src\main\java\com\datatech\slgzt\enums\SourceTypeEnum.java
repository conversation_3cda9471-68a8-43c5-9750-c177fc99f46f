package com.datatech.slgzt.enums;

import lombok.Getter;

/**
 * 工单类型的枚举
 */
@Getter
public enum SourceTypeEnum {

    /**
     * 订单类型枚举
     */
    STANDARD("standard","BZ", "开通资源"),
    NON_STANDARD("nonStandardSubscribe", "FB","非标开通资源"),
    EXTERNAL("external", "EX","外部资源"),
    ;

    private final String code;

    //前缀
    private final String prefix;

    private final String message;

    SourceTypeEnum(String code, String prefix, String message) {
        this.code = code;
        this.prefix = prefix;
        this.message = message;
    }

    /**
     * 分割订单号前缀
     * 获取对应的订单类型
     */
    public static SourceTypeEnum getOrderType(String orderNo) {
        for(SourceTypeEnum orderTypeEnum : SourceTypeEnum.values()) {
            if (orderNo.startsWith(orderTypeEnum.getPrefix())) {
                return orderTypeEnum;
            }
        }
        return null;
    }
}

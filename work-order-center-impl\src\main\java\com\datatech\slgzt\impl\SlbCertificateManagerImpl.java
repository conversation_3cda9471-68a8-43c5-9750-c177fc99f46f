package com.datatech.slgzt.impl;

import com.datatech.slgzt.convert.SlbCertificateManagerConvert;
import com.datatech.slgzt.dao.SlbCertificateDAO;
import com.datatech.slgzt.dao.model.SlbCertificateDO;
import com.datatech.slgzt.helps.UserHelper;
import com.datatech.slgzt.manager.SlbCertificateManager;
import com.datatech.slgzt.model.dto.SlbCertificateDTO;
import com.datatech.slgzt.model.query.SlbCertificateQuery;
import com.datatech.slgzt.model.usercenter.UserCenterUserDTO;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.warpper.PageWarppers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * SLB证书Manager实现
 */
@Slf4j
@Service
public class SlbCertificateManagerImpl implements SlbCertificateManager {

    @Resource
    private SlbCertificateDAO slbCertificateDAO;
    
    @Resource
    private SlbCertificateManagerConvert slbCertificateManagerConvert;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(SlbCertificateDTO dto) {
        SlbCertificateDO slbCertificateDO = slbCertificateManagerConvert.dto2do(dto);
        slbCertificateDAO.insert(slbCertificateDO);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(SlbCertificateDTO dto) {
        SlbCertificateDO slbCertificateDO = slbCertificateManagerConvert.dto2do(dto);
        slbCertificateDAO.update(slbCertificateDO);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        slbCertificateDAO.delete(id);
    }
    
    @Override
    public SlbCertificateDTO getById(String id) {
        SlbCertificateDO slbCertificateDO = slbCertificateDAO.getById(id);
        return slbCertificateManagerConvert.do2dto(slbCertificateDO);
    }
    
    @Override
    public PageResult<SlbCertificateDTO> page(SlbCertificateQuery query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<SlbCertificateDO> list = slbCertificateDAO.list(query);
        return PageWarppers.box(new PageInfo<>(list), slbCertificateManagerConvert::do2dto);
    }
} 
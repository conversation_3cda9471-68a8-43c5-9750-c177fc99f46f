package com.datatech.slgzt.impl.service;

import com.datatech.slgzt.service.PlatformService;
import com.datatech.slgzt.utils.OkHttpsUtils;
import com.datatech.slgzt.utils.Precondition;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

/**
 * @program: cloudbasedbusinessproject
 * @description: 资源中心交互类
 * @author: LK
 * @create: 2024-12-13 14:12
 **/
@Service
@Slf4j
public class PlatformServiceImpl implements PlatformService {

    @Value("${http.resourceCenterUrl}")
    String resourceCenter;

    String platformSyncUrl = "/v1/cloud/resourcecenter/tenant/alone";


    @Resource
    private RestTemplate restTemplate;


    private Long getTenantIdFromResource(String account, String regionCode) {
        Long tenantId;
        String url = resourceCenter + platformSyncUrl + "?account=" + account + "&regionCode=" + regionCode;
        log.info("request resource center url:" + url);
        Mapper mapperData =
                OkHttps.sync(url)
                        .addHeader("RemoteUser", "BusinessCenter")
                        .addUrlPara("account", account)
                        .addUrlPara("regionCode", regionCode)
                        .get()
                        .getBody()
                        .toMapper();
        Precondition.checkArgument(mapperData.getInt("success") == 1, "资源中心调用失败:" + mapperData.getString("message"));
        Mapper result = mapperData.getMapper("entity");
        Precondition.checkArgument(result, "资源中心返回数据为空");
        Tenant tenant = result.toBean(Tenant.class);
        tenantId = tenant.getId();
        return tenantId;
    }


    @Override
    public Long getOrCreateTenantId(String account, String regionCode) {
        Long tenantId = getResourceTenantId(account, regionCode);
        if (tenantId != null) return tenantId;
        String url = resourceCenter + "v1/cloud/resourcecenter/tenant/create";
        log.info("Calling resource center URL: {}", url);
        Mapper response = OkHttpsUtils.http().sync(url)
                                      .addHeader("RemoteUser", "BusinessCenter")
                                      .addUrlPara("account", account)
                                      .addUrlPara("regionCode", regionCode)
                                      .addUrlPara("safetyCall"
                        , "true").post()  // 原逻辑是POST请求
                                      .getBody().toMapper();
        Precondition.checkArgument(response.getInt("success") == 1, "资源中心调用失败: " + response.getString("message"));
        tenantId = getResourceTenantId(account, regionCode);
        Precondition.checkArgument(tenantId != null, "资源中心租户ID查询失败");
        return tenantId;

    }

    @Override
    public Long getResourceTenantId(String account, String regionCode) {
        try {
            String url = resourceCenter + "v1/cloud/resourcecenter/tenant/alone";
            Mapper response =
                    OkHttps.sync(url)
                            .addHeader("RemoteUser", "BusinessCenter")
                            .addHeader("Content-Type", "application/json")
                            .addUrlPara("account", account)
                            .addUrlPara(
                            "regionCode", regionCode)
                            .get()
                            .getBody()
                            .toMapper();

            log.info("Resource tenant query response: {}", response);
            Precondition.checkArgument(response.getInt("success") == 1, "资源中心查询失败: " + response.getString("message"));
            Mapper entity = response.getMapper("entity");
            Precondition.checkArgument(entity != null, "响应数据缺失entity字段");

            return entity.getLong("id");
        } catch(Exception e) {
            log.error("租户ID查询异常: {}", e.getMessage());
            return null;
        }
    }

    @Data
    static class Tenant {
        private Long id;
        private String name;
        private String description;
        private String platformCode;
    }
}

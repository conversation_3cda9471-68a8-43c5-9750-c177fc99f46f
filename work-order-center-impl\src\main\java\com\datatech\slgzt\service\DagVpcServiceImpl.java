package com.datatech.slgzt.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.datatech.slgzt.dao.mapper.network.VpcOrderMapper;
import com.datatech.slgzt.dao.mapper.network.VpcSubnetOrderMapper;
import com.datatech.slgzt.dao.model.vpc.VpcOrder;
import com.datatech.slgzt.dao.model.vpc.VpcSubnetOrder;
import com.datatech.slgzt.enums.network.NetworkPrefixEnum;
import com.datatech.slgzt.enums.network.NetworkStatusEnum;
import com.datatech.slgzt.exception.BusinessException;
import com.datatech.slgzt.handle.ResourceHandle;
import com.datatech.slgzt.model.dto.vpc.CreateVpcAndSubnetRcDTO;
import com.datatech.slgzt.model.tem.DagVpcModel;
import com.datatech.slgzt.model.tem.TemVpcSubnetModel;
import com.datatech.slgzt.service.dag.DagProductCreateService;
import com.datatech.slgzt.utils.HeadersUtil;
import com.datatech.slgzt.utils.HttpClientUtil;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.UuidUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 05月20日 11:30:42
 */
@Service
@Slf4j
public class DagVpcServiceImpl implements DagProductCreateService {


    @Resource
    private VpcOrderMapper vpcOrderMapper;

    @Resource
    private VpcSubnetOrderMapper vpcSubnetOrderMapper;

    @Resource
    private ResourceHandle resourceHandle;

    /**
     * 创建VPC
     *
     * @return
     */
    @Transactional
    @Override
    public String createProduct(String createParam, String globalParam) {
        DagVpcModel dagVpcModel = JSONObject.parseObject(createParam, DagVpcModel.class);
        // 1. 参数校验
        if (vpcOrderMapper.selectVpcName(
                dagVpcModel.getRegionCode(),
                dagVpcModel.getVpcName(),
                null,  // TODO: tenantId - TemVpcModel中缺少租户ID
                dagVpcModel.getCidr()) > 0) {
            throw new BusinessException(dagVpcModel.getVpcName() + "：vpc已存在，请勿重复创建");
        }

        // 2. 创建VPC订单
        VpcOrder vpcOrder = new VpcOrder();
        String vpcId = UuidUtil.getGid(NetworkPrefixEnum.VPC.getType());

        // 基础信息设置
        vpcOrder.setId(vpcId);
        vpcOrder.setCreatedTime(new Date());
        vpcOrder.setVpcName(dagVpcModel.getVpcName());

        // 租户相关 - dagVpcModel中缺少
        vpcOrder.setTenantId(null);        // TODO: 缺少租户ID
        vpcOrder.setTenantName(null);      // TODO: 缺少租户名称
        vpcOrder.setAccount(null);         // TODO: 缺少账号信息，通常为bottomTenantId

        // 业务系统相关 - dagVpcModel中缺少
        vpcOrder.setBusinessSysId(null);   // TODO: 缺少业务系统ID
        vpcOrder.setBusinessSysName(null); // TODO: 缺少业务系统名称

        // 工单相关 - dagVpcModel中缺少
        vpcOrder.setOrderId(null);         // TODO: 缺少工单ID
        vpcOrder.setOrderCode(null);       // TODO: 缺少工单编码

        // 用户相关 - dagVpcModel中缺少
        vpcOrder.setCreatedBy(null);       // TODO: 缺少创建人ID
        vpcOrder.setApplyUserId(null);     // TODO: 缺少申请人ID

        // 网络配置
        vpcOrder.setRegionCode(dagVpcModel.getRegionCode());
        vpcOrder.setIpv4Cidr(dagVpcModel.getCidr());
        vpcOrder.setIpv6Cidr(dagVpcModel.getIpv6Cidr());
        vpcOrder.setDeleted(1);
        vpcOrder.setGid(vpcId);
        vpcOrder.setAzCode(dagVpcModel.getAzCode());
        vpcOrder.setPlane(dagVpcModel.getPlane());
        vpcOrder.setStatus(NetworkStatusEnum.EXECUTING.getType());
        vpcOrder.setDetail(dagVpcModel.getDetail());

        // 3. 准备创建VPC请求
        CreateVpcAndSubnetRcDTO createVpcDto = new CreateVpcAndSubnetRcDTO();
        createVpcDto.setRegionCode(dagVpcModel.getRegionCode());
        createVpcDto.setName(dagVpcModel.getVpcName());
        createVpcDto.setIpv4Cidr(dagVpcModel.getCidr());
        createVpcDto.setIpv6Cidr(dagVpcModel.getIpv6Cidr());
        createVpcDto.setOrderId(vpcId);
        createVpcDto.setGId(vpcId);

        // 底层租户ID - dagVpcModel中缺少
        createVpcDto.setTenantId(null);    // TODO: 缺少bottomTenantId

        // 4. 处理子网
        if (!CollectionUtils.isEmpty(dagVpcModel.getSubnetDTOList())) {
            List<CreateVpcAndSubnetRcDTO.CreateSubnetRcDto> createSubnetDtoList = new ArrayList<>();
            List<VpcSubnetOrder> vpcSubnetOrderList = new ArrayList<>();

            for (TemVpcSubnetModel subnet : dagVpcModel.getSubnetDTOList()) {
                String subnetId = UuidUtil.getGid(NetworkPrefixEnum.VPC_SUB.getType());
                String cidr = subnet.getStartIp() + "/" + subnet.getNetmask();

                // 创建子网订单
                VpcSubnetOrder subnetOrder = new VpcSubnetOrder();
                subnetOrder.setId(subnetId);
                subnetOrder.setVpcId(vpcId);
                subnetOrder.setCreatedTime(new Date());
                subnetOrder.setSubnetName(subnet.getSubnetName());
                subnetOrder.setAzCode(dagVpcModel.getAzCode());
                subnetOrder.setCidr(cidr);
                subnetOrder.setStartIp(subnet.getStartIp());
                subnetOrder.setNetmask(subnet.getNetmask());
                subnetOrder.setDeleted(1);
                subnetOrder.setInstanceId(subnet.getInstanceId());
                subnetOrder.setLevel2InstanceId(subnet.getLevel2InstanceId());
                subnetOrder.setStatus(NetworkStatusEnum.EXECUTING.getType());
                subnetOrder.setUuid(subnet.getUuid());
                vpcSubnetOrderList.add(subnetOrder);

                // 创建子网请求
                CreateVpcAndSubnetRcDTO.CreateSubnetRcDto subnetDto = new CreateVpcAndSubnetRcDTO.CreateSubnetRcDto();
                subnetDto.setCidr(cidr);
                subnetDto.setName(subnet.getSubnetName());
                subnetDto.setAzCode(dagVpcModel.getAzCode());
                subnetDto.setVpcId(vpcId);
                subnetDto.setOrderId(subnetId);

                // 底层租户ID - dagVpcModel中缺少
                subnetDto.setTenantId(null);        // TODO: 缺少bottomTenantId

                createSubnetDtoList.add(subnetDto);
            }

            createVpcDto.setSubnetDTOList(createSubnetDtoList);
            vpcOrder.setSubnetNum(vpcSubnetOrderList.size());

            // 保存子网订单
            if (!vpcSubnetOrderList.isEmpty()) {
                vpcSubnetOrderMapper.batchInsertVpcSubnet(vpcSubnetOrderList);
            }
        }

        // 5. 保存VPC订单
        vpcOrderMapper.insert(vpcOrder);

        // 6. 调用资源中心创建VPC
        String requestUrl = resourceHandle.resourceCenterUrl + resourceHandle.getVpcCreateVpcAndSubVerify();
        log.info("vpc创建请求参数 createVpcDto:{}", JSONObject.toJSONString(createVpcDto));

        Map<String, Object> response = HttpClientUtil.post(
                requestUrl,
                JSONObject.toJSONString(createVpcDto),
                HeadersUtil.getHeaders()
        );

        log.info("vpc创建返回参数 response:{}", response);

        Integer code = (Integer) response.get("code");
        String json = (String) response.get("json");

        if (code != 200) {
            String message = "vpc创建失败";
            if (ObjNullUtils.isNotNull(json)) {
                JSONObject jsonObject = JSONObject.parseObject(json);
                message = jsonObject.get("message").toString();
            }

            // 更新VPC状态为错误
            vpcOrder.setStatus(NetworkStatusEnum.ERROR.getType());
            vpcOrder.setMessage(JSONObject.toJSONString(response));
            vpcOrderMapper.updateById(vpcOrder);

            log.error("vpc创建失败 error {}", message);
            throw new BusinessException(message);
        }
        return vpcId;
    }

    /**
     * 产品类型
     */
    @Override
    public String getProductType() {
        return "vpc";
    }
}

package com.datatech.slgzt.impl.manager.xieyun;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.config.XieyunProperties;
import com.datatech.slgzt.convert.XieYunOpmManagerConvert;
import com.datatech.slgzt.helps.XieYunLoginHelper;
import com.datatech.slgzt.manager.xieyun.XieyunProjectManager;
import com.datatech.slgzt.manager.xieyun.local.XieyunProjectLocalManager;
import com.datatech.slgzt.model.dto.XieYunProjectDTO;
import com.datatech.slgzt.model.dto.xieyun.QuotaResultDTO;
import com.datatech.slgzt.model.query.container.XieYunProjectQuery;
import com.datatech.slgzt.model.xieyun.XieyunProjectCreateOpm;
import com.datatech.slgzt.model.xieyun.XieyunProjectQuotaOpm;
import com.datatech.slgzt.utils.XieyunUnpackUtil;
import com.ejlchina.data.Mapper;
import com.ejlchina.okhttps.OkHttps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 04月09日 16:25:46
 */
@Service
@Slf4j
public class XieyunProjectManagerImpl implements XieyunProjectManager {

    @Resource
    private XieyunProperties xieyunProperties;

    @Resource
    private XieyunProjectLocalManager projectLocalManager;

    @Resource
    private XieYunOpmManagerConvert opmManagerConvert;

    @Override
    @Transactional
    public String ensureProject(String orgId, XieyunProjectCreateOpm opm) {
        XieYunProjectQuery query = new XieYunProjectQuery().setProjectName(opm.getProjectName());
        List<XieYunProjectDTO> list = projectLocalManager.list(query);
        if (CollectionUtil.isNotEmpty(list)) {
            log.info("谐云项目：{} 在数据库中已存在，直接返回项目id：{}", opm.getProjectName(), list.get(0).getXieYunProjectId());
            return list.get(0).getXieYunProjectId();
        }

        String adminToken = XieYunLoginHelper.INSTANCE.getAdminToken();
        Mapper mapper = OkHttps.sync(xieyunProperties.getXieyunUrl() + "/caas-amp/organization/" + orgId + "/projects")
                .addBodyPara(JSON.parseObject(JSON.toJSONString(opm)))
                .bodyType("json")
                .addHeader("Authorization", adminToken)
                .addHeader("Amp-Organ-Id", orgId)
                .addHeader("Amp-App-Id", xieyunProperties.getAmpAppId())
                .post()
                .getBody()
                .toMapper();
        String projectId = XieyunUnpackUtil.unpackData(mapper, "创建项目调用底层接口失败");

        // 创建本地项目数据
        XieYunProjectDTO projectDTO = opmManagerConvert.projectOpm2dto(opm);
        projectDTO.setXieYunOrgId(orgId)
                .setXieYunProjectId(projectId);
        projectLocalManager.insert(projectDTO);
        return projectId;
    }

    @Override
    public String queryProject(String projectName) {
        XieYunProjectQuery query = new XieYunProjectQuery().setProjectName(projectName);
        List<XieYunProjectDTO> list = projectLocalManager.list(query);
        if (CollectionUtil.isNotEmpty(list)) {
            return list.get(0).getXieYunProjectId();
        }
        return null;
    }

    /**
     * 项目分配额度
     */
    @Override
    @Transactional
    public String projectQuota(String orgId, String projectId, String cluster, String nodepool, XieyunProjectQuotaOpm opm) {
        //先本地创建
        XieYunProjectDTO projectDTO = opmManagerConvert.projectQuotaOpm2dto(opm);
        projectDTO.setClusterName(cluster)
                .setXieYunProjectId(projectId)
                .setXieYunOrgId(orgId)
                .setNodePoolName(nodepool)
                .setUpdatedTime(LocalDateTime.now());
        projectLocalManager.updateByProjectId(projectDTO);

        //如果创建成功，调用xieyun接口创建
        String adminToken = XieYunLoginHelper.INSTANCE.getAdminToken();
        Mapper mapper = OkHttps.sync(xieyunProperties.getXieyunUrl() + "/olympus-core/organizations/" + orgId + "/clusters/"+cluster+"/nodepools/"+nodepool+"/projects/" + projectId + "/quota")
               .addBodyPara(JSON.parseObject(JSON.toJSONString(opm)))
                .bodyType("json")
                .addHeader("Authorization", adminToken)
                .addHeader("Amp-Organ-Id", orgId)
                .addHeader("Amp-App-Id", xieyunProperties.getAmpAppId())
                .post()
                .getBody()
                .toMapper();
        return XieyunUnpackUtil.unpackData(mapper, "项目分配额度调用底层接口失败");
    }

    @Override
    public QuotaResultDTO selectProjectQuota(String clusterName, String nodePoolName, String orgId, String projectId) {
        // /olympus-core/organizations/3919155575887945728/clusters/eic-wz-cluster/nodepools/default/projects/1927616236795252736/quota
        String uriString = UriComponentsBuilder
                .fromPath("/olympus-core/organizations/{orgId}/clusters/{clusterName}/nodepools/{nodePoolName}/projects/{projectId}/quota")
                .buildAndExpand(orgId, clusterName, nodePoolName, projectId).toUriString();
        // 创建远程数据
        String adminToken = XieYunLoginHelper.INSTANCE.getAdminToken();
        Mapper mapper = OkHttps.sync(xieyunProperties.getXieyunUrl() + uriString)
                .bodyType("json")
                .addHeader("Authorization", adminToken)
                .addHeader("Amp-Organ-Id", xieyunProperties.getAmpOrganId())
                .get()
                .getBody()
                .toMapper();
        String data = XieyunUnpackUtil.unpackData(mapper, "调用底层分配额度接口失败");

        return JSON.parseObject(data, QuotaResultDTO.class);
    }



}

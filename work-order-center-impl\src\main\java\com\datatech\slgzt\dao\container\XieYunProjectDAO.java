package com.datatech.slgzt.dao.container;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.container.XieYunProjectMapper;
import com.datatech.slgzt.dao.model.container.XieYunOrgDO;
import com.datatech.slgzt.dao.model.container.XieYunProjectDO;
import com.datatech.slgzt.model.query.container.XieYunProjectQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: liupeihan
 * @Date: 2025/4/14
 */

@Repository
public class XieYunProjectDAO {

    @Resource
    private XieYunProjectMapper mapper;

    public void insert(XieYunProjectDO projectDO) {
        mapper.insert(projectDO);
    }

    public XieYunProjectDO getById(String id) {
        return mapper.selectById(id);
    }

    public void update(XieYunProjectDO projectDO) {
        mapper.updateById(projectDO);
    }

    public List<XieYunProjectDO> list(XieYunProjectQuery query) {
        return mapper.selectList(Wrappers.<XieYunProjectDO>lambdaQuery()
                .eq(ObjNullUtils.isNotNull(query.getClusterName()), XieYunProjectDO::getClusterName, query.getClusterName())
                .eq(ObjNullUtils.isNotNull(query.getNodePoolName()), XieYunProjectDO::getNodePoolName, query.getNodePoolName())
                .eq(ObjNullUtils.isNotNull(query.getProjectName()), XieYunProjectDO::getProjectName, query.getProjectName())
        );
    }

    public void updateByProjectId(XieYunProjectDO projectDO) {
        UpdateWrapper<XieYunProjectDO> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("XIE_YUN_PROJECT_ID", projectDO.getXieYunProjectId());
        mapper.update(projectDO, updateWrapper);
    }
}


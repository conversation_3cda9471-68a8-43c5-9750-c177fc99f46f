package com.datatech.slgzt.manager;

import com.datatech.slgzt.model.dto.StandardWorkOrderProductDTO;
import com.datatech.slgzt.model.dto.order.StandardAuditWorkOrderDTO;
import com.datatech.slgzt.model.query.StandardWorkOrderProductQuery;
import com.datatech.slgzt.model.resourcce.ResourceShowInfoDTO;

import java.util.List;

public interface StandardWorkOrderProductManager {

    List<StandardWorkOrderProductDTO> list(StandardWorkOrderProductQuery query);


    /**
     * update
     */
    void update(StandardWorkOrderProductDTO dto);


    StandardWorkOrderProductDTO getById(Long id);

    StandardWorkOrderProductDTO getByGid(String gid);

    StandardWorkOrderProductDTO getBySubOrderId(Long subOrderId);

    void deleteByWorkOrderId(String id);


    /**
     * insert
     */
    void insert(StandardWorkOrderProductDTO dto);

    void updateStatusById(Long id, String status);


    void updateStatusByParentId(Long id, String status);

    void fillRegionMessage(StandardAuditWorkOrderDTO auditDto);

    ResourceShowInfoDTO selectResourceOverview(StandardWorkOrderProductQuery productQuery);

    /**
     * 更新Job执行ID（仅对CQ产品有效）
     * @param subOrderId 子订单ID
     * @param jobExecutionId Job执行ID
     */
    void updateJobExecutionIdBySubOrderId(Long subOrderId, Long jobExecutionId);
}

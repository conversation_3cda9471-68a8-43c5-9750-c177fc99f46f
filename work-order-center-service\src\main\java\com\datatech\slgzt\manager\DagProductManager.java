package com.datatech.slgzt.manager;

import com.datatech.slgzt.model.dto.DagProductDTO;
import com.datatech.slgzt.model.query.DagProductQuery;
import com.datatech.slgzt.utils.PageResult;

import java.util.List;

/**
 * DAG产品Manager接口
 */
public interface DagProductManager {

    /**
     * 创建DAG产品
     */
    void create(DagProductDTO dto);

    /**
     * 更新DAG产品
     */
    void update(DagProductDTO dto);

    /**
     * 删除DAG产品
     */
    void delete(String id);

    /**
     * 根据ID查询
     */
    DagProductDTO getById(String id);

    /**
     * 列表查询
     */
    List<DagProductDTO> list(DagProductQuery query);
} 
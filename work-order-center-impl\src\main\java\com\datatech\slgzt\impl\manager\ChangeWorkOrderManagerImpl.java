package com.datatech.slgzt.impl.manager;

import cn.hutool.core.util.IdUtil;
import com.datatech.slgzt.convert.ChangeWorkOrderManagerConvert;
import com.datatech.slgzt.dao.ChangeWorkOrderDAO;
import com.datatech.slgzt.dao.model.change.ChangeWorkOrderDO;
import com.datatech.slgzt.enums.OrderTypeEnum;
import com.datatech.slgzt.manager.ChangeWorkOrderManager;
import com.datatech.slgzt.model.dto.change.ChangeWorkOrderDTO;
import com.datatech.slgzt.model.query.ChangeWorkOrderQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.utils.StreamUtils;
import com.datatech.slgzt.warpper.PageWarppers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


@Service
@Slf4j
public class ChangeWorkOrderManagerImpl implements ChangeWorkOrderManager {

    @Resource
    private ChangeWorkOrderDAO dao;

    @Resource
    private ChangeWorkOrderManagerConvert convert;


    @Override
    public List<ChangeWorkOrderDTO> list(ChangeWorkOrderQuery query) {
        return StreamUtils.mapArray(dao.list(query), convert::do2DTO);
    }

    @Override
    public PageResult<ChangeWorkOrderDTO> page(ChangeWorkOrderQuery query) {
        if (query.getPageNum() != null && query.getPageSize() != null) {
            PageHelper.startPage(query.getPageNum(), query.getPageSize());
        }
        List<ChangeWorkOrderDO> list = dao.listApproval(query);
        return PageWarppers.box(new PageInfo<>(list), convert::do2DTO);
    }

    @Override
    public String create(ChangeWorkOrderDTO dto) {
        if (ObjNullUtils.isNull(dto.getId())) {
            dto.setId(OrderTypeEnum.CHANGE.getPrefix() + "-" + IdUtil.nanoId());
        }
        dao.insert(convert.dto2DO(dto));
        return dto.getId();

    }

    @Override
    public void update(ChangeWorkOrderDTO orderDTO) {
        ChangeWorkOrderDO orderDO = convert.dto2DO(orderDTO);
        dao.update(orderDO);
    }

    @Override
    public ChangeWorkOrderDTO getById(String workOrderId) {
        return convert.do2DTO(dao.getById(workOrderId));
    }
}

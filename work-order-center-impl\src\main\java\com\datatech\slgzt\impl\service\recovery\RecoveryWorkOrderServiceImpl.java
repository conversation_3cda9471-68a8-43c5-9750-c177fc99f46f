package com.datatech.slgzt.impl.service.recovery;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.convert.RecoveryWorkOrderServiceConvert;
import com.datatech.slgzt.dao.mapper.network.NetworkOrderMapper;
import com.datatech.slgzt.dao.mapper.network.VpcOrderMapper;
import com.datatech.slgzt.enums.*;
import com.datatech.slgzt.enums.bpmn.ActivityEnum;
import com.datatech.slgzt.enums.bpmn.RecoveryOrderNodeEnum;
import com.datatech.slgzt.exception.UniversalException;
import com.datatech.slgzt.helps.UserHelper;
import com.datatech.slgzt.manager.*;
import com.datatech.slgzt.model.BaseReconveryProductModel;
import com.datatech.slgzt.model.baseconfig.OacConfig;
import com.datatech.slgzt.model.bpmn.ActivityTaskVo;
import com.datatech.slgzt.model.bpmn.InnerTask;
import com.datatech.slgzt.model.bpmn.TaskNodeDTO;
import com.datatech.slgzt.model.dto.*;
import com.datatech.slgzt.model.dto.order.RecoveryAuditWorkOrderDTO;
import com.datatech.slgzt.model.dto.recovery.RecoveryWorkOrderDetailDTO;
import com.datatech.slgzt.model.home.AuditCountVo;
import com.datatech.slgzt.model.opm.RecoveryWorkOrderCreateOpm;
import com.datatech.slgzt.model.query.RecoveryWorkOrderQuery;
import com.datatech.slgzt.model.query.ResourceDetailQuery;
import com.datatech.slgzt.model.query.WorkOrderAuthLogQuery;
import com.datatech.slgzt.model.recovery.*;
import com.datatech.slgzt.model.usercenter.UserCenterUserDTO;
import com.datatech.slgzt.model.vo.network.NetworkOrderResult;
import com.datatech.slgzt.model.vo.network.NetworkTableVo;
import com.datatech.slgzt.model.vo.vpc.VpcOrderResult;
import com.datatech.slgzt.model.vo.vpc.VpcTableVo;
import com.datatech.slgzt.service.OrderDataProvideService;
import com.datatech.slgzt.service.UserService;
import com.datatech.slgzt.service.bpmn.BaseActivity;
import com.datatech.slgzt.service.config.ConfigService;
import com.datatech.slgzt.service.network.NetworkCommonService;
import com.datatech.slgzt.service.order.RecoveryWorkOrderTempSaveService;
import com.datatech.slgzt.service.recovery.RecoveryWorkOrderService;
import com.datatech.slgzt.service.usercenter.IUserSelectStrategy;
import com.datatech.slgzt.utils.*;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月24日 21:52:30
 */
@Service
@Slf4j
public class RecoveryWorkOrderServiceImpl implements RecoveryWorkOrderService {

    @Resource
    private RecoveryWorkOrderProductManager productManager;

    @Resource
    private ResourceDetailManager resourceDetailManager;

    @Resource
    private SlbListenerManager slbListenerManager;

    @Resource
    private RecoveryWorkOrderManager reconveryWorkOrderManager;

    @Resource
    private RegionManager regionManager;

    @Resource
    private OrderDataProvideService orderDataProvideService;

    @Resource
    private RecoveryWorkOrderServiceConvert convert;

    @Resource
    private UserHelper userHelper;

    @Resource
    private BaseActivity baseActivity;

    @Resource
    private WorkOrderAuthLogManager workOrderAuthLogManager;

    @Resource
    private ConfigService configService;

    @Resource
    private NetworkOrderManager networkOrderManager;

    @Resource
    private VpcOrderManager vpcOrderManager;

    @Resource
    private UserService userService;

    @Resource
    private RecoveryWorkOrderTempSaveService recoveryWorkOrderTempSaveService;

    @Resource
    private RecoveryWorkOrderProductManager recoveryWorkOrderProductManager;
    @Resource
    private NetworkCommonService networkCommonService;


    @Resource
    private VpcOrderMapper vpcOrderMapper;

    @Resource
    private NetworkOrderMapper networkOrderMapper;

    @Resource
    private IUserSelectStrategy iUserSelectStrategy;

    /**
     * createRecoveryWorkOrder
     *
     * @param opm
     */
    @Override
    public String createRecoveryWorkOrder(RecoveryWorkOrderCreateOpm opm) {
        RecoveryWorkOrderDTO submitDTO = convert.convert(opm);
        //如果传入id不是空的 先对所有关联的产品全部解绑,然后清空关联的产品
        if (StringUtils.isNotEmpty(submitDTO.getId())) {
            afterCancelUpdateData(submitDTO.getId());
            //todo 网络和vpc需要特殊处理
            recoveryWorkOrderProductManager.deleteByWorkOrderId(submitDTO.getId());
        }
        submitDTO.setOrderCode("HS" + System.currentTimeMillis());
        //--------------------创建工单并对接流程服务和记录日志----------
        String workOrderId = fillAndCreateWorkOrder(submitDTO, opm.getCanDraft());
        //--------------------开始查询对应的产品创建对应的product-------
        //--------------------ecs----------------------------------
        fillCreateEcs(opm, workOrderId);
        //--------------------gcs----------------------------------
        fillCreateGcs(opm, workOrderId);
        //--------------------mysql----------------------------------
        fillCreateMysql(opm, workOrderId);
        //--------------------redis----------------------------------
        fillCreateRedis(opm, workOrderId);
        //--------------------evs----------------------------------
        fillCreateEvs(opm, workOrderId);
        //--------------------eip----------------------------------
        fillCreateEip(opm, workOrderId);
        //--------------------obs----------------------------------
        fillCreateObs(opm, workOrderId);
        //--------------------nat----------------------------------
        fillCreateNat(opm, workOrderId);
        //--------------------slb----------------------------------
        fillCreateSlb(opm, workOrderId);
        //--------------------vpc----------------------------------
        fillCreateVpc(opm, workOrderId);
        //--------------------network------------------------------
        fillCreateNetwork(opm, workOrderId);
        return workOrderId;
    }

    private String fillAndCreateWorkOrder(RecoveryWorkOrderDTO submitDTO, Boolean canDraft) {
        String inputOrderId = submitDTO.getId();
        UserCenterUserDTO userCenterUserDTO = userService.getUserById(submitDTO.getCreatedBy());
        LocalDateTime now = LocalDateTime.now();
        String activityId = "";
        String auditResult = "";
        if (StringUtils.isEmpty(inputOrderId)) {
            // 创建工单
            submitDTO.setWorkOrderStartTime(now);
            submitDTO.setCreateTime(now);
            inputOrderId = reconveryWorkOrderManager.createWorkOrder(submitDTO);
            auditResult = OrderLogStatusEnum.CREATE.getCode();
            activityId = baseActivity.instanceRun(submitDTO, ActivityEnum.ActivityProcessEnum.RESOURCE_RECOVERY_PROCESS, AuthorityCodeEnum.USER_TASK.code(), null);
            if (StringUtils.isEmpty(activityId)) {
                throw UniversalException.build(GlobalExceptionEnum.ACTIVITY_DEFINITION_IS_NOT_FOUND);
            }
        } else {
            auditResult = OrderLogStatusEnum.RESUBMIT.getCode();
            RecoveryWorkOrderDTO orderDTO = reconveryWorkOrderManager.getById(submitDTO.getId());
            activityId = orderDTO.getActivitiId();
        }

        submitDTO.setActivitiId(activityId);
        submitDTO.setActiviteKey(ActivityEnum.ActivityProcessEnum.RESOURCE_RECOVERY_PROCESS.getCode());
        submitDTO.setOrderStatus(OrderStatusEnum.EXAMINING.getCode());

        // 创建审批日志
        workOrderAuthLogManager.createWorkOrderAuthLog(new WorkOrderAuthLogDTO().setCreateTime(now).setModifyTime(now)
                .setWorkOrderId(inputOrderId).setProcessInstanceId(activityId)
                .setAuditNodeCode(ActivitiStatusEnum.USER_TASK.getNode())
                .setAuditNodeName(com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum.USER_TASK.getNodeRemark())
                .setUserId(submitDTO.getCreatedBy()).setUserName(userCenterUserDTO.getUserName())
                .setUserPhone(userCenterUserDTO.getPhone()).setUserEmail(userCenterUserDTO.getUserEmail())
                .setAuditResult(auditResult));
        // 进行节点审批
        if (!canDraft) {
            baseActivity.complete(submitDTO, ActivityEnum.ActivityStatusEnum.PASS, activityId, null, ActivityEnum.ActivityProcessEnum.RESOURCE_RECOVERY_PROCESS,
                    userCenterUserDTO.getId(), null, null);
        }
        ActivityTaskVo taskVo = baseActivity.taskNodes(activityId, ActivityEnum.ActivityProcessEnum.RESOURCE_RECOVERY_PROCESS);
        submitDTO.setCurrentNodeCode(taskVo.getCurrentTask());
        submitDTO.setCurrentNodeName(com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum.getNodeRemarkByNode(taskVo.getCurrentTask(), RecoveryOrderNodeEnum.EXAMINING.getCode()));
        submitDTO.setCurrentNodeStartTime(now);
        updateOrderStatus(submitDTO, submitDTO.getCreatedBy(), OrderStatusEnum.EXAMINING.getCode());
        // 提交后删除购物车中的暂存数据
        recoveryWorkOrderTempSaveService.handleDeleteAll(submitDTO.getCreatedBy());
        return inputOrderId;
    }

    private void fillCreateNetwork(RecoveryWorkOrderCreateOpm opm, String workOrderId) {
        if (ObjNullUtils.isNull(opm.getNetworkIdList())) {
            return;
        }
        List<NetworkOrderResult> networkOrderResultList = networkOrderManager.selectNetworkRecoveryList(opm.getNetworkIdList());
        networkOrderResultList.forEach(networkOrderResult -> {
            //先锁定资源
            networkOrderMapper.updateRecoveryStatus(Collections.singletonList(networkOrderResult.getId()), null, RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
            RecoveryNetworkModel networkModel = new RecoveryNetworkModel();
            networkModel.setNetworkId(networkOrderResult.getId());
            networkModel.setNetworkName(networkOrderResult.getName());
            networkModel.setRegionName(networkOrderResult.getPoolName());
            networkModel.setRegionCode(networkOrderResult.getRegionCode());
            networkModel.setDomainName(networkOrderResult.getDomainName());
            networkModel.setCatalogueDomainCode(networkOrderResult.getCatalogueDomainCode());
            networkModel.setCatalogueDomainName(networkOrderResult.getCatalogueDomainName());
            networkModel.setOrderCode(networkOrderResult.getOrderCode());
            networkModel.setNetworkType(networkOrderResult.getNetworkType());
            networkModel.setTenantName(networkOrderResult.getTenantName());
            networkModel.setBillId(networkOrderResult.getBillId());
            long productOrderId = IdUtil.getSnowflake().nextId();
            networkModel.setProductOrderId(productOrderId);
            //---------------------创建--------------------------------------
            RecoveryWorkOrderProductDTO product = new RecoveryWorkOrderProductDTO();
            product.setId(productOrderId);
            product.setProductType(ProductTypeEnum.NETWORK.getCode());
            product.setWorkOrderId(workOrderId);
            product.setHcmStatus(DisDimensionStatusEnum.WAIT.getCode());
            product.setRecoveryStatus(RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType().toString());
            product.setPropertySnapshot(JSON.toJSONString(networkModel));
            product.setParentProductId(0L);
            product.setResourceDetailId(networkOrderResult.getId());
            productManager.insert(product);
        });
    }

    private void fillCreateVpc(RecoveryWorkOrderCreateOpm opm, String workOrderId) {
        if (ObjNullUtils.isNull(opm.getVpcIdList())) {
            return;
        }
        List<VpcOrderResult> vpcOrderResultList = vpcOrderManager.listByIdList(opm.getVpcIdList());
        //创建VPC存储对象
        vpcOrderResultList.forEach(vpcOrderResult -> {
            //先锁定资源
            vpcOrderManager.updateRecoveryStatusByIds(Collections.singletonList(vpcOrderResult.getId()), RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
            RecoveryVpcModel vpcModel = new RecoveryVpcModel();
            vpcModel.setVpcId(vpcOrderResult.getId());
            vpcModel.setVpcName(vpcOrderResult.getVpcName());
            vpcModel.setRegionName(vpcOrderResult.getPoolName());
            vpcModel.setRegionCode(vpcOrderResult.getRegionCode());
            vpcModel.setDomainName(vpcOrderResult.getDomainName());
            vpcModel.setCatalogueDomainCode(vpcOrderResult.getCatalogueDomainCode());
            vpcModel.setCatalogueDomainName(vpcOrderResult.getCatalogueDomainName());
            vpcModel.setBillId(vpcOrderResult.getBillId());
            vpcModel.setTenantName(vpcOrderResult.getTenantName());
            vpcModel.setCidr(vpcOrderResult.getCidr());
            vpcModel.setSubnetNum(vpcOrderResult.getSubnetNum());
            vpcModel.setOrderCode(vpcOrderResult.getOrderCode());
            vpcModel.setUserName(vpcOrderResult.getUserName());
            long productOrderId = IdUtil.getSnowflake().nextId();
            vpcModel.setProductOrderId(productOrderId);
            //---------------------创建--------------------------------------
            RecoveryWorkOrderProductDTO product = new RecoveryWorkOrderProductDTO();
            product.setId(productOrderId);
            product.setProductType(ProductTypeEnum.VPC.getCode());
            product.setWorkOrderId(workOrderId);
            product.setHcmStatus(DisDimensionStatusEnum.WAIT.getCode());
            product.setRecoveryStatus(RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType().toString());
            product.setPropertySnapshot(JSON.toJSONString(vpcModel));
            product.setParentProductId(0L);
            product.setResourceDetailId(vpcOrderResult.getId());
            productManager.insert(product);
        });

    }

    private void fillCreateSlb(RecoveryWorkOrderCreateOpm opm, String workOrderId) {
        if (ObjNullUtils.isNull(opm.getSlbIdList())) {
            return;
        }
        List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setIds(opm.getSlbIdList())
                .setType(ProductTypeEnum.SLB.getCode()));
        resourceDetailDTOList.forEach(dto -> {
            //先锁定资源
            resourceDetailManager.updateRecoveryTypeByIds(Collections.singletonList(dto.getId()), RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
            RecoverySlbModel slbModel = new RecoverySlbModel();
            slbModel.setSlbId(dto.getDeviceId());
            slbModel.setSpec(dto.getSpec());
            slbModel.setSlbName(dto.getDeviceName());
            fillCommonParam(slbModel, dto, opm);
            //eip部分----------------------------
            if (ObjNullUtils.isNotNull(dto.getEip())) {
                resourceDetailManager.updateRecoveryTypeByDeviceIds(Collections.singletonList(dto.getEipId()), RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
                RecoveryEipModel recoveryEipModel = new RecoveryEipModel();
                recoveryEipModel.setProductOrderId(IdUtil.getSnowflake().nextId());
                recoveryEipModel.setEipId(dto.getEipId());
                recoveryEipModel.setEip(dto.getEip());
                recoveryEipModel.setBandwidth(dto.getBandWidth());
                slbModel.setEipModel(recoveryEipModel);
            }
            long productOrderId = IdUtil.getSnowflake().nextId();
            slbModel.setProductOrderId(productOrderId);
            //---------------------创建--------------------------------------
            RecoveryWorkOrderProductDTO product = new RecoveryWorkOrderProductDTO();
            product.setId(productOrderId);
            product.setSyncRecovery(opm.getSyncRecoveryIdList().contains(dto.getId()));
            product.setProductType(ProductTypeEnum.SLB.getCode());
            product.setWorkOrderId(workOrderId);
            product.setHcmStatus(DisDimensionStatusEnum.WAIT.getCode());
            product.setRecoveryStatus(RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType().toString());
            product.setPropertySnapshot(JSON.toJSONString(slbModel));
            product.setParentProductId(0L);
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            product.setResourceDetailId(dto.getId().toString());
            productManager.insert(product);
            //插入eip产品
            if (ObjNullUtils.isNotNull(dto.getEip())) {
                createEipProduct(Collections.singletonList(slbModel.getEipModel()), product.getId(), workOrderId);
            }
        });
    }


    private void fillCreateNat(RecoveryWorkOrderCreateOpm opm, String workOrderId) {
        if (ObjNullUtils.isNull(opm.getNatIdList())) {
            return;
        }
        //先拉回ecs列表
        List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setIds(opm.getNatIdList())
                .setType(ProductTypeEnum.NAT.getCode()));
        resourceDetailDTOList.forEach(dto -> {
            //先锁定资源
            resourceDetailManager.updateRecoveryTypeByIds(Collections.singletonList(dto.getId()), RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
            RecoveryNatModel natModel = new RecoveryNatModel();
            natModel.setNatId(dto.getDeviceId());
            natModel.setNatName(dto.getDeviceName());
            natModel.setVpcName(dto.getVpcName());
            natModel.setSpec(dto.getSpec());
            //eip部分------------------------
            String eipId = dto.getEipId();
            if (ObjNullUtils.isNotNull(eipId)) {
                resourceDetailManager.updateRecoveryTypeByDeviceIds(Collections.singletonList(dto.getEipId()), RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
                RecoveryEipModel recoveryEipModel = new RecoveryEipModel();
                recoveryEipModel.setProductOrderId(IdUtil.getSnowflake().nextId());
                recoveryEipModel.setBandwidth(dto.getBandWidth());
                recoveryEipModel.setEip(dto.getEip());
                recoveryEipModel.setEipId(dto.getEipId());
                natModel.setEipModel(recoveryEipModel);
            }
            //vpc部分----------------------------
            if (ObjNullUtils.isNotNull(dto.getVpcId())) {
                RecoveryPlaneNetworkModel reconveryVpcModel = new RecoveryPlaneNetworkModel();
                reconveryVpcModel.setId(dto.getVpcId());
                reconveryVpcModel.setName(dto.getVpcName());
                RecoveryPlaneNetworkModel.Subnet subnet = new RecoveryPlaneNetworkModel.Subnet();
                subnet.setSubnetId(dto.getSubnetId());
                subnet.setSubnetName(dto.getSubnetName());
                reconveryVpcModel.setSubnets(Collections.singletonList(subnet));
                natModel.setPlaneNetworkModel(reconveryVpcModel);
            }
            fillCommonParam(natModel, dto, opm);
            long productOrderId = IdUtil.getSnowflake().nextId();
            natModel.setProductOrderId(productOrderId);
            //---------------------创建--------------------------------------
            RecoveryWorkOrderProductDTO product = new RecoveryWorkOrderProductDTO();
            product.setId(productOrderId);
            product.setSyncRecovery(opm.getSyncRecoveryIdList().contains(dto.getId()));
            product.setProductType(ProductTypeEnum.NAT.getCode());
            product.setWorkOrderId(workOrderId);
            product.setHcmStatus(DisDimensionStatusEnum.WAIT.getCode());
            product.setRecoveryStatus(RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType().toString());
            product.setPropertySnapshot(JSON.toJSONString(natModel));
            product.setParentProductId(0L);
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            product.setResourceDetailId(dto.getId().toString());
            productManager.insert(product);
            //插入eip产品
            if (ObjNullUtils.isNotNull(eipId)) {
                createEipProduct(Collections.singletonList(natModel.getEipModel()), product.getId(), workOrderId);
            }
        });

    }


    private void fillCreateObs(RecoveryWorkOrderCreateOpm opm, String workOrderId) {
        if (ObjNullUtils.isNull(opm.getObsIdList())) {
            return;
        }
        //先拉回ecs列表
        List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setIds(opm.getObsIdList())
                .setType(ProductTypeEnum.OBS.getCode()));
        //查询对应ecs需要关联回收的产品比如它的EVS 和EIP
        resourceDetailDTOList.forEach(dto -> {
            //先锁定资源
            resourceDetailManager.updateRecoveryTypeByIds(Collections.singletonList(dto.getId()), RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
            RecoveryObsModel obsModel = new RecoveryObsModel();
            obsModel.setObsId(dto.getDeviceId());
            obsModel.setSpec(dto.getSpec());
            obsModel.setObsName(dto.getDeviceName());
            fillCommonParam(obsModel, dto, opm);
            long productOrderId = IdUtil.getSnowflake().nextId();
            obsModel.setProductOrderId(productOrderId);
            //---------------------创建--------------------------------------
            RecoveryWorkOrderProductDTO product = new RecoveryWorkOrderProductDTO();
            product.setId(productOrderId);
            product.setProductType(ProductTypeEnum.OBS.getCode());
            product.setWorkOrderId(workOrderId);
            product.setHcmStatus(DisDimensionStatusEnum.WAIT.getCode());
            product.setRecoveryStatus(RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType().toString());
            product.setPropertySnapshot(JSON.toJSONString(obsModel));
            product.setParentProductId(0L);
            product.setResourceDetailId(dto.getId().toString());
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            productManager.insert(product);
        });
    }

    private void fillCreateEvs(RecoveryWorkOrderCreateOpm opm, String workOrderId) {
        if (ObjNullUtils.isNull(opm.getEvsIdList())) {
            return;
        }
        //先拉回ecs列表
        List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setIds(opm.getEvsIdList())
                .setType(ProductTypeEnum.EVS.getCode()));
        //查询对应ecs需要关联回收的产品比如它的EVS 和EIP
        resourceDetailDTOList.forEach(dto -> {
            //先锁定资源
            resourceDetailManager.updateRecoveryTypeByIds(Collections.singletonList(dto.getId()), RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
            RecoveryEvsModel recoveryEvsModel = new RecoveryEvsModel();
            recoveryEvsModel.setDataDiskId(dto.getDeviceId());
            recoveryEvsModel.setDataDiskName(dto.getDeviceName());
            recoveryEvsModel.setDataDisk(dto.getDataDisk());
            recoveryEvsModel.setVmId(dto.getVmId());
            recoveryEvsModel.setVmName(dto.getEcsName());
            recoveryEvsModel.setMountVm("是".equals(dto.getMountOrNot()));
            fillCommonParam(recoveryEvsModel, dto, opm);
            long productOrderId = IdUtil.getSnowflake().nextId();
            recoveryEvsModel.setProductOrderId(productOrderId);
            //---------------------创建--------------------------------------
            RecoveryWorkOrderProductDTO product = new RecoveryWorkOrderProductDTO();
            product.setId(productOrderId);
            product.setProductType(ProductTypeEnum.EVS.getCode());
            product.setWorkOrderId(workOrderId);
            product.setHcmStatus(DisDimensionStatusEnum.WAIT.getCode());
            product.setRecoveryStatus(RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType().toString());
            product.setPropertySnapshot(JSON.toJSONString(recoveryEvsModel));
            product.setParentProductId(0L);
            product.setResourceDetailId(dto.getId().toString());
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            productManager.insert(product);
        });
    }

    private void fillCreateEip(RecoveryWorkOrderCreateOpm opm, String workOrderId) {
        if (ObjNullUtils.isNull(opm.getEipIdList())) {
            return;
        }
        //先拉回eip列表
        List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setIds(opm.getEipIdList())
                .setType(ProductTypeEnum.EIP.getCode()));

        resourceDetailDTOList.forEach(dto -> {
            //先锁定资源
            resourceDetailManager.updateRecoveryTypeByIds(Collections.singletonList(dto.getId()), RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
            RecoveryEipModel recoveryEipModel = new RecoveryEipModel();
            recoveryEipModel.setBandwidth(dto.getBandWidth());
            recoveryEipModel.setEip(dto.getEip());
            recoveryEipModel.setEipId(dto.getDeviceId());
            recoveryEipModel.setEipName(dto.getDeviceName());
            recoveryEipModel.setVmId(dto.getVmId());
            recoveryEipModel.setVmName(dto.getEcsName());
            recoveryEipModel.setRelatedDeviceId(dto.getRelatedDeviceId());
            recoveryEipModel.setRelatedDeviceType(dto.getRelatedDeviceType());
            recoveryEipModel.setRelatedDeviceName(dto.getRelatedDeviceName());
            fillCommonParam(recoveryEipModel, dto, opm);
            long productOrderId = IdUtil.getSnowflake().nextId();
            recoveryEipModel.setProductOrderId(productOrderId);
            //---------------------创建--------------------------------------
            RecoveryWorkOrderProductDTO product = new RecoveryWorkOrderProductDTO();
            product.setId(productOrderId);
            product.setProductType(ProductTypeEnum.EIP.getCode());
            product.setWorkOrderId(workOrderId);
            product.setHcmStatus(DisDimensionStatusEnum.WAIT.getCode());
            product.setRecoveryStatus(RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType().toString());
            product.setPropertySnapshot(JSON.toJSONString(recoveryEipModel));
            product.setParentProductId(0L);
            product.setResourceDetailId(dto.getId().toString());
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            productManager.insert(product);
        });
    }


    private void fillCreateRedis(RecoveryWorkOrderCreateOpm opm, String workOrderId) {
        if (ObjNullUtils.isNull(opm.getRedisIdList())) {
            return;
        }
        //先拉回ecs列表
        List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setIds(opm.getGcsIdList())
                                                                                                            .setType(ProductTypeEnum.REDIS.getCode()));
        //查询对应ecs需要关联回收的产品比如它的EVS 和EIP
        resourceDetailDTOList.forEach(dto -> {
            //先锁定资源
            resourceDetailManager.updateRecoveryTypeByIds(Collections.singletonList(dto.getId()), RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
            RecoveryEcsModel recoveryEcsModel = new RecoveryEcsModel();
            recoveryEcsModel.setVmId(dto.getDeviceId());
            recoveryEcsModel.setVmName(dto.getDeviceName());
            recoveryEcsModel.setOsVersion(dto.getOsVersion());
            recoveryEcsModel.setSpec(dto.getSpec());
            recoveryEcsModel.setSysDisk(dto.getSysDisk());
            recoveryEcsModel.setCmdbId(dto.getConfigId());
            recoveryEcsModel.setManageIp(dto.getManageIp());
            recoveryEcsModel.setIp(dto.getIp());
            recoveryEcsModel.setResourceId(dto.getResourceId());
            List<RecoveryEvsModel> volumeModelList = Lists.newArrayList();
            //evs部分
            String volumeId = dto.getVolumeId();
            String dataDisk = dto.getDataDisk();
            if (ObjNullUtils.isNotNull(volumeId) && ObjNullUtils.isNotNull(dataDisk)) {
                //逗号拆分volumeId 和 dataDisk 变成list
                List<String> volumeIds = Arrays.asList(volumeId.split(","));
                List<String> dataDisks = Arrays.asList(dataDisk.split(","));
                //确保两个列表长度一致
                Precondition.checkArgument(volumeIds.size() == dataDisks.size(), "数据盘ID和数据盘大小数量不一致，请检查数据");
                if (!volumeIds.isEmpty()) {
                    resourceDetailManager.updateRecoveryTypeByDeviceIds(volumeIds, RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
                }
                for (int i = 0; i < volumeIds.size(); i++) {
                    RecoveryEvsModel recoveryEvsModel = new RecoveryEvsModel();
                    recoveryEvsModel.setDataDiskId(volumeIds.get(i));
                    recoveryEvsModel.setDataDisk(dataDisks.get(i));
                    recoveryEvsModel.setProductOrderId(IdUtil.getSnowflake().nextId());
                    volumeModelList.add(recoveryEvsModel);
                }
            }
            recoveryEcsModel.setEvsModelList(volumeModelList);
            //eip部分 eip不存在多个的情况
            if (ObjNullUtils.isNotNull(dto.getEip())) {
                resourceDetailManager.updateRecoveryTypeByDeviceIds(Collections.singletonList(dto.getEipId()), RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
                RecoveryEipModel recoveryEipModel = new RecoveryEipModel();
                recoveryEipModel.setProductOrderId(IdUtil.getSnowflake().nextId());
                recoveryEipModel.setEipId(dto.getEipId());
                recoveryEipModel.setEip(dto.getEip());
                recoveryEipModel.setBandwidth(dto.getBandWidth());
                recoveryEcsModel.setEipModel(recoveryEipModel);
            }
            fillCommonParam(recoveryEcsModel, dto, opm);
            long productOrderId = IdUtil.getSnowflake().nextId();
            recoveryEcsModel.setProductOrderId(productOrderId);
            RecoveryWorkOrderProductDTO mainProduct = createEcsProduct(recoveryEcsModel, productOrderId, ProductTypeEnum.GCS.getCode(), workOrderId,opm.getSyncRecoveryIdList());
            productManager.insert(mainProduct);
            // 创建关联子产品 如果不是ESC和GCS不用创建子产品
            if (ObjNullUtils.isNotNull(volumeModelList)) {
                createEvsProduct(volumeModelList, mainProduct.getId(), workOrderId);
            }
            if (ObjNullUtils.isNotNull(recoveryEcsModel.getEipModel())) {
                createEipProduct(Collections.singletonList(recoveryEcsModel.getEipModel()), mainProduct.getId(), workOrderId);
            }
        });
    }

    private void fillCreateMysql(RecoveryWorkOrderCreateOpm opm, String workOrderId) {
        if (ObjNullUtils.isNull(opm.getMysqlIdList())) {
            return;
        }
        //先拉回ecs列表
        List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setIds(opm.getGcsIdList())
                                                                                                            .setType(ProductTypeEnum.MYSQL.getCode()));
        //查询对应ecs需要关联回收的产品比如它的EVS 和EIP
        resourceDetailDTOList.forEach(dto -> {
            //先锁定资源
            resourceDetailManager.updateRecoveryTypeByIds(Collections.singletonList(dto.getId()), RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
            RecoveryEcsModel recoveryEcsModel = new RecoveryEcsModel();
            recoveryEcsModel.setVmId(dto.getDeviceId());
            recoveryEcsModel.setVmName(dto.getDeviceName());
            recoveryEcsModel.setOsVersion(dto.getOsVersion());
            recoveryEcsModel.setSpec(dto.getSpec());
            recoveryEcsModel.setSysDisk(dto.getSysDisk());
            recoveryEcsModel.setCmdbId(dto.getConfigId());
            recoveryEcsModel.setManageIp(dto.getManageIp());
            recoveryEcsModel.setIp(dto.getIp());
            recoveryEcsModel.setResourceId(dto.getResourceId());
            List<RecoveryEvsModel> volumeModelList = Lists.newArrayList();
            //evs部分
            String volumeId = dto.getVolumeId();
            String dataDisk = dto.getDataDisk();
            if (ObjNullUtils.isNotNull(volumeId) && ObjNullUtils.isNotNull(dataDisk)) {
                //逗号拆分volumeId 和 dataDisk 变成list
                List<String> volumeIds = Arrays.asList(volumeId.split(","));
                List<String> dataDisks = Arrays.asList(dataDisk.split(","));
                //确保两个列表长度一致
                Precondition.checkArgument(volumeIds.size() == dataDisks.size(), "数据盘ID和数据盘大小数量不一致，请检查数据");
                if (!volumeIds.isEmpty()) {
                    resourceDetailManager.updateRecoveryTypeByDeviceIds(volumeIds, RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
                }
                for (int i = 0; i < volumeIds.size(); i++) {
                    RecoveryEvsModel recoveryEvsModel = new RecoveryEvsModel();
                    recoveryEvsModel.setDataDiskId(volumeIds.get(i));
                    recoveryEvsModel.setDataDisk(dataDisks.get(i));
                    recoveryEvsModel.setProductOrderId(IdUtil.getSnowflake().nextId());
                    volumeModelList.add(recoveryEvsModel);
                }
            }
            recoveryEcsModel.setEvsModelList(volumeModelList);
            //eip部分 eip不存在多个的情况
            if (ObjNullUtils.isNotNull(dto.getEip())) {
                resourceDetailManager.updateRecoveryTypeByDeviceIds(Collections.singletonList(dto.getEipId()), RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
                RecoveryEipModel recoveryEipModel = new RecoveryEipModel();
                recoveryEipModel.setProductOrderId(IdUtil.getSnowflake().nextId());
                recoveryEipModel.setEipId(dto.getEipId());
                recoveryEipModel.setEip(dto.getEip());
                recoveryEipModel.setBandwidth(dto.getBandWidth());
                recoveryEcsModel.setEipModel(recoveryEipModel);
            }
            fillCommonParam(recoveryEcsModel, dto, opm);
            long productOrderId = IdUtil.getSnowflake().nextId();
            recoveryEcsModel.setProductOrderId(productOrderId);
            RecoveryWorkOrderProductDTO mainProduct = createEcsProduct(recoveryEcsModel, productOrderId, ProductTypeEnum.GCS.getCode(), workOrderId,opm.getSyncRecoveryIdList());
            productManager.insert(mainProduct);
            // 创建关联子产品 如果不是ESC和GCS不用创建子产品
            if (ObjNullUtils.isNotNull(volumeModelList)) {
                createEvsProduct(volumeModelList, mainProduct.getId(), workOrderId);
            }
            if (ObjNullUtils.isNotNull(recoveryEcsModel.getEipModel())) {
                createEipProduct(Collections.singletonList(recoveryEcsModel.getEipModel()), mainProduct.getId(), workOrderId);
            }
        });
    }

    private void fillCreateGcs(RecoveryWorkOrderCreateOpm opm, String workOrderId) {
        if (ObjNullUtils.isNull(opm.getGcsIdList())) {
            return;
        }
        //先拉回ecs列表
        List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setIds(opm.getGcsIdList())
                .setType(ProductTypeEnum.GCS.getCode()));
        //查询对应ecs需要关联回收的产品比如它的EVS 和EIP
        resourceDetailDTOList.forEach(dto -> {
            //先锁定资源
            resourceDetailManager.updateRecoveryTypeByIds(Collections.singletonList(dto.getId()), RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
            RecoveryEcsModel recoveryEcsModel = new RecoveryEcsModel();
            recoveryEcsModel.setVmId(dto.getDeviceId());
            recoveryEcsModel.setVmName(dto.getDeviceName());
            recoveryEcsModel.setOsVersion(dto.getOsVersion());
            recoveryEcsModel.setSpec(dto.getSpec());
            recoveryEcsModel.setSysDisk(dto.getSysDisk());
            recoveryEcsModel.setCmdbId(dto.getConfigId());
            recoveryEcsModel.setManageIp(dto.getManageIp());
            recoveryEcsModel.setIp(dto.getIp());
            recoveryEcsModel.setResourceId(dto.getResourceId());
            List<RecoveryEvsModel> volumeModelList = Lists.newArrayList();
            //evs部分
            String volumeId = dto.getVolumeId();
            String dataDisk = dto.getDataDisk();
            if (ObjNullUtils.isNotNull(volumeId) && ObjNullUtils.isNotNull(dataDisk)) {
                //逗号拆分volumeId 和 dataDisk 变成list
                List<String> volumeIds = Arrays.asList(volumeId.split(","));
                List<String> dataDisks = Arrays.asList(dataDisk.split(","));
                //确保两个列表长度一致
                Precondition.checkArgument(volumeIds.size() == dataDisks.size(), "数据盘ID和数据盘大小数量不一致，请检查数据");
                if (!volumeIds.isEmpty()) {
                    resourceDetailManager.updateRecoveryTypeByDeviceIds(volumeIds, RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
                }
                for (int i = 0; i < volumeIds.size(); i++) {
                    RecoveryEvsModel recoveryEvsModel = new RecoveryEvsModel();
                    recoveryEvsModel.setDataDiskId(volumeIds.get(i));
                    recoveryEvsModel.setDataDisk(dataDisks.get(i));
                    recoveryEvsModel.setProductOrderId(IdUtil.getSnowflake().nextId());
                    volumeModelList.add(recoveryEvsModel);
                }
            }
            recoveryEcsModel.setEvsModelList(volumeModelList);
            //eip部分 eip不存在多个的情况
            if (ObjNullUtils.isNotNull(dto.getEip())) {
                resourceDetailManager.updateRecoveryTypeByDeviceIds(Collections.singletonList(dto.getEipId()), RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
                RecoveryEipModel recoveryEipModel = new RecoveryEipModel();
                recoveryEipModel.setProductOrderId(IdUtil.getSnowflake().nextId());
                recoveryEipModel.setEipId(dto.getEipId());
                recoveryEipModel.setEip(dto.getEip());
                recoveryEipModel.setBandwidth(dto.getBandWidth());
                recoveryEcsModel.setEipModel(recoveryEipModel);
            }
            fillCommonParam(recoveryEcsModel, dto, opm);
            long productOrderId = IdUtil.getSnowflake().nextId();
            recoveryEcsModel.setProductOrderId(productOrderId);
            RecoveryWorkOrderProductDTO mainProduct = createEcsProduct(recoveryEcsModel, productOrderId, ProductTypeEnum.GCS.getCode(), workOrderId,opm.getSyncRecoveryIdList());
            productManager.insert(mainProduct);
            // 创建关联子产品 如果不是ESC和GCS不用创建子产品
            if (ObjNullUtils.isNotNull(volumeModelList)) {
                createEvsProduct(volumeModelList, mainProduct.getId(), workOrderId);
            }
            if (ObjNullUtils.isNotNull(recoveryEcsModel.getEipModel())) {
                createEipProduct(Collections.singletonList(recoveryEcsModel.getEipModel()), mainProduct.getId(), workOrderId);
            }
        });
    }

    private void fillCreateEcs(RecoveryWorkOrderCreateOpm opm, String workOrderId) {
        if (ObjNullUtils.isNull(opm.getEcsIdList())) {
            return;
        }
        //先拉回ecs列表
        List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setIds(opm.getEcsIdList())
                .setType(ProductTypeEnum.ECS.getCode()));
        //查询对应ecs需要关联回收的产品比如它的EVS 和EIP
        resourceDetailDTOList.forEach(dto -> {
            //先锁定资源
            resourceDetailManager.updateRecoveryTypeByIds(Collections.singletonList(dto.getId()), RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
            RecoveryEcsModel recoveryEcsModel = new RecoveryEcsModel();
            recoveryEcsModel.setVmId(dto.getDeviceId());
            recoveryEcsModel.setVmName(dto.getDeviceName());
            recoveryEcsModel.setOsVersion(dto.getOsVersion());
            recoveryEcsModel.setSpec(dto.getSpec());
            recoveryEcsModel.setSysDisk(dto.getSysDisk());
            recoveryEcsModel.setCmdbId(dto.getConfigId());
            recoveryEcsModel.setManageIp(dto.getManageIp());
            recoveryEcsModel.setIp(dto.getIp());
            recoveryEcsModel.setResourceId(dto.getResourceId());
            List<RecoveryEvsModel> volumeModelList = Lists.newArrayList();
            //evs部分
            String volumeId = dto.getVolumeId();
            String dataDisk = dto.getDataDisk();
            if (ObjNullUtils.isNotNull(volumeId) && ObjNullUtils.isNotNull(dataDisk)) {
                //逗号拆分volumeId 和 dataDisk 变成list
                List<String> volumeIds = Arrays.asList(volumeId.split(","));
                List<String> dataDisks = Arrays.asList(dataDisk.split(","));
                //确保两个列表长度一致
                Precondition.checkArgument(volumeIds.size() == dataDisks.size(), "数据盘ID和数据盘大小数量不一致，请检查数据");
                if (!volumeIds.isEmpty()) {
                    resourceDetailManager.updateRecoveryTypeByDeviceIds(volumeIds, RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
                }
                for (int i = 0; i < volumeIds.size(); i++) {
                    RecoveryEvsModel recoveryEvsModel = new RecoveryEvsModel();
                    recoveryEvsModel.setDataDiskId(volumeIds.get(i));
                    recoveryEvsModel.setDataDisk(dataDisks.get(i));
                    recoveryEvsModel.setProductOrderId(IdUtil.getSnowflake().nextId());
                    volumeModelList.add(recoveryEvsModel);
                }
            }
            recoveryEcsModel.setEvsModelList(volumeModelList);
            //eip部分 eip不存在多个的情况
            if (ObjNullUtils.isNotNull(dto.getEip())) {
                resourceDetailManager.updateRecoveryTypeByDeviceIds(Collections.singletonList(dto.getEipId()), RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType());
                RecoveryEipModel recoveryEipModel = new RecoveryEipModel();
                recoveryEipModel.setProductOrderId(IdUtil.getSnowflake().nextId());
                recoveryEipModel.setEipId(dto.getEipId());
                recoveryEipModel.setEip(dto.getEip());
                recoveryEipModel.setBandwidth(dto.getBandWidth());
                recoveryEcsModel.setEipModel(recoveryEipModel);
            }
            fillCommonParam(recoveryEcsModel, dto, opm);
            long productOrderId = IdUtil.getSnowflake().nextId();
            recoveryEcsModel.setProductOrderId(productOrderId);
            RecoveryWorkOrderProductDTO mainProduct = createEcsProduct(recoveryEcsModel, productOrderId, ProductTypeEnum.ECS.getCode(), workOrderId,opm.getSyncRecoveryIdList());
            productManager.insert(mainProduct);
            // 创建关联子产品 如果不是ESC和GCS不用创建子产品
            if (ObjNullUtils.isNotNull(volumeModelList)) {
                createEvsProduct(volumeModelList, mainProduct.getId(), workOrderId);
            }
            if (ObjNullUtils.isNotNull(recoveryEcsModel.getEipModel())) {
                createEipProduct(Collections.singletonList(recoveryEcsModel.getEipModel()), mainProduct.getId(), workOrderId);
            }
        });
    }


    private void createEvsProduct(List<RecoveryEvsModel> models, Long parentId, String workOrderId) {
        if (ObjNullUtils.isNull(models)) return;

        models.stream().map(model -> {
            RecoveryWorkOrderProductDTO product = new RecoveryWorkOrderProductDTO();
            product.setId(model.getProductOrderId());
            product.setProductType(ProductTypeEnum.EVS.getCode());
            product.setWorkOrderId(workOrderId);
            product.setHcmStatus(DisDimensionStatusEnum.WAIT.getCode());
            product.setRecoveryStatus(RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType().toString());
            product.setPropertySnapshot(JSON.toJSONString(model));
            product.setParentProductId(parentId);
            ResourceDetailDTO detailEvs = resourceDetailManager.getByDeviceId(model.getDataDiskId());
            product.setResourceDetailId(detailEvs.getId().toString());
            return product;
        }).forEach(productManager::insert);
    }

    private void createEipProduct(List<RecoveryEipModel> models, Long parentId, String workOrderId) {
        if (ObjNullUtils.isNull(models)) return;
        models.stream().map(model -> {
            RecoveryWorkOrderProductDTO product = new RecoveryWorkOrderProductDTO();
            product.setId(model.getProductOrderId());
            product.setProductType(ProductTypeEnum.EIP.getCode());
            product.setWorkOrderId(workOrderId);
            product.setHcmStatus(DisDimensionStatusEnum.WAIT.getCode());
            product.setRecoveryStatus(RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType().toString());
            product.setPropertySnapshot(JSON.toJSONString(model));
            product.setParentProductId(parentId);
            ResourceDetailDTO detailEip = resourceDetailManager.getByDeviceId(model.getEipId());
            product.setResourceDetailId(detailEip.getId().toString());
            return product;
        }).forEach(productManager::insert);
    }


    private RecoveryWorkOrderProductDTO createEcsProduct(RecoveryEcsModel model, Long mainId, String productType, String workOrderId,List<Long> syncRecoveryIdList) {
        RecoveryWorkOrderProductDTO product = new RecoveryWorkOrderProductDTO();
        product.setId(mainId);
        product.setProductType(productType);
        product.setSyncRecovery(syncRecoveryIdList.contains(model.getResourceDetailId()));
        product.setWorkOrderId(workOrderId);
        product.setRecoveryStatus(RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType().toString());
        product.setGid(UuidUtil.getGid(productType));
        product.setHcmStatus(DisDimensionStatusEnum.WAIT.getCode());
        product.setRecoveryStatus(RecoveryStatusEnum.RESOURCE_TO_BE_RECOVERED.getType().toString());
        product.setSubOrderId(IdUtil.getSnowflake().nextId());
        product.setPropertySnapshot(JSON.toJSONString(model));
        product.setResourceDetailId(model.getResourceDetailId().toString());
        product.setCmdbId(model.getCmdbId());
        return product;
    }


    private void fillCommonParam(BaseReconveryProductModel baseModel, ResourceDetailDTO resourceDetailDTO, RecoveryWorkOrderCreateOpm opm) {
        RegionDTO regionDTO = new RegionDTO();
        if (ObjNullUtils.isNotNull(resourceDetailDTO.getResourcePoolId())) {
            regionDTO = regionManager.getById(Long.valueOf(resourceDetailDTO.getResourcePoolId()));
        }
        regionManager.getById(Long.valueOf(resourceDetailDTO.getResourcePoolId()));
        baseModel.setResourceDetailId(resourceDetailDTO.getId());
        baseModel.setProductType(resourceDetailDTO.getType());
        baseModel.setAzName(resourceDetailDTO.getAzName());
        baseModel.setAzCode(resourceDetailDTO.getAzCode());
        baseModel.setAzId(resourceDetailDTO.getAzId());
        baseModel.setRegionId(regionDTO.getId().toString());
        baseModel.setRegionCode(regionDTO.getCode());
        baseModel.setRegionName(regionDTO.getName());
        baseModel.setExpireTime(resourceDetailDTO.getExpireTime());
        baseModel.setApplyTime(resourceDetailDTO.getApplyTime());
        baseModel.setTenantId(resourceDetailDTO.getTenantId());
        baseModel.setTenantName(resourceDetailDTO.getTenantName());
        baseModel.setBusinessSystemName(resourceDetailDTO.getBusinessSysName());
        baseModel.setBusinessSystemId(resourceDetailDTO.getBusinessSysId());
        baseModel.setDomainCode(resourceDetailDTO.getDomainCode());
        baseModel.setDomainName(resourceDetailDTO.getDomainName());
        baseModel.setBillId(opm.getBillId());
        baseModel.setCustomNo(opm.getCustomNo());
    }

    @Override
    public void cancel(String workOrderId) {
        RecoveryWorkOrderDTO orderDTO = reconveryWorkOrderManager.getById(workOrderId);
        Precondition.checkArgument(orderDTO, "当前工单不存在");
        UserCenterUserDTO currentUser = userHelper.getCurrentUser();
        // 进行撤销工单权限校验
        preCheckCancelAuthority(orderDTO, currentUser);
        baseActivity.stop(orderDTO, currentUser.getId(), ActivityEnum.ActivityProcessEnum.RESOURCE_RECOVERY_PROCESS);
        // 更新工单状态
        LocalDateTime now = LocalDateTime.now();
        workOrderAuthLogManager.createWorkOrderAuthLog(new WorkOrderAuthLogDTO().setWorkOrderId(workOrderId)
                .setCreateTime(now).setModifyTime(now).setProcessInstanceId(orderDTO.getActivitiId())
                .setAdvice(ActivitiStatusEnum.ORDER_CANCEL.getNodeRemark())
                .setAuditNodeCode(ActivitiStatusEnum.ORDER_CANCEL.getNode())
                .setAuditNodeName(ActivitiStatusEnum.ORDER_CANCEL.getNodeRemark()).setUserId(orderDTO.getCreatedBy())
                .setUserName(currentUser.getUserName()).setUserPhone(currentUser.getPhone())
                .setUserEmail(currentUser.getUserEmail())
                .setAuditResult(ActivitiStatusEnum.ORDER_CANCEL.getNodeRemark()));

        orderDTO.setCurrentNodeCode(ActivitiStatusEnum.ORDER_CANCEL.getNode());
        orderDTO.setCurrentNodeName(ActivitiStatusEnum.ORDER_CANCEL.getNodeRemark());
        orderDTO.setWorkOrderEndTime(now);
        orderDTO.setCurrentNodeStartTime(now);
        updateOrderStatus(orderDTO, userHelper.getCurrentUserId(), OrderStatusEnum.CLOSE.getCode());
        afterCancelUpdateData(workOrderId);
    }

    private void afterCancelUpdateData(String workOrderId) {
        List<RecoveryWorkOrderProductDTO> orderProductDTOS = recoveryWorkOrderProductManager.listByWorkOrderId(workOrderId);
        //取出统一的产品id
        List<String> resDeatils = StreamUtils.mapArrayFilterNull(orderProductDTOS, RecoveryWorkOrderProductDTO::getResourceDetailId);
        List<Long> resIds = resDeatils.stream().filter(id -> !id.startsWith("vpc")).filter(id -> !id.startsWith("net"))
                .map(Long::valueOf).collect(Collectors.toList());
        //如果resIds不为空 更新状态
        if (CollectionUtil.isNotEmpty(resIds)) {
            resourceDetailManager.updateRecoveryTypeByIds(resIds, RecoveryStatusEnum.ORDER_TO_BE_RECOVERED.getType());
        }
        List<String> vpcIds = resDeatils.stream().filter(id -> id.startsWith("vpc")).filter(ObjNullUtils::isNotNull)
                .map(String::valueOf).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(vpcIds)) {
            vpcOrderMapper.updateRecoveryStatus(vpcIds, null, RecoveryStatusEnum.ORDER_TO_BE_RECOVERED.getType());
        }
        List<String> networkIds = resDeatils.stream().filter(id -> id.startsWith("net")).filter(ObjNullUtils::isNotNull)
                .map(String::valueOf).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(networkIds)) {
            networkOrderMapper.updateRecoveryStatus(networkIds, null, RecoveryStatusEnum.ORDER_TO_BE_RECOVERED.getType());
        }
    }


    @Override
    @Transactional
    public void audit(RecoveryAuditWorkOrderDTO auditDTO) {
        UserCenterUserDTO currentUser = userHelper.getCurrentUser();
        Precondition.checkArgument(currentUser, "获取当前用户信息失败");
        RecoveryWorkOrderDTO orderDTO = reconveryWorkOrderManager.getById(auditDTO.getOrderId());
        Precondition.checkArgument(orderDTO, "当前工单不存在");
        ActivityTaskVo taskVo = baseActivity.taskNodes(orderDTO.getActivitiId(), ActivityEnum.ActivityProcessEnum.RESOURCE_RECOVERY_PROCESS);
        Precondition.checkArgument(taskVo, "已无审核流程节点！");
        orderDTO.setCurrentNodeCode(taskVo.getCurrentTask());
        // 审核状态
        ActivityEnum.ActivityStatusEnum auditStatus = ActivityEnum.ActivityStatusEnum.getByCode(auditDTO.getActiviteStatus());
        ActivityEnum.ActivityProcessEnum activityProcessEnum = ActivityEnum.ActivityProcessEnum.getByCode(orderDTO.getActiviteKey());
        ConfigTypeEnum configTypeEnum = ConfigTypeEnum.findByActivityEnum(activityProcessEnum);
        OacConfig oldTaskCode = configService.getByCode(taskVo.getCurrentTask(), configTypeEnum.getCode(), null, AuthorityCodeEnum.USER_TASK.code());

        String orderStatus = null;
        orderDTO.setCurrentNodeStartTime(LocalDateTime.now());
        switch (auditStatus) {
            case PASS:
                if (!ActivitiStatusEnum.TENANT_TASK.getNode().equals(taskVo.getCurrentTask())) {
                    orderStatus = OrderStatusEnum.EXAMINING.getCode();
                    insertAuthLog(taskVo.getCurrentTask(), orderDTO, auditDTO.getAuditAdvice(), OrderLogStatusEnum.PASS.getCode());
                } else {
                    //到这里已经是工单完成节点了
                    // todo 业务系统下线处理
                    updateUserAppMessage(orderDTO);
                    orderDTO.setWorkOrderEndTime(LocalDateTime.now());
                    insertAuthLog(taskVo.getCurrentTask(), orderDTO, auditDTO.getAuditAdvice(), OrderLogStatusEnum.PASS.getCode());
                    orderStatus = OrderStatusEnum.END.getCode();
                }
                break;
            case REJECT:
                insertAuthLog(null, orderDTO, auditDTO.getAuditAdvice(), OrderLogStatusEnum.REJECT.getCode());
                orderStatus = OrderStatusEnum.findDescByAuditStatus(auditStatus).getCode();
                break;
            default:
        }
        baseActivity.complete(orderDTO, auditStatus, orderDTO.getActivitiId(), auditDTO.getAuditAdvice(), activityProcessEnum, currentUser.getId(), oldTaskCode,
                auditDTO.getNodeCode());

        taskVo = baseActivity.taskNodes(orderDTO.getActivitiId(), ActivityEnum.ActivityProcessEnum.RESOURCE_RECOVERY_PROCESS);
        orderDTO.setCurrentNodeCode(taskVo.getCurrentTask());
        orderDTO.setCurrentNodeName(com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum.getNodeRemarkByNode(taskVo.getCurrentTask(), null));
        updateOrderStatus(orderDTO, currentUser.getId(), orderStatus);
    }

    private void updateUserAppMessage(RecoveryWorkOrderDTO orderDTO) {
        List<ModuleOfflineDTO> list = resourceDetailManager.selectNotUserModule(orderDTO.getBusinessSystemId());
        networkCommonService.deleteUserModule(list, orderDTO.getBusinessSystemId());
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        iUserSelectStrategy.updateUserAppMessage(list, orderDTO);
    }

    @Override
    public PageResult<RecoveryWorkOrderDTO> page(RecoveryWorkOrderQuery query, Long currentUserId) {
        if (QueryParamCheckUtil.containsPercentage(query)) {
            return new PageResult<>();
        }

        // 判断查询的是待审批 还是已审批 还是驳回的工单
        UserCenterUserDTO user = userService.getUserById(currentUserId);
        List<String> ruleCodeList = UserServiceExt.processRoleData_(user.getOacRoles());
        switch (query.getApprovalCode()) {
            case "pending":
                //关联的运行时任务表 需要当前用户的角色code和用户id
                query.setRoleCodeList(ruleCodeList);
                query.setUserId(currentUserId.toString());
                query.setApprovalCode("pending");
                break;
            case "approved":
                query.setUserId(currentUserId.toString());
                query.setApprovalCode("approved");
                query.setRoleCodeList(ruleCodeList);
                break;
            case "rejected":
                query.setUserId(currentUserId.toString());
                query.setApprovalCode("rejected");
                query.setRoleCodeList(ruleCodeList);
                // 需要查询用户历史里有过审批拒绝相关的流程id
                List<String> processIds =
                        workOrderAuthLogManager.groupByProcessInstanceId(new WorkOrderAuthLogQuery().setUserId(currentUserId)
                                .setAuditResult(OrderStatusEnum.REJECT.getCode()));
                //如果processIds为空则直接返回空直接返回空
                if (CollectionUtil.isEmpty(processIds)) {
                    return new PageResult<>();
                }
                query.setProcessIds(processIds);
                break;
            default:
                break;
        }

        return reconveryWorkOrderManager.page(query);
    }


    @Override
    public AuditCountVo orderCount(RecoveryWorkOrderQuery orderQuery) {
        Long userId = Long.valueOf(orderQuery.getUserId());
        orderQuery.setApprovalCode(ApprovalTypeEnum.TODO_TYPE.getType());
        PageResult<RecoveryWorkOrderDTO> page = page(orderQuery, userId);
        Long todoSize = Optional.ofNullable(page.getTotal()).orElse(0L);
        orderQuery.setApprovalCode(ApprovalTypeEnum.DONE_TYPE.getType());
        page = page(orderQuery, userId);
        Long doneSize = Optional.ofNullable(page.getTotal()).orElse(0L);
        orderQuery.setApprovalCode(ApprovalTypeEnum.REJECT_TYPE.getType());
        page = page(orderQuery, userId);
        Long rejectSize = Optional.ofNullable(page.getTotal()).orElse(0L);
        return new AuditCountVo().setPendingCount(todoSize).setApprovedCount(doneSize).setRejectedCount(rejectSize);
    }

    @Override
    public ActivityTaskVo getTaskNodes(String orderId) {
        RecoveryWorkOrderDTO orderDTO = reconveryWorkOrderManager.getById(orderId);
        Precondition.checkArgument(orderDTO, "工单不存在");
        ActivityTaskVo taskNode = baseActivity.taskNodes(orderDTO.getActivitiId(), ActivityEnum.ActivityProcessEnum.RESOURCE_RECOVERY_PROCESS);
        if (taskNode != null) {
            String remark = com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum.getNodeRemarkByNode(taskNode.getCurrentTask(), null);
            taskNode.setCurrentTaskName(remark);
            List<InnerTask> tasks = taskNode.getAllTasks();
            tasks.forEach(task -> {
                String taskName = com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum.getNodeRemarkByNode(task.getTask(), null);
                task.setTaskName(taskName);
            });
        }
        return taskNode;
    }

    @Override
    public void getNextTaskNodes(String orderId, List<WorkOrderAuthLogDTO> authLogDTOS, String currentTaskName) {
        RecoveryWorkOrderDTO orderDTO = reconveryWorkOrderManager.getById(orderId);
        Precondition.checkArgument(orderDTO, "工单不存在");
        TaskNodeDTO nextTaskNode = baseActivity.nextTaskNodes(orderDTO.getActivitiId(), ActivityEnum.ActivityProcessEnum.RESOURCE_RECOVERY_PROCESS);
        if (nextTaskNode == null) {
            return;
        }
        String assignee = nextTaskNode.getAssignee();
        if(assignee == null){
            return;
        }
        String name = "";
        String email = "";
        if (assignee.matches("[-+]?\\d+(\\.\\d+)?")) {
            UserCenterUserDTO userCenterUserDTO = userService.getUserById(Long.valueOf(assignee));
            name = userCenterUserDTO.getUserName();
            email = userCenterUserDTO.getUserEmail();
        } else {
            assignee = AuthorityCodeEnum.OPERATION_GROUP.code();
            List<UserCenterUserDTO> list = userService.getUserListByRoleCode(assignee);
            name = list.stream().map(UserCenterUserDTO::getUserName).collect(Collectors.joining(","));
            email = list.stream().map(UserCenterUserDTO::getUserEmail).collect(Collectors.joining(","));
        }
        WorkOrderAuthLogDTO workOrderAuthLogDTO = new WorkOrderAuthLogDTO();
        workOrderAuthLogDTO.setWorkOrderId(orderId);
        workOrderAuthLogDTO.setUserName(name);
        workOrderAuthLogDTO.setUserEmail(email);
        workOrderAuthLogDTO.setAuditNodeName(currentTaskName);
        workOrderAuthLogDTO.setAuditNodeCode(nextTaskNode.getBpmnName());
        workOrderAuthLogDTO.setAuditResultDesc("待审核");
        authLogDTOS.add(workOrderAuthLogDTO);
    }

    @Override
    public void selectResourceDetails(String workOrderId, RecoveryWorkOrderDetailDTO detailDTO) {
        List<RecoveryWorkOrderProductDTO> productDTOS = recoveryWorkOrderProductManager.listByWorkOrderId(workOrderId);
        List<Long> emptyResourceProductIds = productDTOS.stream()
                .filter(productDTO -> productDTO.getResourceDetailId() ==
                        null).map(RecoveryWorkOrderProductDTO::getId).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(emptyResourceProductIds)) {
            log.error("回收工单产品id集：{}，对应的记录中resourceDetailId为空", emptyResourceProductIds);
        }
        // 获取产品列表中的resourceDetailId资源详情id值
        Map<String, List<RecoveryWorkOrderProductDTO>> productTypeMap = productDTOS.stream()
                .filter(productDTO -> productDTO.getResourceDetailId() != null).filter(productDTO ->
                        productDTO.getParentProductId() == 0)
                .collect(Collectors.groupingBy(RecoveryWorkOrderProductDTO::getProductType));
        productTypeMap.forEach((type, products) -> {
            ResourceDetailQuery query = new ResourceDetailQuery();
            switch (type) {
                case "ecs":
                    query = processResource(products);
                    detailDTO.setEcsList(resourceDetailManager.list(query));
                    break;
                case "gcs":
                    query = processResource(products);
                    detailDTO.setGcsList(resourceDetailManager.list(query));
                    break;
                case "evs":
                    query = processResource(products);
                    detailDTO.setEvsList(resourceDetailManager.list(query));
                    break;
                case "slb":
                    query = processResource(products);
                    detailDTO.setSlbList(resourceDetailManager.list(query));
                    break;
                case "obs":
                    query = processResource(products);
                    detailDTO.setObsList(resourceDetailManager.list(query));
                    break;
                case "nat":
                    query = processResource(products);
                    detailDTO.setNatList(resourceDetailManager.list(query));
                    break;
                case "vpc":
                    List<VpcOrderResult> vpcList = processVpc(products);
                    detailDTO.setVpcList(vpcList);
                    break;
                case "network":
                    List<NetworkOrderResult> networkOrderResults = processNetwork(products);
                    detailDTO.setNetworkList(networkOrderResults);
                    break;
                default:
            }
        });
    }

    @Override
    public List<NetworkOrderResult> processNetwork(List<RecoveryWorkOrderProductDTO> products) {
        List<String> networkIds = products.stream().map(RecoveryWorkOrderProductDTO::getResourceDetailId)
                .collect(Collectors.toList());
        NetworkTableVo networkTableVo = new NetworkTableVo();
        networkTableVo.setIds(networkIds);
        return networkOrderMapper.selectNetworkOrderList(networkTableVo);
    }

    @Override
    public List<VpcOrderResult> processVpc(List<RecoveryWorkOrderProductDTO> products) {
        List<String> vpcIds = products.stream().map(RecoveryWorkOrderProductDTO::getResourceDetailId)
                .collect(Collectors.toList());
        VpcTableVo vpcTableVo = new VpcTableVo().setIds(vpcIds);
        vpcTableVo.setSourceType(SourceTypeEnum.STANDARD.getPrefix());
        return vpcOrderMapper.selectVpcOrderList(vpcTableVo);
    }


    private ResourceDetailQuery processResource(List<RecoveryWorkOrderProductDTO> products) {
        List<Long> ids = products.stream().map(item -> Long.parseLong(item.getResourceDetailId()))
                .collect(Collectors.toList());
        return new ResourceDetailQuery().setIds(ids);
    }

    @Override
    public List<ResourceDetailDTO> fillResourceDetailIds(List<RecoveryWorkOrderProductDTO> products) {
        List<Long> ids = products.stream().map(item -> Long.parseLong(item.getResourceDetailId()))
                .collect(Collectors.toList());
        ResourceDetailQuery query = new ResourceDetailQuery().setIds(ids);
        return resourceDetailManager.list(query);
    }

    private void insertAuthLog(String currentTask, RecoveryWorkOrderDTO orderDTO, String auditAdvice, String auditResult) {
        UserCenterUserDTO currentUser = userHelper.getCurrentUser();
        String orderStatus = currentTask;
        if (StringUtils.isEmpty(currentTask)) {
            if (OrderLogStatusEnum.REJECT.getCode().equalsIgnoreCase(auditResult)) {
                orderStatus = RecoveryOrderNodeEnum.REJECT.getCode();
            } else {
                orderStatus = RecoveryOrderNodeEnum.END.getCode();
            }
        }

        WorkOrderAuthLogDTO logDTO =
                new WorkOrderAuthLogDTO().setCreateTime(orderDTO.getCurrentNodeStartTime())
                        .setModifyTime(orderDTO.getCurrentNodeStartTime()).setWorkOrderId(orderDTO.getId())
                        .setProcessInstanceId(orderDTO.getActivitiId()).setUserId(currentUser.getId())
                        .setUserName(currentUser.getUserName()).setUserPhone(currentUser.getPhone())
                        .setUserEmail(currentUser.getUserEmail()).setAdvice(auditAdvice).setAuditNodeCode(currentTask)
                        .setAuditResult(auditResult)
                        .setAuditResultDesc(OrderLogStatusEnum.getByCode(auditResult).getDesc())
                        .setAuditNodeName(com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum.getNodeRemarkByNode(currentTask, orderStatus));
        workOrderAuthLogManager.createWorkOrderAuthLog(logDTO);
    }

    private void updateOrderStatus(RecoveryWorkOrderDTO orderDTO, Long userId, String orderStatus) {
        //1. 修改操作订单
        orderDTO.setOrderStatus(orderStatus);
        orderDTO.setModifyTime(LocalDateTime.now());
        orderDTO.setUpdatedBy(userId);
        reconveryWorkOrderManager.update(orderDTO);
    }

    private void preCheckCancelAuthority(RecoveryWorkOrderDTO orderDTO, UserCenterUserDTO currentUser) {
        Precondition.checkArgument(currentUser, "获取当前用户信息失败");
        //判断当前工单是不是当前用户创建不然不能撤销
        Precondition.checkArgument(currentUser.getId()
                .equals(orderDTO.getCreatedBy()), "当前用户不是工单创建人，不能撤销");
        // 获取工单当前审核节点
        ActivityTaskVo taskVo = baseActivity.taskNodes(orderDTO.getActivitiId(), ActivityEnum.ActivityProcessEnum.RESOURCE_RECOVERY_PROCESS);
        Precondition.checkArgument(taskVo.getCurrentTask() != null, "已无审核流程节点！");
        //如果当前节点是资源回收或租户确认才能进行撤销
        Precondition.checkArgument(Lists.newArrayList(AuthorityCodeEnum.USER_TASK.code(), AuthorityCodeEnum.BUSINESS_DEPART_LEADER2.code())
                        .contains(taskVo.getCurrentTask()),
                "当前节点不允许撤销");
    }


    /**
     * fillCheckCreate
     */
    @Override
    public void fillCheckCreate(RecoveryWorkOrderCreateOpm opm) {
        //vpc的id和云主机的id的映射关系
        ArrayListMultimap<String, String> vpcId2deviceIdMap = ArrayListMultimap.create();
        //networkId和deviceId的映射关系
        ArrayListMultimap<String, String> networkId2deviceIdMap = ArrayListMultimap.create();
        //-----------------------------------------------校验ecs部分------------------------------------------------------------------
        //如果ecsIdList不为空，校验ecsList
        if (ObjNullUtils.isNotNull(opm.getEcsIdList())) {
            //先拉回ecs列表
            List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setIds(opm.getEcsIdList())
                    .setType(ProductTypeEnum.ECS.getCode()));
            //循环校验里面的数据回收状态只有在使用中才能回收
            resourceDetailDTOList.forEach(resourceDetailDTO -> {
                boolean orderComplete = orderDataProvideService.isOrderComplete(resourceDetailDTO.getOrderId());
                Precondition.checkArgument(orderComplete, "当前资源存在未完成的工单 工单id为" + resourceDetailDTO.getOrderId());
                Precondition.checkArgument(RecoveryStatusEnum.canRecovery(resourceDetailDTO.getRecoveryStatus()),
                        "当前资源状态不可被回收 资源id为" + resourceDetailDTO.getDeviceId());
                //校验下云主机是否有绑定的eip 如果有一定不包含要在提交的eipIdList中
                String eipId = resourceDetailDTO.getEipId();
                if (ObjNullUtils.isNotNull(eipId)) {
                    //查询Eip的resourceDetailDTO
                    ResourceDetailDTO eipResourceDetailDTO = resourceDetailManager.getByDeviceId(eipId);
                    Precondition.checkArgument(eipResourceDetailDTO, "找不到需要关联回收的eip数据请联系管理员 resourceDetailDTOId为" + resourceDetailDTO.getId());
                    Precondition.checkArgument(!opm.getEipIdList()
                            .contains(eipResourceDetailDTO.getId()), "提交的弹性公网产品已经存在于云主机的回收列表中,无需重复回收");
                }
                //校验下云主机是否有绑定的evs 如果有一定要在提交的evsIdList中
                String evsId = resourceDetailDTO.getVolumeId();
                if (ObjNullUtils.isNotNull(evsId)) {
                    //evsId可能存在多个的逗号隔开
                    for (String evsIdItem : evsId.split(",")) {
                        //查询Evs的resourceDetailDTO
                        ResourceDetailDTO evsResourceDetailDTO = resourceDetailManager.getByDeviceId(evsIdItem);
                        Precondition.checkArgument(evsResourceDetailDTO, "找不到需要关联回收的evs数据请联系管理员 resourceDetailDTOId为" + resourceDetailDTO.getId());
                        Precondition.checkArgument(!opm.getEvsIdList()
                                .contains(evsResourceDetailDTO.getId()), "提交的云硬盘产品已经存在于云主机的回收列表中,无需重复回收");
                    }
                }
                String vpcId = resourceDetailDTO.getVpcId();
                //如果vpcId不为空，说明这个云主机是挂载在vpc下的
                if (ObjNullUtils.isNotNull(vpcId)) {
                    //加到vpcId2deviceIdMap中 但是ecs的这个id可能是存在多个的逗号隔开
                    List<String> vpcIdList = Arrays.asList(vpcId.split(","));
                    //循环
                    vpcIdList.forEach(vpcIdItem -> {
                        //如果前缀是是vpc开头的就是vpc 如果是net开头的就是network
                        if (vpcIdItem.startsWith("vpc")) {
                            vpcId2deviceIdMap.put(vpcIdItem, resourceDetailDTO.getDeviceId());
                        }
                        if (vpcIdItem.startsWith("net")) {
                            networkId2deviceIdMap.put(vpcIdItem, resourceDetailDTO.getDeviceId());
                        }
                    });
                }
            });
            //数量不对等
            Precondition.checkArgument(resourceDetailDTOList.size() == opm.getEcsIdList()
                    .size(), "ECS列表数据有误,请检查");
        }
        //-----------------------------------------------校验gcs部分------------------------------------------------------------------
        if (ObjNullUtils.isNotNull(opm.getGcsIdList())) {
            //先拉回ecs列表
            List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setIds(opm.getGcsIdList())
                    .setType(ProductTypeEnum.GCS.getCode()));
            //循环校验里面的数据回收状态只有在使用中才能回收
            resourceDetailDTOList.forEach(resourceDetailDTO -> {
                boolean orderComplete = orderDataProvideService.isOrderComplete(resourceDetailDTO.getOrderId());
                Precondition.checkArgument(orderComplete, "当前资源存在未完成的工单 工单id为" + resourceDetailDTO.getOrderId());
                Precondition.checkArgument(RecoveryStatusEnum.canRecovery(resourceDetailDTO.getRecoveryStatus()),
                        "当前资源状态不可被回收 资源id为" + resourceDetailDTO.getDeviceId());
                //校验下云主机是否有绑定的eip 如果有一定要在提交的eipIdList中
                String eipId = resourceDetailDTO.getEipId();
                if (ObjNullUtils.isNotNull(eipId)) {
                    //查询Eip的resourceDetailDTO
                    ResourceDetailDTO eipResourceDetailDTO = resourceDetailManager.getByDeviceId(eipId);
                    Precondition.checkArgument(eipResourceDetailDTO, "找不到需要关联回收的eip数据请联系管理员 resourceDetailDTOId为" + resourceDetailDTO.getId());
                    Precondition.checkArgument(!opm.getEipIdList()
                            .contains(eipResourceDetailDTO.getId()), "提交的弹性公网产品已经存在于CPU云主机的回收列表中,无需重复回收");
                }
                //校验下云主机是否有绑定的evs 如果有一定要在提交的evsIdList中
                String evsId = resourceDetailDTO.getVolumeId();
                if (ObjNullUtils.isNotNull(evsId)) {
                    //evsId可能存在多个的逗号隔开
                    for (String evsIdItem : evsId.split(",")) {
                        //查询Evs的resourceDetailDTO
                        ResourceDetailDTO evsResourceDetailDTO = resourceDetailManager.getByDeviceId(evsIdItem);
                        Precondition.checkArgument(evsResourceDetailDTO, "找不到需要关联回收的evs数据请联系管理员 resourceDetailDTOId为" + resourceDetailDTO.getId());
                        Precondition.checkArgument(!opm.getEvsIdList()
                                .contains(evsResourceDetailDTO.getId()), "提交的云硬盘产品已经存在于CPU云主机的回收列表中,无需重复回收");
                    }
                }
                String vpcId = resourceDetailDTO.getVpcId();
                //如果vpcId不为空，说明这个云主机是挂载在vpc下的
                if (ObjNullUtils.isNotNull(vpcId)) {
                    //加到vpcId2deviceIdMap中 但是ecs的这个id可能是存在多个的逗号隔开
                    List<String> vpcIdList = Arrays.asList(vpcId.split(","));
                    //循环
                    vpcIdList.forEach(vpcIdItem -> {
                        //如果前缀是是vpc开头的就是vpc 如果是net开头的就是network
                        if (vpcIdItem.startsWith("vpc")) {
                            vpcId2deviceIdMap.put(vpcIdItem, resourceDetailDTO.getDeviceId());
                        }
                        if (vpcIdItem.startsWith("net")) {
                            networkId2deviceIdMap.put(vpcIdItem, resourceDetailDTO.getDeviceId());
                        }
                    });
                }
            });
            //数量不对等
            Precondition.checkArgument(resourceDetailDTOList.size() == opm.getGcsIdList()
                    .size(), "GCS列表数据有误,请检查");

        }
        //-----------------------------------------------校验mysql部分------------------------------------------------------------------
        if (ObjNullUtils.isNotNull(opm.getMysqlIdList())) {
            //先拉回ecs列表
            List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setIds(opm.getMysqlIdList())
                                                                                                                .setType(ProductTypeEnum.MYSQL.getCode()));
            //循环校验里面的数据回收状态只有在使用中才能回收
            resourceDetailDTOList.forEach(resourceDetailDTO -> {
                boolean orderComplete = orderDataProvideService.isOrderComplete(resourceDetailDTO.getOrderId());
                Precondition.checkArgument(orderComplete, "当前资源存在未完成的工单 工单id为" + resourceDetailDTO.getOrderId());
                Precondition.checkArgument(RecoveryStatusEnum.canRecovery(resourceDetailDTO.getRecoveryStatus()),
                        "当前资源状态不可被回收 资源id为" + resourceDetailDTO.getDeviceId());
                //校验下云主机是否有绑定的eip 如果有一定要在提交的eipIdList中
                String eipId = resourceDetailDTO.getEipId();
                if (ObjNullUtils.isNotNull(eipId)) {
                    //查询Eip的resourceDetailDTO
                    ResourceDetailDTO eipResourceDetailDTO = resourceDetailManager.getByDeviceId(eipId);
                    Precondition.checkArgument(eipResourceDetailDTO, "找不到需要关联回收的eip数据请联系管理员 resourceDetailDTOId为" + resourceDetailDTO.getId());
                    Precondition.checkArgument(!opm.getEipIdList()
                                                   .contains(eipResourceDetailDTO.getId()), "提交的弹性公网产品已经存在于Mysql的回收列表中,无需重复回收");
                }
                //校验下云主机是否有绑定的evs 如果有一定要在提交的evsIdList中
                String evsId = resourceDetailDTO.getVolumeId();
                if (ObjNullUtils.isNotNull(evsId)) {
                    //evsId可能存在多个的逗号隔开
                    for (String evsIdItem : evsId.split(",")) {
                        //查询Evs的resourceDetailDTO
                        ResourceDetailDTO evsResourceDetailDTO = resourceDetailManager.getByDeviceId(evsIdItem);
                        Precondition.checkArgument(evsResourceDetailDTO, "找不到需要关联回收的evs数据请联系管理员 resourceDetailDTOId为" + resourceDetailDTO.getId());
                        Precondition.checkArgument(!opm.getEvsIdList()
                                                       .contains(evsResourceDetailDTO.getId()), "提交的云硬盘产品已经存在于Mysql的回收列表中,无需重复回收");
                    }
                }
                String vpcId = resourceDetailDTO.getVpcId();
                //如果vpcId不为空，说明这个云主机是挂载在vpc下的
                if (ObjNullUtils.isNotNull(vpcId)) {
                    //加到vpcId2deviceIdMap中 但是ecs的这个id可能是存在多个的逗号隔开
                    List<String> vpcIdList = Arrays.asList(vpcId.split(","));
                    //循环
                    vpcIdList.forEach(vpcIdItem -> {
                        //如果前缀是是vpc开头的就是vpc 如果是net开头的就是network
                        if (vpcIdItem.startsWith("vpc")) {
                            vpcId2deviceIdMap.put(vpcIdItem, resourceDetailDTO.getDeviceId());
                        }
                        if (vpcIdItem.startsWith("net")) {
                            networkId2deviceIdMap.put(vpcIdItem, resourceDetailDTO.getDeviceId());
                        }
                    });
                }
            });
            //数量不对等
            Precondition.checkArgument(resourceDetailDTOList.size() == opm.getGcsIdList()
                                                                          .size(), "Mysql列表数据有误,请检查");

        }
        //-----------------------------------------------校验redis部分------------------------------------------------------------------
        if (ObjNullUtils.isNotNull(opm.getRedisIdList())) {
            //先拉回ecs列表
            List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setIds(opm.getMysqlIdList())
                                                                                                                .setType(ProductTypeEnum.REDIS.getCode()));
            //循环校验里面的数据回收状态只有在使用中才能回收
            resourceDetailDTOList.forEach(resourceDetailDTO -> {
                boolean orderComplete = orderDataProvideService.isOrderComplete(resourceDetailDTO.getOrderId());
                Precondition.checkArgument(orderComplete, "当前资源存在未完成的工单 工单id为" + resourceDetailDTO.getOrderId());
                Precondition.checkArgument(RecoveryStatusEnum.canRecovery(resourceDetailDTO.getRecoveryStatus()),
                        "当前资源状态不可被回收 资源id为" + resourceDetailDTO.getDeviceId());
                //校验下云主机是否有绑定的eip 如果有一定要在提交的eipIdList中
                String eipId = resourceDetailDTO.getEipId();
                if (ObjNullUtils.isNotNull(eipId)) {
                    //查询Eip的resourceDetailDTO
                    ResourceDetailDTO eipResourceDetailDTO = resourceDetailManager.getByDeviceId(eipId);
                    Precondition.checkArgument(eipResourceDetailDTO, "找不到需要关联回收的eip数据请联系管理员 resourceDetailDTOId为" + resourceDetailDTO.getId());
                    Precondition.checkArgument(!opm.getEipIdList()
                                                   .contains(eipResourceDetailDTO.getId()), "提交的弹性公网产品已经存在于Redis的回收列表中,无需重复回收");
                }
                //校验下云主机是否有绑定的evs 如果有一定要在提交的evsIdList中
                String evsId = resourceDetailDTO.getVolumeId();
                if (ObjNullUtils.isNotNull(evsId)) {
                    //evsId可能存在多个的逗号隔开
                    for (String evsIdItem : evsId.split(",")) {
                        //查询Evs的resourceDetailDTO
                        ResourceDetailDTO evsResourceDetailDTO = resourceDetailManager.getByDeviceId(evsIdItem);
                        Precondition.checkArgument(evsResourceDetailDTO, "找不到需要关联回收的evs数据请联系管理员 resourceDetailDTOId为" + resourceDetailDTO.getId());
                        Precondition.checkArgument(!opm.getEvsIdList()
                                                       .contains(evsResourceDetailDTO.getId()), "提交的云硬盘产品已经存在于Redis的回收列表中,无需重复回收");
                    }
                }
                String vpcId = resourceDetailDTO.getVpcId();
                //如果vpcId不为空，说明这个云主机是挂载在vpc下的
                if (ObjNullUtils.isNotNull(vpcId)) {
                    //加到vpcId2deviceIdMap中 但是ecs的这个id可能是存在多个的逗号隔开
                    List<String> vpcIdList = Arrays.asList(vpcId.split(","));
                    //循环
                    vpcIdList.forEach(vpcIdItem -> {
                        //如果前缀是是vpc开头的就是vpc 如果是net开头的就是network
                        if (vpcIdItem.startsWith("vpc")) {
                            vpcId2deviceIdMap.put(vpcIdItem, resourceDetailDTO.getDeviceId());
                        }
                        if (vpcIdItem.startsWith("net")) {
                            networkId2deviceIdMap.put(vpcIdItem, resourceDetailDTO.getDeviceId());
                        }
                    });
                }
            });
            //数量不对等
            Precondition.checkArgument(resourceDetailDTOList.size() == opm.getGcsIdList()
                                                                          .size(), "Redis列表数据有误,请检查");

        }
        //-----------------------------------------------校验eip部分------------------------------------------------------------------
        if (ObjNullUtils.isNotNull(opm.getEipIdList())) {
            //先拉回ecs列表
            List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setIds(opm.getEipIdList())
                    .setType(ProductTypeEnum.EIP.getCode()));
            //循环校验里面的数据回收状态只有在使用中才能回收
            resourceDetailDTOList.forEach(resourceDetailDTO -> {
                boolean orderComplete = orderDataProvideService.isOrderComplete(resourceDetailDTO.getOrderId());
                Precondition.checkArgument(orderComplete, "当前资源存在未完成的工单 工单id为" + resourceDetailDTO.getOrderId());
                Precondition.checkArgument(RecoveryStatusEnum.canRecovery(resourceDetailDTO.getRecoveryStatus()),
                        "当前资源状态不可被回收 资源id为" + resourceDetailDTO.getDeviceId());
            });
            //数量不对等
            Precondition.checkArgument(resourceDetailDTOList.size() == opm.getEipIdList()
                    .size(), "EIP列表数据有误,请检查");
        }
        //-----------------------------------------------校验evs部分------------------------------------------------------------------
        if (ObjNullUtils.isNotNull(opm.getEvsIdList())) {
            //先拉回ecs列表
            List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setIds(opm.getEvsIdList())
                    .setType(ProductTypeEnum.EVS.getCode()));
            //循环校验里面的数据回收状态只有在使用中才能回收
            resourceDetailDTOList.forEach(resourceDetailDTO -> {
                boolean orderComplete = orderDataProvideService.isOrderComplete(resourceDetailDTO.getOrderId());
                Precondition.checkArgument(orderComplete, "当前资源存在未完成的工单 工单id为" + resourceDetailDTO.getOrderId());
                Precondition.checkArgument(RecoveryStatusEnum.canRecovery(resourceDetailDTO.getRecoveryStatus()),
                        "当前资源状态不可被回收 资源id为" + resourceDetailDTO.getDeviceId());
            });
            //数量不对等
            Precondition.checkArgument(resourceDetailDTOList.size() == opm.getEvsIdList()
                    .size(), "EVS列表数据有误,请检查");
        }
        //-----------------------------------------------校验nat部分------------------------------------------------------------------
        if (ObjNullUtils.isNotNull(opm.getNatIdList())) {
            //先拉回ecs列表
            List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setIds(opm.getNatIdList())
                    .setType(ProductTypeEnum.NAT.getCode()));
            //循环校验里面的数据回收状态只有在使用中才能回收
            resourceDetailDTOList.forEach(resourceDetailDTO -> {
                boolean orderComplete = orderDataProvideService.isOrderComplete(resourceDetailDTO.getOrderId());
                Precondition.checkArgument(orderComplete, "当前资源存在未完成的工单 工单id为" + resourceDetailDTO.getOrderId());
                Precondition.checkArgument(RecoveryStatusEnum.canRecovery(resourceDetailDTO.getRecoveryStatus()),
                        "当前资源状态不可被回收 资源id为" + resourceDetailDTO.getDeviceId());
                //校验下云主机是否有绑定的eip 如果有一定要在提交的eipIdList中
                String eipId = resourceDetailDTO.getEipId();
                if (ObjNullUtils.isNotNull(eipId)) {
                    //查询Eip的resourceDetailDTO
                    ResourceDetailDTO eipResourceDetailDTO = resourceDetailManager.getByDeviceId(eipId);
                    Precondition.checkArgument(eipResourceDetailDTO, "找不到需要关联回收的eip数据请联系管理员 resourceDetailDTOId为" + resourceDetailDTO.getId());
                    Precondition.checkArgument(!opm.getEipIdList()
                            .contains(eipResourceDetailDTO.getId()), "提交的EIP产品已经存在于nat网关的回收列表中,无需重复回收");
                }
                String vpcId = resourceDetailDTO.getVpcId();
                //如果vpcId不为空，说明这个云主机是挂载在vpc下的
                if (ObjNullUtils.isNotNull(vpcId)) {
                    //加到vpcId2deviceIdMap中 但是ecs的这个id可能是存在多个的逗号隔开
                    List<String> vpcIdList = Arrays.asList(vpcId.split(","));
                    //循环
                    vpcIdList.forEach(vpcIdItem -> {
                        //如果前缀是是vpc开头的就是vpc 如果是net开头的就是network
                        if (vpcIdItem.startsWith("vpc")) {
                            vpcId2deviceIdMap.put(vpcIdItem, resourceDetailDTO.getDeviceId());
                        }
                        if (vpcIdItem.startsWith("net")) {
                            networkId2deviceIdMap.put(vpcIdItem, resourceDetailDTO.getDeviceId());
                        }
                    });
                }
            });
            //数量不对等
            Precondition.checkArgument(resourceDetailDTOList.size() == opm.getNatIdList()
                    .size(), "NAT列表数据有误,请检查");
        }
        //-----------------------------------------------校验obs部分------------------------------------------------------------------
        if (ObjNullUtils.isNotNull(opm.getObsIdList())) {
            //先拉回ecs列表
            List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setIds(opm.getObsIdList())
                    .setType(ProductTypeEnum.OBS.getCode()));
            //循环校验里面的数据回收状态只有在使用中才能回收
            resourceDetailDTOList.forEach(resourceDetailDTO -> {
                boolean orderComplete = orderDataProvideService.isOrderComplete(resourceDetailDTO.getOrderId());
                Precondition.checkArgument(orderComplete, "当前资源存在未完成的工单 工单id为" + resourceDetailDTO.getOrderId());
                Precondition.checkArgument(RecoveryStatusEnum.canRecovery(resourceDetailDTO.getRecoveryStatus()),
                        "当前资源状态不可被回收 资源id为" + resourceDetailDTO.getDeviceId());
            });
            //数量不对等
            Precondition.checkArgument(resourceDetailDTOList.size() == opm.getObsIdList()
                    .size(), "OBS列表数据有误,请检查");
        }
        //-----------------------------------------------校验slb部分------------------------------------------------------------------
        if (ObjNullUtils.isNotNull(opm.getSlbIdList())) {
            //先拉回ecs列表
            List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setIds(opm.getSlbIdList())
                    .setType(ProductTypeEnum.SLB.getCode()));
            //循环校验里面的数据回收状态只有在使用中才能回收
            resourceDetailDTOList.forEach(resourceDetailDTO -> {
                boolean orderComplete = orderDataProvideService.isOrderComplete(resourceDetailDTO.getOrderId());
                Precondition.checkArgument(orderComplete, "当前资源存在未完成的工单 工单id为" + resourceDetailDTO.getOrderId());
                Precondition.checkArgument(RecoveryStatusEnum.canRecovery(resourceDetailDTO.getRecoveryStatus()),
                        "当前资源状态不可被回收 资源id为" + resourceDetailDTO.getDeviceId());
                //校验下云主机是否有绑定的eip 如果有一定要在提交的eipIdList中
                String eipId = resourceDetailDTO.getEipId();
                if (ObjNullUtils.isNotNull(eipId)) {
                    //查询Eip的resourceDetailDTO
                    ResourceDetailDTO eipResourceDetailDTO = resourceDetailManager.getByDeviceId(eipId);
                    Precondition.checkArgument(eipResourceDetailDTO, "找不到需要关联回收的弹性公网数据请联系管理员 resourceDetailDTOId为" + resourceDetailDTO.getId());
                    Precondition.checkArgument(!opm.getEipIdList()
                            .contains(eipResourceDetailDTO.getId()), "提交的弹性公网产品已经存在于负载均衡的回收列表中,无需重复回收");
                }
                //如果SLB下有监听不能回收
                List<SlbListenerDTO> slbListenerDTOS = slbListenerManager.listBySlbIds(opm.getSlbIdList());
                Precondition.checkArgument(CollectionUtil.isEmpty(slbListenerDTOS), "当前SLB下存在监听，无法进行回收");
                String vpcId = resourceDetailDTO.getVpcId();
                //如果vpcId不为空，说明这个云主机是挂载在vpc下的
                if (ObjNullUtils.isNotNull(vpcId)) {
                    //加到vpcId2deviceIdMap中 但是ecs的这个id可能是存在多个的逗号隔开
                    List<String> vpcIdList = Arrays.asList(vpcId.split(","));
                    //循环
                    vpcIdList.forEach(vpcIdItem -> {
                        //如果前缀是是vpc开头的就是vpc 如果是net开头的就是network
                        if (vpcIdItem.startsWith("vpc")) {
                            vpcId2deviceIdMap.put(vpcIdItem, resourceDetailDTO.getDeviceId());
                        }
                        if (vpcIdItem.startsWith("net")) {
                            networkId2deviceIdMap.put(vpcIdItem, resourceDetailDTO.getDeviceId());
                        }
                    });
                }
            });
        }
        //-----------------------------------------------校验传入vpc部分------------------------------------------------------------------
        ////如果vpcIdList不为空，校验vpcList
        if (ObjNullUtils.isNotNull(opm.getVpcIdList())) {
            ////vpc校验逻辑是如果传入vpc列表 那么先查询资源表里 vpcId like %vpcId% 包含的所有资源看看是否都在 设备里
            opm.getVpcIdList().forEach(vpcId -> {
                vpcOrderManager.listByIdList(opm.getVpcIdList()).forEach(vpcOrderDTO -> {
                    Precondition.checkArgument(RecoveryStatusEnum.canRecovery(vpcOrderDTO.getRecoveryStatus()), "当前资源状态不可被回收 VPC名称为" + vpcOrderDTO.getVpcName());
                });
                List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setVpcId(vpcId));
                //如果资源表里没有这个vpcId的资源，那么直接返回
                if (ObjNullUtils.isNull(resourceDetailDTOList)) {
                    //没有挂载的资源直接通过
                    return;
                }
                Set<String> currentVpcBindDeviceIds = StreamUtils.mapSet(resourceDetailDTOList, ResourceDetailDTO::getDeviceId);
                List<String> deviceIds = vpcId2deviceIdMap.get(vpcId);
                //返回的列表是currentVpcBindDeviceIds里有的而且deviceIds里没有的
                Sets.SetView<String> difference = Sets.difference(currentVpcBindDeviceIds, StreamUtils.toSet(deviceIds));

                //如果difference不为空，那么说明这个vpcId关联的设备有一部分没有在deviceIds里，那么直接抛出异常
                Precondition.checkArgument(difference.isEmpty(), "VPC关联的设备不在申请回收的设备列表里,请检查 设备id=" + difference);

            });

        }
        ////-----------------------------------------------校验传入network部分------------------------------------------------------------------
        //如果vpcIdList不为空，校验vpcList
        if (ObjNullUtils.isNotNull(opm.getNetworkIdList())) {
            //todo 少一个网络回收状态的检查
            ////vpc校验逻辑是如果传入vpc列表 那么先查询资源表里 vpcId like %vpcId% 包含的所有资源看看是否都在 设备里
            opm.getNetworkIdList().forEach(vpcId -> {
                List<ResourceDetailDTO> resourceDetailDTOList = resourceDetailManager.list(new ResourceDetailQuery().setVpcId(vpcId));
                //如果资源表里没有这个vpcId的资源，那么直接返回
                if (ObjNullUtils.isNull(resourceDetailDTOList)) {
                    //没有挂载的资源直接通过
                    return;
                }
                Set<String> currentVpcBindDeviceIds = StreamUtils.mapSet(resourceDetailDTOList, ResourceDetailDTO::getDeviceId);
                List<String> deviceIds = networkId2deviceIdMap.get(vpcId);
                //返回的列表是currentVpcBindDeviceIds里有的而且deviceIds里没有的
                Sets.SetView<String> difference = Sets.difference(currentVpcBindDeviceIds, StreamUtils.toSet(deviceIds));
                //如果difference不为空，那么说明这个vpcId关联的设备有一部分没有在deviceIds里，那么直接抛出异常
                Precondition.checkArgument(difference.isEmpty(), "VPC关联的设备不在申请回收的设备列表里,请检查 设备id=" + difference);

            });
        }
    }
}

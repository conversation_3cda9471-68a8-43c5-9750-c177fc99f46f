package com.datatech.slgzt.impl.manager;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.convert.StandardWorkOrderProductManagerConvert;
import com.datatech.slgzt.dao.StandardWorkOrderProductDAO;
import com.datatech.slgzt.dao.model.order.StandardWorkOrderProductDO;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.ResOpenEnum;
import com.datatech.slgzt.manager.RegionManager;
import com.datatech.slgzt.manager.StandardWorkOrderManager;
import com.datatech.slgzt.manager.StandardWorkOrderProductManager;
import com.datatech.slgzt.model.dto.RegionDTO;
import com.datatech.slgzt.model.dto.StandardWorkOrderDTO;
import com.datatech.slgzt.model.dto.StandardWorkOrderProductDTO;
import com.datatech.slgzt.model.dto.order.GoodsProductDTO;
import com.datatech.slgzt.model.dto.order.StandardAuditWorkOrderDTO;
import com.datatech.slgzt.model.nostander.*;
import com.datatech.slgzt.model.query.RegionQuery;
import com.datatech.slgzt.model.query.StandardWorkOrderProductQuery;
import com.datatech.slgzt.model.resourcce.*;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.utils.StreamUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月13日 17:02:23
 */
@Service
public class StandardWorkOrderProductManagerImpl implements StandardWorkOrderProductManager {


    @Resource
    private StandardWorkOrderProductDAO dao;


    @Resource
    private StandardWorkOrderManager standardWorkOrderManager;

    @Resource
    private RegionManager regionManager;

    @Resource
    private StandardWorkOrderProductManagerConvert convert;

    @Override
    public List<StandardWorkOrderProductDTO> list(StandardWorkOrderProductQuery query) {
        return StreamUtils.mapArray(dao.list(query), convert::do2dto);
    }

    /**
     * update
     *
     * @param dto
     */
    @Override
    public void update(StandardWorkOrderProductDTO dto) {
        dao.updateById(convert.dto2do(dto));

    }

    @Override
    public StandardWorkOrderProductDTO getById(Long id) {
        return convert.do2dto(dao.getById(id));
    }


    @Override
    public StandardWorkOrderProductDTO getByGid(String gid) {
        return convert.do2dto(dao.getByGid(gid));
    }

    @Override
    public StandardWorkOrderProductDTO getBySubOrderId(Long subOrderId) {
        return convert.do2dto(dao.getBySubOrderId(subOrderId));
    }

    @Override
    public void deleteByWorkOrderId(String id) {
        dao.delByWorkOrderId(id);
    }

    /**
     * insert
     *
     * @param dto
     */
    @Override
    public void insert(StandardWorkOrderProductDTO dto) {
        dao.insert(convert.dto2do(dto));
    }

    @Override
    public void updateStatusById(Long id, String status) {
        StandardWorkOrderProductDO standardWorkOrderProductDO = new StandardWorkOrderProductDO();
        standardWorkOrderProductDO.setId(id);
        standardWorkOrderProductDO.setOpenStatus(status);
        dao.updateById(standardWorkOrderProductDO);
    }


    @Override
    public void updateStatusByParentId(Long id, String status) {
        StandardWorkOrderProductDO standardWorkOrderProductDO = new StandardWorkOrderProductDO();
        standardWorkOrderProductDO.setParentProductId(id);
        standardWorkOrderProductDO.setOpenStatus(status);
        dao.updateByParentId(standardWorkOrderProductDO);
    }

    @Override
    public void updateJobExecutionIdBySubOrderId(Long subOrderId, Long jobExecutionId) {
        StandardWorkOrderProductDO updateDO = new StandardWorkOrderProductDO();
        updateDO.setSubOrderId(subOrderId);
        updateDO.setJobExecutionId(jobExecutionId);
        dao.updateJobExecutionIdBySubOrderId(updateDO);
    }

    @Override
    public void fillRegionMessage(StandardAuditWorkOrderDTO auditDto) {
        //获取工单下的所有产品
        //1.获取提交的资源信息
        List<GoodsProductDTO> goodsModelList = auditDto.getGoodsModelList();
        //这里要判断资源池是否包含特殊的资源池
        Set<String> regionCodeList = StreamUtils.mapSet(goodsModelList, GoodsProductDTO::getRegionCode);
        List<RegionDTO> list = regionManager.list(new RegionQuery().setCodeList(regionCodeList));
        //整理出OFFLINE_REGION_CODE
        Set<String> offlineRegionSet = StreamUtils.mapSet(list, RegionDTO::getOfflineRegion);
        //如果offlineRegionSet里包含了null 同时也包含了OFFLINE 则报错 线上和线下开通的资源池不能同时选择
        Precondition.checkArgument(!(offlineRegionSet.contains(null) &&offlineRegionSet.contains("OFFLINE")),
                "线上和线下开通的资源池不能同时选择");
        //如果只包含了OFFLINE 就要把工单的isoffline设置为true
        if (offlineRegionSet.size() == 1 &&offlineRegionSet.contains("OFFLINE")) {
            StandardWorkOrderDTO updateDTO = new StandardWorkOrderDTO();
            updateDTO.setId(auditDto.getOrderId());
            updateDTO.setIsOffline(true);
            standardWorkOrderManager.update(updateDTO);
            //所有产品直接开通成功
            StandardWorkOrderProductDO productDO = new StandardWorkOrderProductDO();
            productDO.setWorkOrderId(auditDto.getOrderId());
            productDO.setOpenStatus(ResOpenEnum.OPEN_SUCCESS.getCode());
            dao.updateByWorkOrderId(productDO);
        }
        //2.循环并且填充
        for (GoodsProductDTO goodsProductDTO : goodsModelList) {
            //获取该产品下的所有子产品
            StandardWorkOrderProductDO productDO = dao.getById(Long.valueOf(goodsProductDTO.getId()));
            if (productDO == null) {
                continue;
            }
            //获取子产品
            List<StandardWorkOrderProductDO> productDOList = dao.list(new StandardWorkOrderProductQuery().setParentId(productDO.getId()));
            //组合到一起 循环更新就好了
            productDOList.add(productDO);
            for (StandardWorkOrderProductDO product : productDOList) {
                String propertySnapshot = product.getPropertySnapshot();
                String productType = product.getProductType();
                if (ProductTypeEnum.ECS.getCode().equalsIgnoreCase(productType)
                        || ProductTypeEnum.GCS.getCode().equalsIgnoreCase(productType)) {
                    EcsModel escModel = JSON.parseObject(propertySnapshot, EcsModel.class);
                    escModel.setRegionId(Long.valueOf(goodsProductDTO.getRegionId()));
                    escModel.setRegionCode(goodsProductDTO.getRegionCode());
                    escModel.setRegionName(goodsProductDTO.getRegionName());
                    propertySnapshot = JSON.toJSONString(escModel);
                }
                if (ProductTypeEnum.EVS.getCode().equalsIgnoreCase(productType)) {
                    MountDataDiskModel ecsResourceModel = JSON.parseObject(propertySnapshot, MountDataDiskModel.class);
                    ecsResourceModel.setRegionId(Long.valueOf(goodsProductDTO.getRegionId()));
                    ecsResourceModel.setRegionCode(goodsProductDTO.getRegionCode());
                    ecsResourceModel.setRegionName(goodsProductDTO.getRegionName());
                    propertySnapshot = JSON.toJSONString(ecsResourceModel);
                }
                if (ProductTypeEnum.EIP.getCode().equalsIgnoreCase(productType)) {
                    EipModel eipModel = JSON.parseObject(propertySnapshot, EipModel.class);
                    eipModel.setRegionId(Long.valueOf(goodsProductDTO.getRegionId()));
                    eipModel.setRegionCode(goodsProductDTO.getRegionCode());
                    eipModel.setRegionName(goodsProductDTO.getRegionName());
                    propertySnapshot = JSON.toJSONString(eipModel);
                }
                if (ProductTypeEnum.NAT.getCode().equalsIgnoreCase(productType)) {
                    NatGatwayModel natGatwayModel = JSON.parseObject(propertySnapshot, NatGatwayModel.class);
                    natGatwayModel.setRegionId(Long.valueOf(goodsProductDTO.getRegionId()));
                    natGatwayModel.setRegionCode(goodsProductDTO.getRegionCode());
                    natGatwayModel.setRegionName(goodsProductDTO.getRegionName());
                    propertySnapshot = JSON.toJSONString(natGatwayModel);
                }
                if (ProductTypeEnum.SLB.getCode().equalsIgnoreCase(productType)) {
                    SlbModel slbModel = JSON.parseObject(propertySnapshot, SlbModel.class);
                    slbModel.setRegionId(Long.valueOf(goodsProductDTO.getRegionId()));
                    slbModel.setRegionCode(goodsProductDTO.getRegionCode());
                    slbModel.setRegionName(goodsProductDTO.getRegionName());
                    propertySnapshot = JSON.toJSONString(slbModel);
                }
                if (ProductTypeEnum.OBS.getCode().equalsIgnoreCase(productType)) {
                    ObsModel obsModel = JSON.parseObject(propertySnapshot, ObsModel.class);
                    obsModel.setRegionId(Long.valueOf(goodsProductDTO.getRegionId()));
                    obsModel.setRegionCode(goodsProductDTO.getRegionCode());
                    obsModel.setRegionName(goodsProductDTO.getRegionName());
                    propertySnapshot = JSON.toJSONString(obsModel);
                }
                if (ProductTypeEnum.CQ.getCode().equalsIgnoreCase(productType)) {
                    CQModel CQModel = JSON.parseObject(propertySnapshot, CQModel.class);
                    CQModel.setRegionId(Long.valueOf(goodsProductDTO.getRegionId()));
                    CQModel.setRegionCode(goodsProductDTO.getRegionCode());
                    CQModel.setRegionName(goodsProductDTO.getRegionName());
                    propertySnapshot = JSON.toJSONString(CQModel);
                }
                product.setPropertySnapshot(propertySnapshot);
                dao.updateById(productDO);
            }
        }
    }

    @Override
    public ResourceShowInfoDTO selectResourceOverview(StandardWorkOrderProductQuery productQuery) {
        List<StandardWorkOrderProductDO> productDOS = dao.getByWorkOrderId(productQuery.getOrderId());
        // 通过parentProductId是否为0进行分组开通的产品类型
        Map<Long, StandardWorkOrderProductDO> parentProductMap = productDOS.stream().filter(product -> product.getParentProductId() == 0)
                .collect(Collectors.toMap(StandardWorkOrderProductDO::getId, productDO -> productDO));

        ResourceShowInfoDTO infoDTO = new ResourceShowInfoDTO();
        ExtendEcsResource ecsResource = new ExtendEcsResource();
        ExtendEcsResource gcsResource = new ExtendEcsResource();
        ExtendEvsResource evsResource = new ExtendEvsResource();
        ExtendObsResource obsResource = new ExtendObsResource();
        ExtendNatResource natResource = new ExtendNatResource();
        ExtendSlbResource slbResource = new ExtendSlbResource();
        ExtendCQResource cqResource = new ExtendCQResource();
        parentProductMap.forEach((productId, product) -> {
            if (ProductTypeEnum.ECS.getCode().equalsIgnoreCase(product.getProductType())) {
                convertEcs(product, ecsResource);
                infoDTO.setEcs(ecsResource);
            }
            if (ProductTypeEnum.GCS.getCode().equalsIgnoreCase(product.getProductType())) {
                convertEcs(product, gcsResource);
                infoDTO.setGcs(gcsResource);
            }
            if (ProductTypeEnum.EVS.getCode().equalsIgnoreCase(product.getProductType())) {
                convertEvs(product, evsResource);
                infoDTO.setEvs(evsResource);
            }
            if (ProductTypeEnum.OBS.getCode().equalsIgnoreCase(product.getProductType())) {
                convertObs(product, obsResource);
                infoDTO.setObs(obsResource);
            }
            if (ProductTypeEnum.NAT.getCode().equalsIgnoreCase(product.getProductType())) {
                convertNat(product, natResource);
                infoDTO.setNat(natResource);
            }
            if (ProductTypeEnum.SLB.getCode().equalsIgnoreCase(product.getProductType())) {
                convertSlb(product, slbResource);
                infoDTO.setSlb(slbResource);
            }
            if (ProductTypeEnum.CQ.getCode().equalsIgnoreCase(product.getProductType())) {
                convertCQ(product, cqResource);
                infoDTO.setCq(cqResource);
            }
        });

        assembleResource(infoDTO);
        return infoDTO;
    }

    /**
     * 组装单位
     */
    private void assembleResource(ResourceShowInfoDTO infoDTO) {
        ExtendEcsResource ecs = infoDTO.getEcs();
        if (ecs != null) {
            ecs.setVcpuNumbers(ecs.getCpuNumbersTmp() + "核");
            ecs.setRamNumbers(ecs.getRamNumbersTmp() + "G");
            ecs.setStorageNumbers(ecs.getStorageNumbersTmp() + "G");
            ecs.setBandWidthNumbers(ecs.getBandWidthNumbersTmp() + "M");
        }
        ExtendEcsResource gcs = infoDTO.getGcs();
        if (gcs != null) {
            gcs.setVcpuNumbers(gcs.getCpuNumbersTmp() == null ? "0" : gcs.getCpuNumbersTmp() + "核");
            gcs.setRamNumbers(gcs.getRamNumbersTmp() + "G");
            gcs.setStorageNumbers(gcs.getStorageNumbersTmp() + "G");
            gcs.setBandWidthNumbers(gcs.getBandWidthNumbersTmp() + "M");
            gcs.setVgpuNumbers(gcs.getGpuNumbersTmp() + "T4");
        }
        ExtendEvsResource evs = infoDTO.getEvs();
        if (evs != null) {
            evs.setStorageNumbers(evs.getStorageNumbersTmp() + "G");
        }
        ExtendSlbResource slb = infoDTO.getSlb();
        if (slb != null) {
            slb.setBandWidthNumbers(slb.getBandWidthNumbersTmp() + "M");
        }
        ExtendObsResource obs = infoDTO.getObs();
        if (obs != null) {
            obs.setStorageNumbers(obs.getStorageNumbersTmp() + "G");
        }
        ExtendNatResource nat = infoDTO.getNat();
        if (nat != null) {
            nat.setBandWidthNumbers(nat.getBandWidthNumbersTmp() + "M");
        }
        ExtendCQResource cq = infoDTO.getCq();
        if (cq != null) {
            cq.setCpuNumbers(cq.getCpuNumbersTmp() + "核");
            cq.setMemoryNumbers(cq.getMemoryNumbersTmp() + "G");
            if (cq.getGpuMemoryNumbersTmp() > 0) {
                cq.setGpuMemoryNumbers(cq.getGpuMemoryNumbersTmp() + "G");
            }
            cq.setGpuCoreNumbers(cq.getGpuCoreNumbersTmp() + "个");
            cq.setGpuVirtualCoreNumbers(cq.getGpuVirtualCoreNumbersTmp() + "个");
        }
    }

    /**
     * slb资源概览转换
     */
    private ExtendSlbResource convertSlb(StandardWorkOrderProductDO productDO, ExtendSlbResource slbResource) {
        String propertySnapshot = productDO.getPropertySnapshot();
        SlbModel slbModel = JSON.parseObject(propertySnapshot, SlbModel.class);
        slbResource.setResourceNumbers(slbResource.getResourceNumbers() + 1);
        List<EipModel> eipModelList = slbModel.getEipModelList();
        if (CollectionUtil.isNotEmpty(eipModelList)) {
            EipModel eipModel = eipModelList.get(0);
            Integer bandwidth = eipModel.getBandwidth();
            slbResource.setBandWidthNumbersTmp(slbResource.getBandWidthNumbersTmp() + bandwidth);
        }

        return slbResource;
    }

    /**
     * nat资源概览转换
     */
    private ExtendNatResource convertNat(StandardWorkOrderProductDO productDO, ExtendNatResource natResource) {
        String propertySnapshot = productDO.getPropertySnapshot();
        NatGatwayModel natGatwayModel = JSON.parseObject(propertySnapshot, NatGatwayModel.class);
        natResource.setResourceNumbers(natResource.getResourceNumbers() + 1);
        List<EipModel> eipModelList = natGatwayModel.getEipModelList();
        if (CollectionUtil.isNotEmpty(eipModelList)) {
            Integer bandwidth = eipModelList.get(0).getBandwidth();
            natResource.setBandWidthNumbersTmp(natResource.getBandWidthNumbersTmp() + bandwidth);
        }

        return natResource;
    }

    /**
     * obs资源概览转换
     */
    private ExtendObsResource convertObs(StandardWorkOrderProductDO productDO, ExtendObsResource obsResource) {
        String propertySnapshot = productDO.getPropertySnapshot();
        ObsModel obsModel = JSON.parseObject(propertySnapshot, ObsModel.class);
        obsResource.setResourceNumbers(obsResource.getResourceNumbers() + 1);
        Integer size = obsModel.getStorageDiskSize();
        obsResource.setStorageNumbersTmp(obsResource.getStorageNumbersTmp() + size);
        return obsResource;
    }

    /**
     * evs资源概览转换
     */
    private ExtendEvsResource convertEvs(StandardWorkOrderProductDO productDO, ExtendEvsResource evsResource) {
        String propertySnapshot = productDO.getPropertySnapshot();
        MountDataDiskModel ecsResourceModel = JSON.parseObject(propertySnapshot, MountDataDiskModel.class);
        evsResource.setResourceNumbers(evsResource.getResourceNumbers() + 1);
        Integer size = ecsResourceModel.getSysDiskSize();
        evsResource.setStorageNumbersTmp(evsResource.getStorageNumbersTmp() + size);
        return evsResource;
    }

    /**
     * ecs资源概览转换
     */
    private ExtendEcsResource convertEcs(StandardWorkOrderProductDO productDO, ExtendEcsResource ecsResource) {
        String propertySnapshot = productDO.getPropertySnapshot();
        EcsModel escModel = JSON.parseObject(propertySnapshot, EcsModel.class);
        ecsResource.setResourceNumbers(ecsResource.getResourceNumbers() + 1);
        // 计算cpu大小
        String flavorName = escModel.getFlavorName();
        // 4C16GB/1T4
        if (StringUtils.isNotEmpty(flavorName)) {
            Integer cpuNumber = Integer.parseInt(StringUtils.substring(flavorName,
                    0, StringUtils.indexOf(flavorName, "C")));
            ecsResource.setCpuNumbersTmp(ecsResource.getCpuNumbersTmp() + cpuNumber);

            //获取内存
            Integer ramNumber = Integer.parseInt(StringUtils.substringBetween(flavorName, "C", "G"));
            ecsResource.setRamNumbersTmp(ecsResource.getRamNumbersTmp() + ramNumber);
            if (flavorName.contains("T")) {
                Integer gpuNumber = Integer.parseInt(StringUtils.substringBetween(flavorName, "/", "T"));
                ecsResource.setGpuNumbersTmp(ecsResource.getGpuNumbersTmp() + gpuNumber);
            }
        }

        //存储大小的算法是默认的系统盘大小+数据盘大小
        Integer storageNumbersTmp = ecsResource.getStorageNumbersTmp();//默认的系统盘大小
        //先判断是否存在挂载
        if (escModel.getMountDataDisk()) {
            //再计算数据盘大小
            List<MountDataDiskModel> mountDataDiskModelList = escModel.getMountDataDiskList();
            if (CollectionUtil.isNotEmpty(mountDataDiskModelList)) {
                for (MountDataDiskModel mountDataDiskModel : mountDataDiskModelList) {
                    storageNumbersTmp += Optional.ofNullable(mountDataDiskModel.getSysDiskSize()).orElse(0);
                }
            }
        }
        ecsResource.setStorageNumbersTmp(storageNumbersTmp+ escModel.getSysDiskSize());
        List<EipModel> eipModelList = escModel.getEipModelList();
        if (CollectionUtil.isNotEmpty(eipModelList)) {
            EipModel eipModel = eipModelList.get(0);
            Integer width = eipModel.getBandwidth();
            ecsResource.setBandWidthNumbersTmp(ecsResource.getBandWidthNumbersTmp() + width);
        }

        return ecsResource;
    }



    /**
     * ecs资源概览转换
     */
    private ExtendEcsResource convertGcs(StandardWorkOrderProductDO productDO, ExtendEcsResource ecsResource) {
        String propertySnapshot = productDO.getPropertySnapshot();
        EcsModel escModel = JSON.parseObject(propertySnapshot, EcsModel.class);
        ecsResource.setResourceNumbers(ecsResource.getResourceNumbers() + 1);
        // 计算cpu大小
        String flavorName = escModel.getFlavorName();
        Integer cpuNumber = Integer.parseInt(StringUtils.substring(flavorName,
                0, StringUtils.indexOf(flavorName, "C")));
        ecsResource.setCpuNumbersTmp(ecsResource.getCpuNumbersTmp() + cpuNumber);

        //获取内存
        Integer ramNumber = Integer.parseInt(StringUtils.substringBetween(flavorName, "C", "G"));
        ecsResource.setRamNumbersTmp(ecsResource.getRamNumbersTmp() + ramNumber);
        //存储大小的算法是默认的系统盘大小+数据盘大小
        Integer storageNumbersTmp = ecsResource.getStorageNumbersTmp();//默认的系统盘大小
        //先判断是否存在挂载
        if (escModel.getMountDataDisk()) {
            //再计算数据盘大小
            List<MountDataDiskModel> mountDataDiskModelList = escModel.getMountDataDiskList();
            if (CollectionUtil.isNotEmpty(mountDataDiskModelList)) {
                for (MountDataDiskModel mountDataDiskModel : mountDataDiskModelList) {
                    storageNumbersTmp += Optional.ofNullable(mountDataDiskModel.getSysDiskSize()).orElse(0);
                }
            }
        }
        ecsResource.setStorageNumbersTmp(storageNumbersTmp+ escModel.getSysDiskSize());
        List<EipModel> eipModelList = escModel.getEipModelList();
        if (CollectionUtil.isNotEmpty(eipModelList)) {
            EipModel eipModel = eipModelList.get(0);
            Integer width = eipModel.getBandwidth();
            ecsResource.setBandWidthNumbersTmp(ecsResource.getBandWidthNumbersTmp() + width);
        }

        return ecsResource;
    }

    /**
     * 容器资源配额概览转换
     */
    private ExtendCQResource convertCQ(StandardWorkOrderProductDO productDO, ExtendCQResource containerResource) {
        String propertySnapshot = productDO.getPropertySnapshot();
        CQModel cqModel = JSON.parseObject(propertySnapshot, CQModel.class);
        containerResource.setResourceNumbers(containerResource.getResourceNumbers() + 1);

        // 累加CPU核心数
        Integer cpuCores = cqModel.getVCpus();
        if (cpuCores != null) {
            containerResource.setCpuNumbersTmp(containerResource.getCpuNumbersTmp() + cpuCores);
        }

        // 累加内存大小
        Integer memorySize = cqModel.getRam();
        if (memorySize != null) {
            containerResource.setMemoryNumbersTmp(containerResource.getMemoryNumbersTmp() + memorySize);
        }

        // 累加GPU显存大小（如果有）
        Integer gpuMemorySize = cqModel.getGpuVirtualMemory();
        if (gpuMemorySize != null) {
            containerResource.setGpuMemoryNumbersTmp(containerResource.getGpuMemoryNumbersTmp() + gpuMemorySize);
        }

        // 累加GPU算力大小（如果有）
        Integer gpuRatioSize = cqModel.getGpuRatio();
        if (gpuRatioSize != null) {
            containerResource.setGpuRatioNumbersTmp(containerResource.getGpuRatioNumbersTmp() + gpuRatioSize);
        }

        // 累加物理GPU卡数量（如果有）
        Integer gpuCoreSize = cqModel.getGpuCore();
        if (gpuCoreSize != null) {
            containerResource.setGpuCoreNumbersTmp(containerResource.getGpuCoreNumbersTmp() + gpuCoreSize);
        }

        // 累加虚拟GPU卡数量（如果有）
        Integer gpuVirtualCoreSize = cqModel.getGpuVirtualCore();
        if (gpuVirtualCoreSize != null) {
            containerResource.setGpuVirtualCoreNumbersTmp(containerResource.getGpuVirtualCoreNumbersTmp() + gpuVirtualCoreSize);
        }


        return containerResource;
    }
}

package com.datatech.slgzt.impl.service.xieyun;

import org.springframework.batch.core.scope.context.ChunkContext;
import org.springframework.batch.item.ExecutionContext;

import java.util.Map;

public class JobStepHelper {
    private final Map<String,Object> jobParameters;
    private final ExecutionContext executionContext;
    private final ChunkContext chunkContext;

    public JobStepHelper(ChunkContext context) {
        this.chunkContext = context;
        this.jobParameters = context.getStepContext().getJobParameters();
        this.executionContext = context.getStepContext()
                .getStepExecution()
                .getJobExecution()
                .getExecutionContext();
    }

    // 统一获取参数（优先从ExecutionContext获取，其次从JobParameters获取）
    public String get(String key) {
        Object value = executionContext.get(key);
        if (value != null) {
            return value.toString();
        }
        return jobParameters.get(key).toString();
    }

    // 获取必须参数（加强版校验）
    public String require(String key) {
        String value = get(key);
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalStateException("必需参数 '" + key + "' 不存在或为空");
        }
        return value;
    }

    // 存入上下文
    public void put(String key, Object value) {
        executionContext.put(key, value != null ? value.toString() : "");
    }

    //stop
    public void stop() {
        chunkContext.getStepContext().getStepExecution().getJobExecution().stop();
    }


}
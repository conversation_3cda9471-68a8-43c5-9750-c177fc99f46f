package com.datatech.slgzt.consumer;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.dao.SlbCertificateDAO;
import com.datatech.slgzt.enums.GoodsTypeEnum;
import com.datatech.slgzt.enums.VmOperationEnum;
import com.datatech.slgzt.manager.*;
import com.datatech.slgzt.model.ResourceCenterProductMessage;
import com.datatech.slgzt.model.TaskStatusExt;
import com.datatech.slgzt.model.dto.*;
import com.datatech.slgzt.service.cmdb.CmdbReportService;
import com.datatech.slgzt.utils.RetryUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 资源中心产品操作的消费
 * <AUTHOR>
 * @description TODO
 * @date 2025年 05月12日 13:47:51
 */
@Slf4j
@Component
public class ResourceCenterProductConsumer {

    @Resource
    private VirtualIpManager virtualIpManager;

    @Resource
    private SlbListenerManager slbListenerManager;

    @Resource
    private SlbCertificateManager slbCertificateManager;

    @Resource
    private SlbListenerServerGroupManager slbListenerServerGroupManager;

    @Resource
    private VnicManager vnicManager;

    @Resource
    private ResourceDetailManager resourceDetailManager;

    @Resource
    private CmdbReportService cmdbReportService;

    @Resource
    private SlbCertificateDAO slbCertificateDAO;

    @KafkaListener(groupId = "work-order-layout-task-callback", topics = {"prod_layout_task_callback_topic"})
    public void consumeMessage(List<ConsumerRecord<String, String>> consumerRecordList) {
        log.info("资源中心回调: {}", JSONObject.toJSONString(consumerRecordList));
        for (ConsumerRecord<String, String> record : consumerRecordList) {
            ResourceCenterProductMessage message = JSONObject.parseObject(record.value(), ResourceCenterProductMessage.class);
            log.info("资源中心回调消息: {}", JSONObject.toJSONString(message));
            String resourceType = message.getResourceType();
            switch (resourceType) {
                case "VIP":
                  //  vip(message);
                    break;
                case "NET_CARD":
                    netcard(message);
                    break;
                case "SLB_LISTENER":
                    slbListener(message);
                case "SLB_CERTIFICATE":
                    slbCertificateReceiveInfo(message);
                    break;
            }
        }
    }

    /**
     * 处理slb监听器
     * @param message
     */
    private void slbListener(ResourceCenterProductMessage message) {
        String id = message.getId();
        String operationType = message.getOperationType();
        String status = message.getStatus();
        //状态执行中全部跳过
        if (status.equals("EXECUTING")) {
            return;
        }
        //----------------------------创建--------------------------------------------
        if (operationType.contains("CREATE")) {
          //修改slb监听器的创建状态
            List<SlbListenerDTO> slbListenerDTOS = slbListenerManager.listByCreateTaskId(id);
            //如果不是成功的就直接删除数据
            if (!status.equals("SUCCESS")) {
                for (SlbListenerDTO slbListenerDTO : slbListenerDTOS) {
                    slbListenerManager.delete(slbListenerDTO.getId());
                    //查询slb监听器服务组
                    List<SlbListenerServerGroupDTO> slbListenerServerGroupDTOS = slbListenerServerGroupManager.listByListenerId(slbListenerDTO.getId());
                    //循环更新状态
                    for (SlbListenerServerGroupDTO slbListenerServerGroupDTO : slbListenerServerGroupDTOS) {
                        slbListenerServerGroupManager.delete(slbListenerServerGroupDTO.getId());
                    }
                }
            }
            if (status.equals("SUCCESS")){
                //如果是成功的话要更新数据
                //循环更新状态
                for (SlbListenerDTO slbListenerDTO : slbListenerDTOS) {
                    slbListenerDTO.getTaskStatusExt().setCreateStatus(status);
                    slbListenerDTO.setStatus("0");
                    slbListenerManager.update(slbListenerDTO);
                    //查询slb监听器服务组0
                    List<SlbListenerServerGroupDTO> slbListenerServerGroupDTOS =  RetryUtils.retryGet(()->slbListenerServerGroupManager.listByListenerId(slbListenerDTO.getId()));
                    //循环更新状态
                    for (SlbListenerServerGroupDTO slbListenerServerGroupDTO : slbListenerServerGroupDTOS) {
                        slbListenerServerGroupDTO.getTaskStatusExt().setCreateStatus(status);
                        slbListenerServerGroupDTO.setStatus("0");
                        slbListenerServerGroupManager.update(slbListenerServerGroupDTO);
                    }
                    updateSlbCertificate(slbListenerDTO);
                }
            }

        }
        //----------------------------更新--------------------------------------------
        if (operationType.contains("UPDATE")) {
            //修改slb监听器的更新状态
            List<SlbListenerDTO> slbListenerDTOS =  RetryUtils.retryGet(()->slbListenerManager.listByUpdateTaskId(id));
            for (SlbListenerDTO slbListenerDTO : slbListenerDTOS) {
                //如果是成功的话要更新数据
                slbListenerDTO.getTaskStatusExt().setUpdateStatus(status);
                slbListenerDTO.setStatus("0");
                slbListenerManager.update(slbListenerDTO);
                if (status.equals("SUCCESS")) {
                    String updateSnapshot = slbListenerDTO.getTaskStatusExt().getUpdateSnapshot();
                    SlbListenerDTO snapshotDTO = JSON.parseObject(updateSnapshot, SlbListenerDTO.class);
                    slbListenerManager.update(snapshotDTO);
                }
                updateSlbCertificate(slbListenerDTO);
            }
        }
        //----------------------------删除--------------------------------------------
        if (operationType.contains("DELETE")) {
            //修改slb监听器的删除状态
            List<SlbListenerDTO> slbListenerDTOS = RetryUtils.retryGet(()->slbListenerManager.listByDeleteTaskId(id));
            for (SlbListenerDTO slbListenerDTO : slbListenerDTOS) {
                slbListenerDTO.getTaskStatusExt().setDeleteStatus(status);
                slbListenerDTO.setStatus("0");
                slbListenerManager.update(slbListenerDTO);
                //如果删除成功的话要删除数据
                if (status.equals("SUCCESS")) {
                    slbListenerManager.delete(slbListenerDTO.getId());
                    //删除slb监听器服务组
                    List<SlbListenerServerGroupDTO> slbListenerServerGroupDTOS = slbListenerServerGroupManager.listByListenerId(slbListenerDTO.getId());
                    for (SlbListenerServerGroupDTO slbListenerServerGroupDTO : slbListenerServerGroupDTOS) {
                        slbListenerServerGroupManager.delete(slbListenerServerGroupDTO.getId());
                    }
                }
            }
        }
    }


    /**
     * 更新证书和监听器关联关系
     * @param slbListenerDTO
     */
    private void updateSlbCertificate(SlbListenerDTO slbListenerDTO){
        //ca证书id
        String certificateId = slbListenerDTO.getSlbListenerProtocolModel().getCertificateId();
        String serverCertificateId = slbListenerDTO.getSlbListenerProtocolModel().getServerCertificateId();
        if(StringUtils.isNotBlank(certificateId)){
            //更新监听器与服务器证书关系
            SlbCertificateDTO slbCertificateDTO = slbCertificateManager.getById(certificateId);
            Map<String, String> slbListenerRel = Optional.ofNullable(slbCertificateDTO.getSlbListenerRel()).orElse(new HashMap<>());
            slbListenerRel.put(certificateId,slbListenerDTO.getListenerName());
            slbListenerRel.put(serverCertificateId,slbListenerDTO.getListenerName());
            slbCertificateDTO.setSlbListenerRel(slbListenerRel);
            slbCertificateManager.update(slbCertificateDTO);
        }
        if(StringUtils.isNotBlank(serverCertificateId)){
            //更新监听器与服务器证书关系
            SlbCertificateDTO slbCertificateDTO = slbCertificateManager.getById(serverCertificateId);
            Map<String, String> slbListenerRel = Optional.ofNullable(slbCertificateDTO.getSlbListenerRel()).orElse(new HashMap<>());
            slbListenerRel.put(certificateId,slbListenerDTO.getListenerName());
            slbListenerRel.put(serverCertificateId,slbListenerDTO.getListenerName());
            slbCertificateDTO.setSlbListenerRel(slbListenerRel);
            slbCertificateManager.update(slbCertificateDTO);
        }
    }

    public void slbCertificateReceiveInfo(ResourceCenterProductMessage message){
        String id = message.getId();
        String resourceId = message.getResourceId();
        String operationType = message.getOperationType();
        String status = message.getStatus();
        //状态执行中全部跳过
        if ("EXECUTING".equals(message.getStatus())) {
            return;
        }
        if (operationType.contains("CREATE")){
            //修改slb监听器的创建状态
            List<SlbCertificateDTO> slbCertificateDTOS = slbCertificateManager.listByCreateTaskId(id);
            //如果不是成功的就直接删除数据
            if (!"SUCCESS".equalsIgnoreCase(status)) {
                for (SlbCertificateDTO slbCertificateDTO : slbCertificateDTOS) {
                    slbCertificateManager.delete(slbCertificateDTO.getId());
                }
            }
            //如果成功修改任务状态
            if ("SUCCESS".equalsIgnoreCase(status)) {
                //将初始化状态修改为已处理
                slbCertificateDTOS.forEach(slbCertificateItem ->{
                    TaskStatusExt taskStatusExt = JSONObject.parseObject(slbCertificateItem.getTaskStatusExt(), TaskStatusExt.class);
                    taskStatusExt.setCreateStatus("SUCCESS");
                    slbCertificateItem.setTaskStatusExt(JSON.toJSONString(taskStatusExt));
                    slbCertificateItem.setStatus("0");
                    slbCertificateManager.update(slbCertificateItem);
                });
            }
        }

        if (operationType.contains("DELETE")){
            //根据resourceId获取认证
            List<SlbCertificateDTO> slbCertificateDTOS = slbCertificateManager.listByCreateResourceId(resourceId);
            //如果删除成功的话要删除数据
            if ("SUCCESS".equals(status)) {
                for (SlbCertificateDTO slbCertificateDTO : slbCertificateDTOS) {
                    slbCertificateDAO.delete(slbCertificateDTO.getId());
                }
            }else {
                //修改状态
                slbCertificateDTOS.forEach(slbCertificateItem ->{
                    TaskStatusExt taskStatusExt = JSONObject.parseObject(slbCertificateItem.getTaskStatusExt(), TaskStatusExt.class);
                    taskStatusExt.setCreateStatus("0");
                    slbCertificateItem.setTaskStatusExt(JSON.toJSONString(taskStatusExt));
                    slbCertificateItem.setStatus("0");
                    slbCertificateManager.update(slbCertificateItem);
                });
            }
        }
    }

    /**
     * 处理netcard
     *
     * @param message
     */
    @SneakyThrows
    private void netcard(ResourceCenterProductMessage message) {
        if (!"SUCCESS".equals(message.getStatus())) {
            return;
        }
        Thread.sleep(5000);
        String resourceType = message.getResourceType();
        if (!GoodsTypeEnum.NETCARD.getAlias().equals(resourceType)) {
            return;
        }
        String operateType = message.getOperationType();
        String resourceId = message.getResourceId();
        String attachResourceId = message.getAttachResourceId();
        VnicDTO vnicDTO = vnicManager.getByVnicId(resourceId);
        if (Objects.isNull(vnicDTO)) {
            return;
        }
        if (StringUtils.isBlank(attachResourceId)) {
            attachResourceId = vnicDTO.getVmId();
        }
        ResourceDetailDTO resourceDetailDTO = resourceDetailManager.getByDeviceId(attachResourceId);
        List<ResourceCenterProductMessage.IpInfo> ipList = message.getIpList();
        //更新ip信息
        if (VmOperationEnum.NETCARD_CREATE.getCode().equals(operateType)) {
            if (CollectionUtil.isNotEmpty(ipList)) {
                vnicDTO.setIpAddress(ipList.get(0).getIpAddress());
            }
            vnicManager.update(vnicDTO);
        } else if (VmOperationEnum.NETCARD_BIND.getCode().equals(operateType) && Objects.nonNull(resourceDetailDTO)) {
            //更新网卡信息
            vnicDTO.setVmId(attachResourceId);
            vnicDTO.setVmName(resourceDetailDTO.getDeviceName());
            if (CollectionUtil.isNotEmpty(ipList)) {
                vnicDTO.setIpAddress(ipList.get(0).getIpAddress());
            }
            vnicManager.update(vnicDTO);
            //更新云主机信息
            String ip = resourceDetailDTO.getIp();
            if (StringUtils.isNotBlank(ip)) {
                ArrayList<String> list = ListUtil.toList(ip.split(","));
                if (Objects.nonNull(vnicDTO.getIpAddress())) {
                    list.add(vnicDTO.getIpAddress());
                }
                ip = list.stream().distinct().collect(Collectors.joining(","));
            }
            resourceDetailDTO.setIp(ip);
            resourceDetailManager.updateById(resourceDetailDTO);
            cmdbReportService.updateInstanceEcsByResource(resourceDetailDTO);
        } else if (VmOperationEnum.NETCARD_UNBIND.getCode().equals(operateType) && Objects.nonNull(resourceDetailDTO)) {
            //更新网卡信息
            vnicDTO.setVmId(null);
            vnicDTO.setVmName(null);
            vnicManager.updateVmById(vnicDTO);
            //更新云主机信息
            String ip = resourceDetailDTO.getIp();
            ArrayList<String> list = new ArrayList<>(ListUtil.toList(ip.split(",")));
            list.remove(vnicDTO.getIpAddress());
            resourceDetailDTO.setIp(list.stream().distinct().collect(Collectors.joining(",")));
            resourceDetailManager.updateById(resourceDetailDTO);
            cmdbReportService.updateInstanceEcsByResource(resourceDetailDTO);
        }
    }
}

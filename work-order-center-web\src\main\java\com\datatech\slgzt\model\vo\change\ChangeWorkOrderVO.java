package com.datatech.slgzt.model.vo.change;

import com.datatech.slgzt.annotation.ExcelExportHeader;
import com.datatech.slgzt.model.file.UploadFileModel;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 资源开通工单基础信息
 *
 * @Author: liu<PERSON><PERSON>an
 * @Date: 2025/3/13
 */

@Data
public class ChangeWorkOrderVO {

    private String id;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 订单标题
     */
    @ExcelExportHeader(value = "订单标题")
    private String orderTitle;

    /**
     * 订单编号
     */
    @ExcelExportHeader(value = "订单编号")
    private String orderCode;

    /**
     * 订单状态
     */
    private String orderStatus;

    /**
     * 创建者id
     */
    private Long createdBy;

    /**
     * 创建用户名称
     */
    @ExcelExportHeader(value = "申请人名称")
    private String createdUserName;

    private String departmentName;

    /**
     * 修改者id
     */
    private Long updatedBy;

    /**
     * 局方负责人
     */
    private String bureauUserName;

    /**
     * 三级业务部门领导名称
     */
    private String levelThreeLeaderName;

    /**
     * 三级业务部门领导ID
     */
    private Long levelThreeLeaderId;

    /**
     * 二级业务部门领导ID
     */
    private Long businessDepartLeaderId;

    /**
     * 二级业务部门领导名称
     */
    private String businessDepartLeaderName;


    /**
     * 三级云领导ID
     */
    private Long threeLevelCloudLeaderId;

    /**
     * 三级云领导名称
     */
    private String threeLevelCloudLeaderName;


    /**
     * 二级云资源领导id
     */
    private Long secondLevelCloudLeaderId;

    /**
     * 二级云资源领导名称
     */
    private String secondLevelCloudLeaderName;

    /**
     * 业务系统id
     */
    private Long businessSystemId;

    /**
     * 业务系统名称
     */
    @ExcelExportHeader(value = "业务系统名称")
    private String businessSystemName;

    /**
     * 业务系统编码
     */
    private String businessSystemCode;

    /**
     * 模块ID
     */
    private Long moduleId;

    private String moduleName;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 计费号
     */
    private String billId;

    /**
     * 客户编码
     */
    private String customNo;

    /**
     * 厂家名称
     */
    private String manufacturer;

    /**
     * 厂家联系人
     */
    private String manufacturerContacts;

    /**
     * 厂家联系电话
     */
    private String manufacturerMobile;

    /**
     * 订单状态 0 删除 1 正常
     */
    private Integer STATUS;

    /**
     * 流程实例ID
     */
    private String activitiId;

    /**
     * 流程模板KEY
     */
    private String activiteKey;

    /**
     * 流程当前节点code
     */
    private String currentNodeCode;

    /**
     * 当前流程节点名称
     */
    private String currentNodeName;

    /**
     * 订单描述
     */
    private String orderDesc;

    /**
     * 上云资源说明书
     */
    private List<UploadFileModel> resourceApplyFiles;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;

    /**
     * 工单开始时间
     */
    @ExcelExportHeader(value = "发起时间")
    private LocalDateTime workOrderStartTime;

    /**
     * 工单结束时间-完结时间
     */
    private LocalDateTime workOrderEndTime;

    /**
     * 工单当前节点开始时间
     */
    @ExcelExportHeader(value = "当前工单节点开始时间")
    private LocalDateTime currentNodeStartTime;

    private Boolean canRevoke = true;
}


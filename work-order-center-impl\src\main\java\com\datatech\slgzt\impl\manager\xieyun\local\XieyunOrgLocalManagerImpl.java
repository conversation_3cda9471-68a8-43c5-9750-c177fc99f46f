package com.datatech.slgzt.impl.manager.xieyun.local;

import com.datatech.slgzt.convert.XieYunBeanManagerConvert;
import com.datatech.slgzt.dao.container.XieYunOrgDAO;
import com.datatech.slgzt.dao.model.container.XieYunOrgDO;
import com.datatech.slgzt.manager.xieyun.local.XieyunOrgLocalManager;
import com.datatech.slgzt.model.dto.XieYunOrgDTO;
import com.datatech.slgzt.model.query.container.XieYunOrgQuery;
import com.datatech.slgzt.utils.StreamUtils;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 谐云本地组织处理
 *
 * @Author: liupeihan
 * @Date: 2025/4/15
 */

@Service
public class XieyunOrgLocalManagerImpl implements XieyunOrgLocalManager {

    @Resource
    private XieYunOrgDAO orgDAO;

    @Resource
    private XieYunBeanManagerConvert convert;

    @Override
    public void insert(XieYunOrgDTO xieYunOrgDTO) {
        XieYunOrgDO xieYunOrgDO = convert.orgDTO2DO(xieYunOrgDTO);
        orgDAO.insert(xieYunOrgDO);
    }

    @Override
    public void updateUserIdByOrgId(String orgId, String userId) {
        XieYunOrgDO orgDO = new XieYunOrgDO();
        orgDO.setXieYunUserId(userId);
        orgDO.setUpdatedTime(LocalDateTime.now());
        orgDAO.updateUserIdByOrgId(orgDO, orgId);
    }

    @Override
    public void updateOrgQuotaByOrgId(XieYunOrgDTO orgDTO) {
        XieYunOrgDO orgDO = convert.orgDTO2DO(orgDTO);
        orgDO.setUpdatedTime(LocalDateTime.now());
        orgDAO.updateOrgQuotaByOrgId(orgDO);
    }

    @Override
    public List<XieYunOrgDTO> list(XieYunOrgQuery query) {
        return StreamUtils.mapArray(orgDAO.list(query), convert::orgDO2DTO);
    }
}


package com.datatech.slgzt.convert;

import com.datatech.slgzt.model.dto.SlbCertificateDTO;
import com.datatech.slgzt.model.query.SlbCertificateQuery;
import com.datatech.slgzt.model.req.slb.SlbCertificateCreateReq;
import com.datatech.slgzt.model.req.slb.SlbCertificatePageReq;
import com.datatech.slgzt.model.req.slb.SlbCertificateUpdateReq;
import com.datatech.slgzt.model.vo.slb.SlbCertificateVO;
import org.mapstruct.Mapper;

/**
 * SLB证书Web层转换器
 */
@Mapper(componentModel = "spring")
public interface SlbCertificateWebConvert {

    /**
     * 分页请求转Query
     */
    SlbCertificateQuery convert(SlbCertificatePageReq req);

    /**
     * 新增请求转DTO
     */
    SlbCertificateDTO convert(SlbCertificateCreateReq req);

    /**
     * 修改请求转DTO
     */
    SlbCertificateDTO convert(SlbCertificateUpdateReq req);

    /**
     * DTO转VO
     */
    SlbCertificateVO convert(SlbCertificateDTO dto);
} 
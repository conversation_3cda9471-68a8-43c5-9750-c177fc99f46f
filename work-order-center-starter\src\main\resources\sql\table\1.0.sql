-- SLGZT.ACT_EVT_LOG definition

CREATE TABLE SLGZT.ACT_EVT_LOG (
                                   LOG_NR_ NUMBER(19,0) NOT NULL,
                                   TYPE_ NVARCHAR2(256) NULL,
                                   PROC_DEF_ID_ NVARCHAR2(256) NULL,
                                   PROC_INST_ID_ NVARCHAR2(256) NULL,
                                   EXECUTION_ID_ NVARCHAR2(256) NULL,
                                   TASK_ID_ NVARCHAR2(256) NULL,
                                   TIME_STAMP_ TIMESTAMP NOT NULL,
                                   USER_ID_ NVARCHAR2(1020) NULL,
                                   DATA_ BLOB NULL,
                                   LOCK_OWNER_ NVARCHAR2(1020) NULL,
                                   LOCK_TIME_ TIMESTAMP NULL,
                                   IS_PROCESSED_ NUMBER(3,0) DEFAULT 0 NULL,
                                   CONSTRAINT INDEX33564649 PRIMARY KEY (LOG_NR_)
);


-- SLGZT.ACT_GE_PROPERTY definition

CREATE TABLE SLGZT.ACT_GE_PROPERTY (
                                       NAME_ NVARCHAR2(256) NOT NULL,
                                       VALUE_ NVARCHAR2(1200) NULL,
                                       REV_ NUMBER(10,0) NULL,
                                       CONSTRAINT INDEX33564621 PRIMARY KEY (NAME_)
);


-- SLGZT.ACT_HI_ACTINST definition

CREATE TABLE SLGZT.ACT_HI_ACTINST (
                                      ID_ NVARCHAR2(256) NOT NULL,
                                      PROC_DEF_ID_ NVARCHAR2(256) NOT NULL,
                                      PROC_INST_ID_ NVARCHAR2(256) NOT NULL,
                                      EXECUTION_ID_ NVARCHAR2(256) NOT NULL,
                                      ACT_ID_ NVARCHAR2(1020) NOT NULL,
                                      TASK_ID_ NVARCHAR2(256) NULL,
                                      CALL_PROC_INST_ID_ NVARCHAR2(256) NULL,
                                      ACT_NAME_ NVARCHAR2(1020) NULL,
                                      ACT_TYPE_ NVARCHAR2(1020) NOT NULL,
                                      ASSIGNEE_ NVARCHAR2(1020) NULL,
                                      START_TIME_ TIMESTAMP NOT NULL,
                                      END_TIME_ TIMESTAMP NULL,
                                      DURATION_ NUMBER(19,0) NULL,
                                      DELETE_REASON_ NVARCHAR2(8000) NULL,
                                      TENANT_ID_ NVARCHAR2(1020) DEFAULT '' NULL,
                                      CONSTRAINT INDEX33564755 PRIMARY KEY (ID_)
);
CREATE INDEX ACT_IDX_HI_ACT_INST_END ON SLGZT.ACT_HI_ACTINST (END_TIME_);
CREATE INDEX ACT_IDX_HI_ACT_INST_EXEC ON SLGZT.ACT_HI_ACTINST (EXECUTION_ID_,ACT_ID_);
CREATE INDEX ACT_IDX_HI_ACT_INST_PROCINST ON SLGZT.ACT_HI_ACTINST (PROC_INST_ID_,ACT_ID_);
CREATE INDEX ACT_IDX_HI_ACT_INST_START ON SLGZT.ACT_HI_ACTINST (START_TIME_);


-- SLGZT.ACT_HI_ATTACHMENT definition

CREATE TABLE SLGZT.ACT_HI_ATTACHMENT (
                                         ID_ NVARCHAR2(256) NOT NULL,
                                         REV_ NUMBER(10,0) NULL,
                                         USER_ID_ NVARCHAR2(1020) NULL,
                                         NAME_ NVARCHAR2(1020) NULL,
                                         DESCRIPTION_ NVARCHAR2(8000) NULL,
                                         TYPE_ NVARCHAR2(1020) NULL,
                                         TASK_ID_ NVARCHAR2(256) NULL,
                                         PROC_INST_ID_ NVARCHAR2(256) NULL,
                                         URL_ NVARCHAR2(8000) NULL,
                                         CONTENT_ID_ NVARCHAR2(256) NULL,
                                         TIME_ TIMESTAMP NULL,
                                         CONSTRAINT INDEX33564765 PRIMARY KEY (ID_)
);


-- SLGZT.ACT_HI_COMMENT definition

CREATE TABLE SLGZT.ACT_HI_COMMENT (
                                      ID_ NVARCHAR2(256) NOT NULL,
                                      TYPE_ NVARCHAR2(1020) NULL,
                                      TIME_ TIMESTAMP NOT NULL,
                                      USER_ID_ NVARCHAR2(1020) NULL,
                                      TASK_ID_ NVARCHAR2(256) NULL,
                                      PROC_INST_ID_ NVARCHAR2(256) NULL,
                                      ACTION_ NVARCHAR2(1020) NULL,
                                      MESSAGE_ NVARCHAR2(8000) NULL,
                                      FULL_MSG_ BLOB NULL,
                                      CONSTRAINT INDEX33564763 PRIMARY KEY (ID_)
);


-- SLGZT.ACT_HI_DETAIL definition

CREATE TABLE SLGZT.ACT_HI_DETAIL (
                                     ID_ NVARCHAR2(256) NOT NULL,
                                     TYPE_ NVARCHAR2(1020) NOT NULL,
                                     PROC_INST_ID_ NVARCHAR2(256) NULL,
                                     EXECUTION_ID_ NVARCHAR2(256) NULL,
                                     TASK_ID_ NVARCHAR2(256) NULL,
                                     ACT_INST_ID_ NVARCHAR2(256) NULL,
                                     NAME_ NVARCHAR2(1020) NOT NULL,
                                     VAR_TYPE_ NVARCHAR2(256) NULL,
                                     REV_ NUMBER(10,0) NULL,
                                     TIME_ TIMESTAMP NOT NULL,
                                     BYTEARRAY_ID_ NVARCHAR2(256) NULL,
                                     DOUBLE_ NUMBER(38,10) NULL,
                                     LONG_ NUMBER(19,0) NULL,
                                     TEXT_ NVARCHAR2(8000) NULL,
                                     TEXT2_ NVARCHAR2(8000) NULL,
                                     CONSTRAINT INDEX33564761 PRIMARY KEY (ID_)
);
CREATE INDEX ACT_IDX_HI_DETAIL_ACT_INST ON SLGZT.ACT_HI_DETAIL (ACT_INST_ID_);
CREATE INDEX ACT_IDX_HI_DETAIL_NAME ON SLGZT.ACT_HI_DETAIL (NAME_);
CREATE INDEX ACT_IDX_HI_DETAIL_PROC_INST ON SLGZT.ACT_HI_DETAIL (PROC_INST_ID_);
CREATE INDEX ACT_IDX_HI_DETAIL_TASK_ID ON SLGZT.ACT_HI_DETAIL (TASK_ID_);
CREATE INDEX ACT_IDX_HI_DETAIL_TIME ON SLGZT.ACT_HI_DETAIL (TIME_);


-- SLGZT.ACT_HI_IDENTITYLINK definition

CREATE TABLE SLGZT.ACT_HI_IDENTITYLINK (
                                           ID_ NVARCHAR2(256) NOT NULL,
                                           GROUP_ID_ NVARCHAR2(1020) NULL,
                                           TYPE_ NVARCHAR2(1020) NULL,
                                           USER_ID_ NVARCHAR2(1020) NULL,
                                           TASK_ID_ NVARCHAR2(256) NULL,
                                           PROC_INST_ID_ NVARCHAR2(256) NULL,
                                           CONSTRAINT INDEX33564767 PRIMARY KEY (ID_)
);
CREATE INDEX ACT_IDX_HI_IDENT_LNK_PROCINST ON SLGZT.ACT_HI_IDENTITYLINK (PROC_INST_ID_);
CREATE INDEX ACT_IDX_HI_IDENT_LNK_TASK ON SLGZT.ACT_HI_IDENTITYLINK (TASK_ID_);
CREATE INDEX ACT_IDX_HI_IDENT_LNK_USER ON SLGZT.ACT_HI_IDENTITYLINK (USER_ID_);


-- SLGZT.ACT_HI_PROCINST definition

CREATE TABLE SLGZT.ACT_HI_PROCINST (
                                       ID_ NVARCHAR2(256) NOT NULL,
                                       PROC_INST_ID_ NVARCHAR2(256) NOT NULL,
                                       BUSINESS_KEY_ NVARCHAR2(1020) NULL,
                                       PROC_DEF_ID_ NVARCHAR2(256) NOT NULL,
                                       START_TIME_ TIMESTAMP NOT NULL,
                                       END_TIME_ TIMESTAMP NULL,
                                       DURATION_ NUMBER(19,0) NULL,
                                       START_USER_ID_ NVARCHAR2(1020) NULL,
                                       START_ACT_ID_ NVARCHAR2(1020) NULL,
                                       END_ACT_ID_ NVARCHAR2(1020) NULL,
                                       SUPER_PROCESS_INSTANCE_ID_ NVARCHAR2(256) NULL,
                                       DELETE_REASON_ NVARCHAR2(8000) NULL,
                                       TENANT_ID_ NVARCHAR2(1020) DEFAULT '' NULL,
                                       NAME_ NVARCHAR2(1020) NULL,
                                       CONSTRAINT INDEX33564752 PRIMARY KEY (ID_)
);
CREATE INDEX ACT_IDX_HI_PRO_INST_END ON SLGZT.ACT_HI_PROCINST (END_TIME_);
CREATE INDEX ACT_IDX_HI_PRO_I_BUSKEY ON SLGZT.ACT_HI_PROCINST (BUSINESS_KEY_);
CREATE UNIQUE INDEX INDEX33564753 ON SLGZT.ACT_HI_PROCINST (PROC_INST_ID_);


-- SLGZT.ACT_HI_TASKINST definition

CREATE TABLE SLGZT.ACT_HI_TASKINST (
                                       ID_ NVARCHAR2(256) NOT NULL,
                                       PROC_DEF_ID_ NVARCHAR2(256) NULL,
                                       TASK_DEF_KEY_ NVARCHAR2(1020) NULL,
                                       PROC_INST_ID_ NVARCHAR2(256) NULL,
                                       EXECUTION_ID_ NVARCHAR2(256) NULL,
                                       PARENT_TASK_ID_ NVARCHAR2(256) NULL,
                                       NAME_ NVARCHAR2(1020) NULL,
                                       DESCRIPTION_ NVARCHAR2(8000) NULL,
                                       OWNER_ NVARCHAR2(1020) NULL,
                                       ASSIGNEE_ NVARCHAR2(1020) NULL,
                                       START_TIME_ TIMESTAMP NOT NULL,
                                       CLAIM_TIME_ TIMESTAMP NULL,
                                       END_TIME_ TIMESTAMP NULL,
                                       DURATION_ NUMBER(19,0) NULL,
                                       DELETE_REASON_ NVARCHAR2(8000) NULL,
                                       PRIORITY_ NUMBER(10,0) NULL,
                                       DUE_DATE_ TIMESTAMP NULL,
                                       FORM_KEY_ NVARCHAR2(1020) NULL,
                                       CATEGORY_ NVARCHAR2(1020) NULL,
                                       TENANT_ID_ NVARCHAR2(1020) DEFAULT '' NULL,
                                       CONSTRAINT INDEX33564757 PRIMARY KEY (ID_)
);
CREATE INDEX ACT_IDX_HI_TASK_INST_PROCINST ON SLGZT.ACT_HI_TASKINST (PROC_INST_ID_);


-- SLGZT.ACT_HI_VARINST definition

CREATE TABLE SLGZT.ACT_HI_VARINST (
                                      ID_ NVARCHAR2(256) NOT NULL,
                                      PROC_INST_ID_ NVARCHAR2(256) NULL,
                                      EXECUTION_ID_ NVARCHAR2(256) NULL,
                                      TASK_ID_ NVARCHAR2(256) NULL,
                                      NAME_ NVARCHAR2(1020) NOT NULL,
                                      VAR_TYPE_ NVARCHAR2(400) NULL,
                                      REV_ NUMBER(10,0) NULL,
                                      BYTEARRAY_ID_ NVARCHAR2(256) NULL,
                                      DOUBLE_ NUMBER(38,10) NULL,
                                      LONG_ NUMBER(19,0) NULL,
                                      TEXT_ NVARCHAR2(8000) NULL,
                                      TEXT2_ NVARCHAR2(8000) NULL,
                                      CREATE_TIME_ TIMESTAMP NULL,
                                      LAST_UPDATED_TIME_ TIMESTAMP NULL,
                                      CONSTRAINT INDEX33564759 PRIMARY KEY (ID_)
);
CREATE INDEX ACT_IDX_HI_PROCVAR_NAME_TYPE ON SLGZT.ACT_HI_VARINST (NAME_,VAR_TYPE_);
CREATE INDEX ACT_IDX_HI_PROCVAR_PROC_INST ON SLGZT.ACT_HI_VARINST (PROC_INST_ID_);
CREATE INDEX ACT_IDX_HI_PROCVAR_TASK_ID ON SLGZT.ACT_HI_VARINST (TASK_ID_);


-- SLGZT.ACT_ID_GROUP definition

CREATE TABLE SLGZT.ACT_ID_GROUP (
                                    ID_ NVARCHAR2(256) NOT NULL,
                                    REV_ NUMBER(10,0) NULL,
                                    NAME_ NVARCHAR2(1020) NULL,
                                    TYPE_ NVARCHAR2(1020) NULL,
                                    CONSTRAINT INDEX33564787 PRIMARY KEY (ID_)
);


-- SLGZT.ACT_ID_INFO definition

CREATE TABLE SLGZT.ACT_ID_INFO (
                                   ID_ NVARCHAR2(256) NOT NULL,
                                   REV_ NUMBER(10,0) NULL,
                                   USER_ID_ NVARCHAR2(256) NULL,
                                   TYPE_ NVARCHAR2(256) NULL,
                                   KEY_ NVARCHAR2(1020) NULL,
                                   VALUE_ NVARCHAR2(1020) NULL,
                                   PASSWORD_ BLOB NULL,
                                   PARENT_ID_ NVARCHAR2(1020) NULL,
                                   CONSTRAINT INDEX33564793 PRIMARY KEY (ID_)
);


-- SLGZT.ACT_ID_USER definition

CREATE TABLE SLGZT.ACT_ID_USER (
                                   ID_ NVARCHAR2(256) NOT NULL,
                                   REV_ NUMBER(10,0) NULL,
                                   FIRST_ NVARCHAR2(1020) NULL,
                                   LAST_ NVARCHAR2(1020) NULL,
                                   EMAIL_ NVARCHAR2(1020) NULL,
                                   PWD_ NVARCHAR2(1020) NULL,
                                   PICTURE_ID_ NVARCHAR2(256) NULL,
                                   CONSTRAINT INDEX33564791 PRIMARY KEY (ID_)
);


-- SLGZT.ACT_RE_DEPLOYMENT definition

CREATE TABLE SLGZT.ACT_RE_DEPLOYMENT (
                                         ID_ NVARCHAR2(256) NOT NULL,
                                         NAME_ NVARCHAR2(1020) NULL,
                                         CATEGORY_ NVARCHAR2(1020) NULL,
                                         KEY_ NVARCHAR2(1020) NULL,
                                         TENANT_ID_ NVARCHAR2(1020) DEFAULT '' NULL,
                                         DEPLOY_TIME_ TIMESTAMP NULL,
                                         ENGINE_VERSION_ NVARCHAR2(1020) NULL,
                                         CONSTRAINT INDEX33564625 PRIMARY KEY (ID_)
);


-- SLGZT.ACT_RE_PROCDEF definition

CREATE TABLE SLGZT.ACT_RE_PROCDEF (
                                      ID_ NVARCHAR2(256) NOT NULL,
                                      REV_ NUMBER(10,0) NULL,
                                      CATEGORY_ NVARCHAR2(1020) NULL,
                                      NAME_ NVARCHAR2(1020) NULL,
                                      KEY_ NVARCHAR2(1020) NOT NULL,
                                      VERSION_ NUMBER(10,0) NOT NULL,
                                      DEPLOYMENT_ID_ NVARCHAR2(256) NULL,
                                      RESOURCE_NAME_ NVARCHAR2(8000) NULL,
                                      DGRM_RESOURCE_NAME_ VARCHAR(4000) NULL,
                                      DESCRIPTION_ NVARCHAR2(8000) NULL,
                                      HAS_START_FORM_KEY_ NUMBER(1,0) NULL,
                                      HAS_GRAPHICAL_NOTATION_ NUMBER(1,0) NULL,
                                      SUSPENSION_STATE_ NUMBER(10,0) NULL,
                                      TENANT_ID_ NVARCHAR2(1020) DEFAULT '' NULL,
                                      ENGINE_VERSION_ NVARCHAR2(1020) NULL,
                                      CONSTRAINT INDEX33564639 PRIMARY KEY (ID_)
);
CREATE UNIQUE INDEX INDEX33564661 ON SLGZT.ACT_RE_PROCDEF (KEY_,VERSION_,TENANT_ID_);


-- SLGZT.BATCH_JOB_INSTANCE definition

CREATE TABLE SLGZT.BATCH_JOB_INSTANCE (
                                          JOB_INSTANCE_ID NUMBER(19,0) NOT NULL,
                                          VERSION NUMBER(19,0) NULL,
                                          JOB_NAME VARCHAR2(400) NOT NULL,
                                          JOB_KEY VARCHAR2(128) NOT NULL,
                                          CONSTRAINT INDEX33565566 PRIMARY KEY (JOB_INSTANCE_ID)
);
CREATE UNIQUE INDEX INDEX33565567 ON SLGZT.BATCH_JOB_INSTANCE (JOB_NAME,JOB_KEY);


-- SLGZT.CCMP_ASSET_SET_BAK definition

CREATE TABLE SLGZT.CCMP_ASSET_SET_BAK (
                                          ID NUMBER NOT NULL,
                                          TENANT_ID NUMBER NULL,
                                          ASSET_SET_NAME VARCHAR2(255) NULL,
                                          ASSET_SET_CODE VARCHAR2(32) NULL,
                                          ASSET_SET_TYPE VARCHAR2(32) NULL,
                                          ASSET_SET_LEVEL NUMBER NULL,
                                          OWNER_ID NUMBER NULL,
                                          CREATED_BY NUMBER NULL,
                                          CREATED_TIME TIMESTAMP DEFAULT SYSDATE NULL,
                                          UPDATED_BY NUMBER NULL,
                                          UPDATED_TIME TIMESTAMP NULL,
                                          STATUS NUMBER DEFAULT 1 NULL,
                                          DESCRIPTION VARCHAR2(1024) NULL,
                                          OAC_BUSINESS_SYSTEM_ID NUMBER NULL,
                                          CONSTRAINT INDEX33564151 PRIMARY KEY (ID)
);


-- SLGZT.CCMP_BILLING_ACCOUNT definition

CREATE TABLE SLGZT.CCMP_BILLING_ACCOUNT (
                                            ID NUMBER NOT NULL,
                                            BILL_ID VARCHAR(32) NULL,
                                            CUSTOM_ID VARCHAR(32) NULL,
                                            CUSTOM_NO VARCHAR(32) NULL,
                                            TENANT_ID NUMBER NULL,
                                            "TYPE" NUMBER NULL,
                                            BILL_TYPE NUMBER NULL,
                                            PASSWORD VARCHAR(128) NULL,
                                            B_BILLING_DATE TIMESTAMP NULL,
                                            EXP_DATE TIMESTAMP NULL,
                                            PACKAGE_ID VARCHAR(32) NULL,
                                            CREATED_BY NUMBER NULL,
                                            CREATED_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL,
                                            UPDATED_BY NUMBER NULL,
                                            UPDATED_TIME TIMESTAMP NULL,
                                            STATUS NUMBER DEFAULT 1 NULL,
                                            VDC_ID VARCHAR(64) NULL,
                                            DOMAIN_ID VARCHAR(64) NULL,
                                            CONSTRAINT INDEX33564152 PRIMARY KEY (ID)
);


-- SLGZT.CCMP_CUSTOM definition

CREATE TABLE SLGZT.CCMP_CUSTOM (
                                   ID VARCHAR2(32) NOT NULL,
                                   TITLE VARCHAR2(200) NULL,
                                   CUSTOM_NO VARCHAR2(32) NULL,
                                   CUSTOM_NAME VARCHAR2(200) NULL,
                                   C_MANAGER_CONTACT VARCHAR2(128) NULL,
                                   C_MANAGER_CONTACT_PHONE VARCHAR2(32) NULL,
                                   CONTACT_MOBILE VARCHAR2(32) NULL,
                                   CONTACT_NAME VARCHAR2(128) NULL,
                                   CITY_CODE VARCHAR2(32) NULL,
                                   AREA_CODE VARCHAR2(32) NULL,
                                   CREATED_BY VARCHAR2(32) NULL,
                                   CREATED_TIME TIMESTAMP DEFAULT SYSDATE NULL,
                                   BILLING_DATE TIMESTAMP NULL,
                                   STREET VARCHAR2(512) NULL,
                                   INDUSTRY_ONE VARCHAR2(50) NULL,
                                   INDUSTRY_TWO VARCHAR2(50) NULL,
                                   POST_CODE VARCHAR2(50) NULL,
                                   CERT_TYPE VARCHAR2(32) NULL,
                                   CERT_NUMBER VARCHAR2(128) NULL,
                                   UPDATED_BY VARCHAR2(32) NULL,
                                   UPDATED_TIME TIMESTAMP NULL,
                                   STATUS NUMBER DEFAULT 1 NULL,
                                   POOL_POLICY VARCHAR2(32) DEFAULT 'all' NULL,
                                   ORG_ID NUMBER(36,0) NULL,
                                   CONSTRAINT INDEX33564153 PRIMARY KEY (ID)
);


-- SLGZT.CCMP_ERROR_LOGS definition

CREATE TABLE SLGZT.CCMP_ERROR_LOGS (
                                       ID VARCHAR2(64) NULL,
                                       CREATED_TIME TIMESTAMP DEFAULT SYSDATE NULL,
                                       "TYPE" NUMBER(1,0) NULL,
                                       PARENT_ID VARCHAR2(64) NULL,
                                       CREATED_BY NUMBER(12,0) NULL,
                                       ERROR_CODE VARCHAR2(64) NULL,
                                       ERROR_CHILD_DESC VARCHAR2(256) NULL,
                                       REQUEST_URI VARCHAR2(256) NULL,
                                       EXCEPTION_NAME VARCHAR2(128) NULL,
                                       "METHOD" VARCHAR2(12) NULL,
                                       ERROR_INFO CLOB DEFAULT '' NULL,
                                       ERROR_PARENT_DESC VARCHAR2(256) NULL
);


-- SLGZT.CCMP_ESOP_SYNC_LOG definition

CREATE TABLE SLGZT.CCMP_ESOP_SYNC_LOG (
                                          ID NUMBER NOT NULL,
                                          SHEET_TYPE NVARCHAR2(128) NULL,
                                          SERVICE_TYPE NVARCHAR2(128) NULL,
                                          SERIAL_NO VARCHAR2(64) NULL,
                                          SER_SUPPLIER NVARCHAR2(512) NULL,
                                          SER_CALLER NVARCHAR2(512) NULL,
                                          CALLER_PWD NVARCHAR2(128) NULL,
                                          CALL_TIME NVARCHAR2(128) NULL,
                                          OP_PERSON NVARCHAR2(512) NULL,
                                          OP_CORP NVARCHAR2(512) NULL,
                                          OP_DEPART NVARCHAR2(512) NULL,
                                          OP_CONTACT NVARCHAR2(128) NULL,
                                          OP_TIME NVARCHAR2(128) NULL,
                                          INPUT_TIME TIMESTAMP DEFAULT SYSDATE NULL,
                                          OP_DETAIL CLOB NULL,
                                          CUSTOM_ID VARCHAR2(32) NULL,
                                          USER_ID NUMBER NULL,
                                          SET_MEAL_CODE NVARCHAR2(128) NULL,
                                          CLOUD_PLATFORM_CODE NVARCHAR2(128) NULL,
                                          CLOUD_PLATFORM_CITY_CODE NVARCHAR2(128) NULL,
                                          SYNC_STATUS NUMBER NULL,
                                          RETURN_MESSAGE NVARCHAR2(4096) NULL,
                                          CREATED_BY NUMBER NULL,
                                          CREATED_TIME TIMESTAMP DEFAULT SYSDATE NULL,
                                          UPDATED_BY NUMBER NULL,
                                          UPDATED_TIME TIMESTAMP NULL,
                                          STATUS NUMBER NULL,
                                          BILLING_ACCOUNT_ID NUMBER NULL,
                                          CONSTRAINT INDEX33564154 PRIMARY KEY (ID)
);


-- SLGZT.CCMP_GID definition

CREATE TABLE SLGZT.CCMP_GID (
                                ID VARCHAR2(36) NOT NULL,
                                RESOURCE_ID VARCHAR2(64) NULL,
                                GID VARCHAR2(64) NULL,
                                "TYPE" VARCHAR2(64) NULL,
                                DOMAIN_CODE VARCHAR2(64) NULL,
                                CREATED_TIME TIMESTAMP NULL,
                                UPDATED_TIME TIMESTAMP NULL,
                                SYNC_CMDB NUMBER DEFAULT 0 NULL,
                                STATUS VARCHAR2(64) NULL,
                                DELETED NUMBER DEFAULT 1 NULL,
                                MESSAGE VARCHAR2(1000) NULL,
                                OUT_INSTANCE_ID VARCHAR2(64) NULL,
                                CONSTRAINT INDEX33564155 PRIMARY KEY (ID)
);
CREATE INDEX D_PK_RESOURCE_ID_TYPE ON SLGZT.CCMP_GID (RESOURCE_ID,"TYPE");


-- SLGZT.CCMP_RESULT_CODE definition

CREATE TABLE SLGZT.CCMP_RESULT_CODE (
                                        ID VARCHAR2(64) NOT NULL,
                                        CODE VARCHAR2(128) NULL,
                                        CODE_CHILD_DESC VARCHAR2(255) NULL,
                                        CREATED_TIME TIMESTAMP DEFAULT SYSDATE NULL,
                                        CREATED_BY NUMBER(12,0) NULL,
                                        STATUS NUMBER(1,0) NULL,
                                        EXCEPTION_NAME VARCHAR2(256) NULL,
                                        CODE_PARENT_DESC VARCHAR2(64) NULL,
                                        CONSTRAINT INDEX33564156 PRIMARY KEY (ID)
);


-- SLGZT.CLOUD_RDS definition

CREATE TABLE SLGZT.CLOUD_RDS (
                                 ID VARCHAR2(40) NULL,
                                 NAME VARCHAR2(128) NULL,
                                 CLOUD_PLATFORM_CODE VARCHAR2(40) NULL,
                                 PROJECT_ID VARCHAR2(40) NULL,
                                 FLAVOR_CODE VARCHAR2(40) NULL,
                                 VPC_ID VARCHAR2(40) NULL,
                                 SUBNET_ID VARCHAR2(40) NULL,
                                 SECURITYGROUP_ID VARCHAR2(40) NULL,
                                 VERSION VARCHAR2(40) NULL,
                                 DEPLOY_TYPE VARCHAR2(40) NULL,
                                 TENANT_ID NUMBER NULL,
                                 STATUS VARCHAR2(40) NULL,
                                 STORAGE_TYPE VARCHAR2(40) NULL,
                                 IP_ADDRESS VARCHAR2(40) NULL,
                                 DB_ENGINE VARCHAR2(40) NULL,
                                 TABLES_LOWER NUMBER NULL,
                                 TIME_ZONE VARCHAR2(40) NULL,
                                 RESOURCE_ID VARCHAR2(40) NULL,
                                 CREATED_BY VARCHAR2(40) NULL,
                                 CREATED_TIME TIMESTAMP NULL,
                                 UPDATED_BY VARCHAR2(40) NULL,
                                 UPDATED_TIME TIMESTAMP NULL,
                                 GID VARCHAR2(40) NULL,
                                 SYNC_UPDATED_TIME TIMESTAMP NULL,
                                 DESCRIPTION VARCHAR2(50) NULL,
                                 AZ_ID NUMBER(20,0) NULL,
                                 FLAVOR_ID VARCHAR2(50) NULL,
                                 MASTER_INSTANCE_ID VARCHAR2(50) NULL,
                                 DELETED NUMBER(10,0) DEFAULT 1 NULL,
                                 INSTANCE_TYPE VARCHAR2(50) DEFAULT 'normal' NULL,
                                 RDS_TYPE VARCHAR2(64) NULL,
                                 STORAGE NUMBER(20,0) NULL,
                                 REGION_ID NUMBER(20,0) NULL,
                                 DB_PORT NUMBER(10,0) NULL,
                                 CLOUD_TENANT_ID NUMBER(20,0) NULL,
                                 CMP_TENANT_ID NUMBER(20,0) NULL,
                                 VDC_CODE VARCHAR2(60) NULL,
                                 INSTANCE_UUID VARCHAR2(36) NULL
);


-- SLGZT.CMP_APP_MEMBER_T definition

CREATE TABLE SLGZT.CMP_APP_MEMBER_T (
                                        ID NUMBER(12,0) NOT NULL,
                                        APP_ID NUMBER(12,0) NULL,
                                        USER_ID VARCHAR(512) NULL,
                                        STATUS NUMBER DEFAULT 1 NULL,
                                        CREATED_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL,
                                        CREATED_BY NUMBER NULL,
                                        UPDATED_BY NUMBER NULL,
                                        UPDATED_TIME TIMESTAMP NULL,
                                        CONSTRAINT INDEX33564157 PRIMARY KEY (ID)
);


-- SLGZT.CMP_APP_MODULE_CMDB_T definition

CREATE TABLE SLGZT.CMP_APP_MODULE_CMDB_T (
                                             ID VARCHAR2(36) NOT NULL,
                                             APP_ID NUMBER NULL,
                                             APP_MODULE_ID NUMBER NULL,
                                             MODULE_CMDB_NAME VARCHAR2(128) NULL,
                                             CMDB_ID VARCHAR2(36) NULL,
                                             REGION_CODE VARCHAR2(64) NULL,
                                             LIFE_CYCLE CHAR(1) NULL,
                                             CMDB_APP_VPC VARCHAR2(50) NULL,
                                             CONSTRAINT INDEX33565662 PRIMARY KEY (ID)
);


-- SLGZT.CMP_APP_MODULE_T definition

CREATE TABLE SLGZT.CMP_APP_MODULE_T (
                                        ID NUMBER(12,0) NOT NULL,
                                        APP_ID NUMBER(12,0) NULL,
                                        MODULE_NAME VARCHAR(512) NULL,
                                        STATUS NUMBER DEFAULT 1 NULL,
                                        CREATED_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL,
                                        CREATED_BY NUMBER NULL,
                                        UPDATED_BY NUMBER NULL,
                                        UPDATED_TIME TIMESTAMP NULL,
                                        CONSTRAINT INDEX33564158 PRIMARY KEY (ID)
);


-- SLGZT.CMP_APP_T definition

CREATE TABLE SLGZT.CMP_APP_T (
                                 ID NUMBER NOT NULL,
                                 SYSTEM_CODE VARCHAR(255) NULL,
                                 SYSTEM_NAME VARCHAR(300) NULL,
                                 SYSTEM_DESC VARCHAR(600) NULL,
                                 TENANT_ID NUMBER NULL,
                                 ORG_ID NUMBER NULL,
                                 CONTACT_NAME VARCHAR(90) NULL,
                                 CONTACT_PHONE VARCHAR(255) DEFAULT NULL NULL,
                                 DEFAULT_FLAG NUMBER DEFAULT 1 NULL,
                                 CREATED_BY NUMBER NULL,
                                 CREATED_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL,
                                 UPDATED_BY NUMBER NULL,
                                 UPDATED_TIME TIMESTAMP NULL,
                                 STATUS NUMBER DEFAULT 1 NOT NULL,
                                 OWNER_ID NUMBER NULL,
                                 FIRST_FIELD_ID NUMBER NULL,
                                 ATTACH_ID NUMBER NULL,
                                 MANUFACTURER_USER_ID VARCHAR(64) NULL,
                                 APPLY_USER_ID NUMBER NULL,
                                 DEPARTMENT_NAME VARCHAR(128) NULL,
                                 APPLY_USER_MOBILE VARCHAR(11) NULL,
                                 SYS_MAINTAIN_MOBILE VARCHAR(11) NULL,
                                 MANUFACTURER_NAME VARCHAR(300) NULL,
                                 MANUFACTURER_MOBILE VARCHAR(11) NULL,
                                 IMPORTANCE_LEVEL_ID VARCHAR(50) NULL,
                                 ENABLE_CROSS VARCHAR(32) NULL,
                                 MANUFACTURER_CONTACT_NAME VARCHAR2(50) NULL,
                                 MANUFACTURER_CONTACT_EMAIL VARCHAR2(128) NULL,
                                 LIFE_CYCLE CHAR(1) NULL,
                                 CONSTRAINT INDEX33564159 PRIMARY KEY (ID)
);


-- SLGZT.CMP_BILL_DATA_RECORD_T definition

CREATE TABLE SLGZT.CMP_BILL_DATA_RECORD_T (
                                              ID NUMBER(16,0) NOT NULL,
                                              DOMAIN_CODE VARCHAR2(64) NULL,
                                              PRODUCT_TYPE VARCHAR2(32) NULL,
                                              MESSAGE VARCHAR2(255) NULL,
                                              PROCESS_STATUS VARCHAR2(16) NULL,
                                              FILE_NAME VARCHAR2(64) NULL,
                                              TEXT_CONTENT VARCHAR2(4000) NULL,
                                              FOLDER_NAME VARCHAR2(255) NULL,
                                              PROJECT_ID VARCHAR2(64) NULL,
                                              AZ_CODE VARCHAR2(64) NULL,
                                              REGION_CODE VARCHAR2(32) NULL,
                                              SERVICE_TYPE VARCHAR2(32) NULL,
                                              RESOURCE_TYPE VARCHAR2(32) NULL,
                                              TIME_STAMP VARCHAR2(16) NULL,
                                              RECORD_TYPE VARCHAR2(16) NULL,
                                              SPEC_CODE VARCHAR2(32) NULL,
                                              RESOURCE_ID VARCHAR2(128) NULL,
                                              VOLUME_ID VARCHAR2(128) NULL,
                                              USAGE_QUANTITY VARCHAR2(16) NULL,
                                              BILL_START_TIME TIMESTAMP NULL,
                                              BILL_END_TIME TIMESTAMP NULL,
                                              HANDLE_STATUS NUMBER(2,0) NULL,
                                              NUM NUMBER(16,0) NULL,
                                              USAGE_TIME VARCHAR2(16) NULL,
                                              CONSTRAINT INDEX33564619 PRIMARY KEY (ID)
);


-- SLGZT.CMP_BILL_PATROL_RECORD_T definition

CREATE TABLE SLGZT.CMP_BILL_PATROL_RECORD_T (
                                                ID VARCHAR2(64) NOT NULL,
                                                PATROL_MESSAGE VARCHAR2(4000) NOT NULL,
                                                PATROL_POSITION VARCHAR2(16) NOT NULL,
                                                RESOURCE_ID VARCHAR2(64) NULL,
                                                CREATED_TIME TIMESTAMP NOT NULL,
                                                CONSTRAINT INDEX33564615 PRIMARY KEY (ID)
);


-- SLGZT.CMP_BILL_PATROL_RECORD_T_BAK definition

CREATE TABLE SLGZT.CMP_BILL_PATROL_RECORD_T_BAK (
                                                    ID VARCHAR2(64) NOT NULL,
                                                    PATROL_MESSAGE VARCHAR2(4000) NOT NULL,
                                                    PATROL_POSITION VARCHAR2(16) NOT NULL,
                                                    RESOURCE_ID VARCHAR2(64) NULL,
                                                    CREATED_TIME TIMESTAMP NOT NULL,
                                                    CONSTRAINT INDEX33565743 PRIMARY KEY (ID)
);


-- SLGZT.CMP_BOSS_BILL_MONTH_T definition

CREATE TABLE SLGZT.CMP_BOSS_BILL_MONTH_T (
                                             ID VARCHAR2(64) NOT NULL,
                                             DOMAIN_CODE VARCHAR2(128) NOT NULL,
                                             ACCOUNT VARCHAR2(32) NOT NULL,
                                             BILL_CODE VARCHAR2(64) NULL,
                                             "TIMESTAMP" VARCHAR2(16) NOT NULL,
                                             START_TIME TIMESTAMP NOT NULL,
                                             END_TIME TIMESTAMP NOT NULL,
                                             BILL_STATUS NUMBER(2,0) NOT NULL,
                                             PRICE NUMBER(16,4) NOT NULL,
                                             BOSS_PRICE NUMBER(16,0) NOT NULL,
                                             CREATED_TIME TIMESTAMP NOT NULL,
                                             CONSTRAINT INDEX33564613 PRIMARY KEY (ID)
);


-- SLGZT.CMP_BOSS_BILL_T definition

CREATE TABLE SLGZT.CMP_BOSS_BILL_T (
                                       ID VARCHAR2(64) NOT NULL,
                                       DOMAIN_CODE VARCHAR2(128) NOT NULL,
                                       ACCOUNT VARCHAR2(32) NOT NULL,
                                       CITIES_CODE VARCHAR2(32) NULL,
                                       BILL_CODE VARCHAR2(64) NULL,
                                       START_TIME TIMESTAMP NOT NULL,
                                       END_TIME TIMESTAMP NOT NULL,
                                       BILL_STATUS NUMBER(2,0) NOT NULL,
                                       PRICE NUMBER(16,4) NOT NULL,
                                       BOSS_PRICE NUMBER(16,0) NOT NULL,
                                       "TIMESTAMP" VARCHAR2(16) NOT NULL,
                                       CREATED_TIME TIMESTAMP NOT NULL,
                                       CONSTRAINT INDEX33564611 PRIMARY KEY (ID)
);


-- SLGZT.CMP_CHARGE_BILL_CODE_T definition

CREATE TABLE SLGZT.CMP_CHARGE_BILL_CODE_T (
                                              ID VARCHAR2(32) NOT NULL,
                                              DOMAIN_CODE VARCHAR2(128) NOT NULL,
                                              RESOURCE_TYPE VARCHAR2(64) NOT NULL,
                                              SET_MEAL_CODE VARCHAR2(128) NOT NULL,
                                              STATUS NUMBER(1,0) DEFAULT 1 NOT NULL,
                                              CREATED_TIME TIMESTAMP NULL,
                                              BILL_CODE VARCHAR2(128) NOT NULL,
                                              CONSTRAINT INDEX33564819 PRIMARY KEY (ID)
);


-- SLGZT.CMP_CHARGE_BILL_T definition

CREATE TABLE SLGZT.CMP_CHARGE_BILL_T (
                                         ID VARCHAR2(64) NOT NULL,
                                         DOMAIN_CODE VARCHAR2(128) NOT NULL,
                                         RESOURCE_TYPE VARCHAR2(64) NOT NULL,
                                         RESOURCE_ID VARCHAR2(64) NOT NULL,
                                         ACCOUNT VARCHAR2(32) NULL,
                                         BILL_CODE VARCHAR2(64) NULL,
                                         BILL_STATUS NUMBER(2,0) NOT NULL,
                                         START_TIME TIMESTAMP NOT NULL,
                                         END_TIME TIMESTAMP NOT NULL,
                                         BILL_PRICE NUMBER(16,4) NOT NULL,
                                         "TIMESTAMP" VARCHAR2(16) NOT NULL,
                                         CREATED_TIME TIMESTAMP NOT NULL,
                                         CONSTRAINT INDEX33564609 PRIMARY KEY (ID)
);


-- SLGZT.CMP_CHARGE_RESOURCE_OPERATE_T definition

CREATE TABLE SLGZT.CMP_CHARGE_RESOURCE_OPERATE_T (
                                                     ID VARCHAR2(32) NOT NULL,
                                                     CHARGE_RES_ID VARCHAR2(128) NOT NULL,
                                                     OPERATE_TYPE VARCHAR2(64) NOT NULL,
                                                     RESOURCE_ID VARCHAR2(64) NOT NULL,
                                                     RESOURCE_TYPE VARCHAR2(64) NOT NULL,
                                                     SPEC_INFO VARCHAR2(128) NOT NULL,
                                                     CREATED_TIME TIMESTAMP NOT NULL,
                                                     "SIZE" NUMBER(16,4) NULL,
                                                     SYS_VOL_TYPE VARCHAR2(16) NULL,
                                                     SYS_VOL_SIZE NUMBER(16,4) NULL,
                                                     CONSTRAINT INDEX33564607 PRIMARY KEY (ID)
);


-- SLGZT.CMP_CHARGE_RESOURCE_T definition

CREATE TABLE SLGZT.CMP_CHARGE_RESOURCE_T (
                                             ID VARCHAR2(64) NOT NULL,
                                             DOMAIN_CODE VARCHAR2(128) NULL,
                                             ACCOUNT VARCHAR2(32) NULL,
                                             BILL_CODE VARCHAR2(64) NULL,
                                             SPEC_INFO VARCHAR2(128) NOT NULL,
                                             RESOURCE_TYPE VARCHAR2(64) NOT NULL,
                                             RESOURCE_ID VARCHAR2(64) NOT NULL,
                                             PROJECT_ID VARCHAR2(64) NULL,
                                             BILL_TYPE VARCHAR2(16) NOT NULL,
                                             START_TIME TIMESTAMP NOT NULL,
                                             END_TIME TIMESTAMP NULL,
                                             MESSAGE VARCHAR2(255) NULL,
                                             "SIZE" NUMBER(16,4) NOT NULL,
                                             SYS_VOL_TYPE VARCHAR2(16) NULL,
                                             SYS_VOL_SIZE NUMBER(16,4) NULL,
                                             CREATED_TIME TIMESTAMP NOT NULL,
                                             CONSTRAINT INDEX33564605 PRIMARY KEY (ID)
);


-- SLGZT.CMP_COMPANY_T definition

CREATE TABLE SLGZT.CMP_COMPANY_T (
                                     ID VARCHAR(36) NOT NULL,
                                     DN VARCHAR(255) NOT NULL,
                                     NAME VARCHAR(255) NOT NULL,
                                     ORG_NUM VARCHAR(255) NOT NULL,
                                     ORG_NUMNEW VARCHAR(255) NOT NULL,
                                     ORG_NUM12 VARCHAR(255) NOT NULL,
                                     PARENT_ORGDN VARCHAR(255) NULL,
                                     PARENT_ORGNUM VARCHAR(255) NULL,
                                     COMP_TYPE VARCHAR(10) NOT NULL,
                                     COMPOSITOR NUMBER(10,0) NOT NULL,
                                     DELETED NUMBER(1,0) NOT NULL,
                                     LAST_TIME NUMBER(32,0) DEFAULT 0 NOT NULL,
                                     LAST_TIMESTR VARCHAR(255) NOT NULL,
                                     COMP_DESC VARCHAR(64) NULL,
                                     PARENT_ORGNUM12 VARCHAR(64) NULL,
                                     CONSTRAINT INDEX33564160 PRIMARY KEY (ID)
);


-- SLGZT.CMP_MENU definition

CREATE TABLE SLGZT.CMP_MENU (
                                ID VARCHAR2(64) NOT NULL,
                                NAME VARCHAR2(128) NULL,
                                CODE VARCHAR2(36) NULL,
                                "TYPE" NUMBER(1,0) NULL,
                                URL VARCHAR2(512) NULL,
                                ICON VARCHAR2(512) NULL,
                                DESCRIPTION VARCHAR2(512) NULL,
                                PARENT_ID VARCHAR2(64) NULL,
                                STATUS NUMBER(1,0) NULL,
                                CREATED_TIME TIMESTAMP DEFAULT SYSDATE NULL,
                                UPDATED_TIME TIMESTAMP NULL,
                                CREATED_BY VARCHAR2(64) NULL,
                                UPDATED_BY VARCHAR2(64) NULL,
                                CREATED_USER_NAME VARCHAR2(64) NULL,
                                CONSTRAINT INDEX33564161 PRIMARY KEY (ID)
);


-- SLGZT.CMP_ORG_T definition

CREATE TABLE SLGZT.CMP_ORG_T (
                                 ID NUMBER NOT NULL,
                                 ORG_NAME VARCHAR(1024) NULL,
                                 ORG_CODE VARCHAR(1024) NULL,
                                 ORG_NUM VARCHAR(128) NULL,
                                 PARENT_ORG_NUM VARCHAR(128) NULL,
                                 PARENT_ORG_DN VARCHAR(128) NULL,
                                 OA_LAST_TIME TIMESTAMP NULL,
                                 CREATED_BY NUMBER NULL,
                                 CREATED_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL,
                                 UPDATED_BY NUMBER NULL,
                                 UPDATED_TIME TIMESTAMP NULL,
                                 STATUS NUMBER DEFAULT 1 NULL,
                                 ORG_NUMNEW VARCHAR(1024) NULL,
                                 ORG_NUM12 VARCHAR(64) NULL,
                                 COMPANY_DN VARCHAR(64) NULL,
                                 COMPOSITOR VARCHAR(16) NULL,
                                 PARENT_ORG_NUMNEW VARCHAR(32) NULL,
                                 CAN_VIEW VARCHAR(10) NULL,
                                 MANAGER_ID VARCHAR(32) NULL,
                                 CONSTRAINT INDEX33564162 PRIMARY KEY (ID)
);


-- SLGZT.CMP_ORG_USER_T definition

CREATE TABLE SLGZT.CMP_ORG_USER_T (
                                      ID NUMBER NOT NULL,
                                      LOGIN_NAME VARCHAR(32) NULL,
                                      USER_NAME VARCHAR(32) NULL,
                                      PHONE VARCHAR(32) NULL,
                                      SEX VARCHAR(32) NULL,
                                      PWD VARCHAR(32) NULL,
                                      USER_TYPE VARCHAR(32) NULL,
                                      USER_EMAIL VARCHAR(64) NULL,
                                      ORG_ID NUMBER NULL,
                                      CUSTOM_ID VARCHAR(32) NULL,
                                      DEPARTMENT_DN VARCHAR(1024) NULL,
                                      OA_LAST_TIME TIMESTAMP NULL,
                                      JOB_NAME VARCHAR(256) NULL,
                                      SORT NUMBER NULL,
                                      ACTIVE_STATUS NUMBER NULL,
                                      CREATED_BY NUMBER NULL,
                                      CREATED_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL,
                                      UPDATED_BY VARCHAR(32) NULL,
                                      UPDATED_TIME TIMESTAMP NULL,
                                      STATUS NUMBER DEFAULT 1 NULL,
                                      CONSTRAINT INDEX33564163 PRIMARY KEY (ID)
);


-- SLGZT.CMP_PRODUCT_UNIT_PRICE_T definition

CREATE TABLE SLGZT.CMP_PRODUCT_UNIT_PRICE_T (
                                                ID VARCHAR2(36) NOT NULL,
                                                DOMAIN_CODE VARCHAR2(128) NOT NULL,
                                                SPEC_INFO VARCHAR2(128) NOT NULL,
                                                PRICE NUMBER(32,8) NOT NULL,
                                                RESOURCE_TYPE VARCHAR2(64) NOT NULL,
                                                STATUS NUMBER NOT NULL,
                                                CREATED_TIME TIMESTAMP DEFAULT SYSDATE NOT NULL,
                                                CONSTRAINT INDEX33564603 PRIMARY KEY (ID)
);


-- SLGZT.CMP_REPORT_RESOURCE_DATA_T definition

CREATE TABLE SLGZT.CMP_REPORT_RESOURCE_DATA_T (
                                                  ID NUMBER(16,0) NOT NULL,
                                                  DOMAIN_CODE VARCHAR2(64) NULL,
                                                  PROJECT_ID VARCHAR2(64) NULL,
                                                  RESOURCE_TYPE VARCHAR2(32) NULL,
                                                  RESOURCE_ID VARCHAR2(128) NULL,
                                                  TIME_STAMP VARCHAR2(32) NULL,
                                                  BILL_TYPE VARCHAR2(64) NULL,
                                                  SPEC_CODE VARCHAR2(64) NULL,
                                                  VOLUME_ID VARCHAR2(128) NULL,
                                                  USAGE_QUANTITY VARCHAR2(16) NULL,
                                                  BILL_START_TIME TIMESTAMP NULL,
                                                  BILL_END_TIME TIMESTAMP NULL,
                                                  NUM NUMBER(16,0) NULL,
                                                  CONSTRAINT INDEX33564617 PRIMARY KEY (ID)
);


-- SLGZT.CMP_ROLE definition

CREATE TABLE SLGZT.CMP_ROLE (
                                ID NUMBER(12,0) NOT NULL,
                                NAME VARCHAR(128) NULL,
                                DESCRIPTION VARCHAR(255) NULL,
                                "TYPE" NUMBER(1,0) NULL,
                                IS_PRESET NUMBER(1,0) DEFAULT 0 NULL,
                                CODE VARCHAR(36) NULL,
                                TENANT_ID NUMBER(12,0) NULL,
                                STATUS NUMBER(1,0) NULL,
                                CREATED_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL,
                                UPDATED_TIME TIMESTAMP NULL,
                                CREATED_BY NUMBER(12,0) NULL,
                                UPDATED_BY NUMBER(12,0) NULL,
                                "SCOPE" VARCHAR(32) NULL,
                                ENTITY_ID VARCHAR(32) NULL,
                                DOMAIN_CODE VARCHAR(32) NULL,
                                ROLE_ENTITY_ID NUMBER NULL,
                                SORT NUMBER NULL,
                                IS_DEFAULT NUMBER(1,0) NULL,
                                CREATED_USER_NAME VARCHAR(50) NULL
);


-- SLGZT.CMP_ROLE_PRIVILEGE_T definition

CREATE TABLE SLGZT.CMP_ROLE_PRIVILEGE_T (
                                            ID NUMBER(38,0) NOT NULL,
                                            ROLE_ID VARCHAR(64) NULL,
                                            PRIVILEGE_ID VARCHAR(64) NULL,
                                            STATUS NUMBER(1,0) NULL,
                                            CREATED_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL,
                                            CREATED_BY VARCHAR(12) NULL,
                                            CONSTRAINT INDEX33564164 PRIMARY KEY (ID)
);


-- SLGZT.CMP_TENANT definition

CREATE TABLE SLGZT.CMP_TENANT (
                                  ID NUMBER(12,0) NOT NULL,
                                  NAME VARCHAR2(100) NULL,
                                  EMAIL VARCHAR2(128) NULL,
                                  ADDRESS VARCHAR2(128) NULL,
                                  DESCRIPTION VARCHAR2(512) NULL,
                                  STATUS NUMBER(1,0) DEFAULT 1 NULL,
                                  CREATED_BY NUMBER(12,0) NULL,
                                  CREATED_TIME TIMESTAMP DEFAULT SYSDATE NULL,
                                  UPDATED_BY NUMBER(12,0) NULL,
                                  UPDATED_TIME TIMESTAMP NULL,
                                  CUSTOM_NO VARCHAR2(128) NULL,
                                  SET_MEAL_CODE VARCHAR2(32) NULL,
                                  CLOUD_PLATFORM_CODE VARCHAR2(32) NULL,
                                  CLOUD_PLATFORM_CITY_CODE VARCHAR2(32) NULL,
                                  CUSTOM_ID VARCHAR2(32) NULL,
                                  USER_ID VARCHAR2(32) NULL,
                                  BILL_ID VARCHAR2(32) NULL,
                                  CLOUD_DOMIAN VARCHAR2(255) NULL,
                                  CITY_CODE VARCHAR2(32) NULL,
                                  CLOUD_TENANT_ID VARCHAR2(255) NULL,
                                  CLOUD_TENANT_NAME VARCHAR2(255) NULL,
                                  TENANT_LEVEL VARCHAR2(32) NULL,
                                  RESERVE_TWO VARCHAR2(32) NULL,
                                  RESERVE_THREE VARCHAR2(32) NULL,
                                  DOMAIN_CODE VARCHAR2(32) NULL,
                                  TENANT_TYPE NUMBER DEFAULT 1 NULL,
                                  TENANT_TAG VARCHAR2(16) DEFAULT 'GENERAL' NULL,
                                  IOE NUMBER(1,0) NULL,
                                  ORG_ID NUMBER(20,0) NULL,
                                  OWNER_ID NUMBER(20,0) NULL,
                                  CODE VARCHAR2(64) NULL,
                                  CMDB_ID VARCHAR2(128) NULL,
                                  CONSTRAINT INDEX33564165 PRIMARY KEY (ID)
);


-- SLGZT.CMP_TENANT_ACCOUNT definition

CREATE TABLE SLGZT.CMP_TENANT_ACCOUNT (
                                          ID NUMBER NOT NULL,
                                          TENANT_ID NUMBER NULL,
                                          USER_ID NUMBER NULL,
                                          PRODUCT_ID NUMBER NULL,
                                          STATUS VARCHAR(1) NULL,
                                          CREATED_BY NUMBER NULL,
                                          CREATED_TIME TIMESTAMP NULL,
                                          UPDATED_BY NUMBER NULL,
                                          UPDATED_TIME TIMESTAMP NULL,
                                          ACCOUNT_ID VARCHAR(32) NULL,
                                          CONSTRAINT INDEX33564166 PRIMARY KEY (ID)
);


-- SLGZT.CMP_USER definition

CREATE TABLE SLGZT.CMP_USER (
                                ID NUMBER(12,0) NOT NULL,
                                ACCOUNT VARCHAR(50) NULL,
                                USERNAME VARCHAR(128) NULL,
                                EMAIL VARCHAR(128) NULL,
                                MOBILEPHONE VARCHAR(32) NULL,
                                SEX NUMBER(1,0) NULL,
                                USER_TYPE VARCHAR(32) NULL,
                                STATUS NUMBER(1,0) NULL,
                                CREATED_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL,
                                UPDATED_TIME TIMESTAMP NULL,
                                CREATED_BY NUMBER(12,0) NULL,
                                UPDATED_BY NUMBER(12,0) NULL,
                                ACCOUNT_TYPE VARCHAR(32) DEFAULT 'BILLING' NULL,
                                PASSWORD VARCHAR(100) NULL,
                                B_BILLING_DATE TIMESTAMP NULL,
                                EXP_DATE TIMESTAMP NULL,
                                USER_ID VARCHAR(64) NULL,
                                CUSTOMER_CONTACT VARCHAR(256) NULL,
                                CONTACT_PHONE VARCHAR(32) NULL,
                                CMDB_ID VARCHAR(32) NULL,
                                FACTORY_NAME VARCHAR(64) NULL,
                                RESERVE_THREE VARCHAR(32) NULL,
                                CUSTOM_ID VARCHAR(32) NULL,
                                SET_MEAL_CODE VARCHAR(32) NULL,
                                CLOUD_PLATFORM_CODE VARCHAR(32) NULL,
                                CLOUD_PLATFORM_CITY_CODE VARCHAR(32) NULL,
                                CUSTOM_NO VARCHAR(32) NULL,
                                DESCRIPTION VARCHAR(200) NULL,
                                TENANT_ID NUMBER(12,0) NULL,
                                PERMISSION_POLICY VARCHAR(10) DEFAULT 'SPECIFY' NULL,
                                PACKAGE_ID NUMBER(12,0) DEFAULT 600000445594 NULL,
                                PACKAGE_TYPE_CODE VARCHAR(32) DEFAULT 'pkg_prov_moc' NULL,
                                "TYPE" NUMBER(1,0) NULL,
                                BILL_TYPE VARCHAR(32) NULL,
                                PLATFORM_CODE VARCHAR(64) NULL,
                                CITY_CODE VARCHAR(32) NULL,
                                SALT VARCHAR(64) NULL,
                                FIRST_LOGIN NUMBER(1,0) NULL,
                                ORG_ID VARCHAR(64) NULL,
                                DEPARTMENT_DN VARCHAR(64) NULL,
                                OA_LAST_TIME TIMESTAMP NULL,
                                JOB_NAME VARCHAR(64) NULL,
                                SORT NUMBER(32,0) NULL,
                                CONSTRAINT INDEX33564167 PRIMARY KEY (ID)
);


-- SLGZT.CMP_USER_ROLE definition

CREATE TABLE SLGZT.CMP_USER_ROLE (
                                     ID NUMBER(12,0) NOT NULL,
                                     ROLE_ID NUMBER(12,0) NULL,
                                     USER_ID NUMBER(12,0) NULL,
                                     CREATED_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                     CREATED_BY NUMBER(12,0) NULL,
                                     STATUS NUMBER(1,0) DEFAULT 1 NULL,
                                     CONSTRAINT INDEX33564799 PRIMARY KEY (ID)
);


-- SLGZT."CMP_USER_ROLE_zyf0207" definition

CREATE TABLE SLGZT."CMP_USER_ROLE_zyf0207" (
                                               ID NUMBER(12,0) NOT NULL,
                                               ROLE_ID NUMBER(12,0) NULL,
                                               USER_ID NUMBER(12,0) NULL,
                                               CREATED_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                               CREATED_BY NUMBER(12,0) NULL,
                                               STATUS NUMBER(1,0) DEFAULT 1 NULL,
                                               CONSTRAINT INDEX33564168 PRIMARY KEY (ID)
);


-- SLGZT."CMP_USER_zyf_0121" definition

CREATE TABLE SLGZT."CMP_USER_zyf_0121" (
                                           ID NUMBER(12,0) NOT NULL,
                                           ACCOUNT VARCHAR(50) NULL,
                                           USERNAME VARCHAR(128) NULL,
                                           EMAIL VARCHAR(128) NULL,
                                           MOBILEPHONE VARCHAR(32) NULL,
                                           SEX NUMBER(1,0) NULL,
                                           USER_TYPE VARCHAR(32) NULL,
                                           STATUS NUMBER(1,0) NULL,
                                           CREATED_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL,
                                           UPDATED_TIME TIMESTAMP NULL,
                                           CREATED_BY NUMBER(12,0) NULL,
                                           UPDATED_BY NUMBER(12,0) NULL,
                                           ACCOUNT_TYPE VARCHAR(32) DEFAULT 'BILLING' NULL,
                                           PASSWORD VARCHAR(100) NULL,
                                           B_BILLING_DATE TIMESTAMP NULL,
                                           EXP_DATE TIMESTAMP NULL,
                                           USER_ID VARCHAR(64) NULL,
                                           CUSTOMER_CONTACT VARCHAR(256) NULL,
                                           CONTACT_PHONE VARCHAR(32) NULL,
                                           CMDB_ID VARCHAR(32) NULL,
                                           FACTORY_NAME VARCHAR(64) NULL,
                                           RESERVE_THREE VARCHAR(32) NULL,
                                           CUSTOM_ID VARCHAR(32) NULL,
                                           SET_MEAL_CODE VARCHAR(32) NULL,
                                           CLOUD_PLATFORM_CODE VARCHAR(32) NULL,
                                           CLOUD_PLATFORM_CITY_CODE VARCHAR(32) NULL,
                                           CUSTOM_NO VARCHAR(32) NULL,
                                           DESCRIPTION VARCHAR(200) NULL,
                                           TENANT_ID NUMBER(12,0) NULL,
                                           PERMISSION_POLICY VARCHAR(10) DEFAULT 'SPECIFY' NULL,
                                           PACKAGE_ID NUMBER(12,0) DEFAULT 600000445594 NULL,
                                           PACKAGE_TYPE_CODE VARCHAR(32) DEFAULT 'pkg_prov_moc' NULL,
                                           "TYPE" NUMBER(1,0) NULL,
                                           BILL_TYPE VARCHAR(32) NULL,
                                           PLATFORM_CODE VARCHAR(64) NULL,
                                           CITY_CODE VARCHAR(32) NULL,
                                           SALT VARCHAR(64) NULL,
                                           FIRST_LOGIN NUMBER(1,0) NULL,
                                           ORG_ID VARCHAR(64) NULL,
                                           DEPARTMENT_DN VARCHAR(64) NULL,
                                           OA_LAST_TIME TIMESTAMP NULL,
                                           JOB_NAME VARCHAR(64) NULL,
                                           SORT NUMBER(32,0) NULL,
                                           CONSTRAINT INDEX33564169 PRIMARY KEY (ID)
);


-- SLGZT.CPC_CONFIG definition

CREATE TABLE SLGZT.CPC_CONFIG (
                                  ID VARCHAR2(32) NULL,
                                  CONFIG_TYPE VARCHAR2(64) NULL,
                                  CONFIG_CODE VARCHAR2(64) NULL,
                                  CONFIG_NAME NVARCHAR2(512) NULL,
                                  CONFIG_VALUE VARCHAR2(16000) NULL,
                                  CONFIG_DESC NVARCHAR2(2048) NULL,
                                  SORT NUMBER(5,0) NULL,
                                  CREATED_BY NUMBER(12,0) NULL,
                                  CREATED_TIME TIMESTAMP DEFAULT SYSDATE NULL,
                                  UPDATED_BY NUMBER(12,0) NULL,
                                  UPDATED_TIME TIMESTAMP NULL,
                                  STATUS NUMBER(1,0) DEFAULT 1 NULL,
                                  DOMAIN_CODE VARCHAR2(128) NULL,
                                  PARENT_ID VARCHAR2(32) NULL
);


-- SLGZT.CPC_EXPENSES definition

CREATE TABLE SLGZT.CPC_EXPENSES (
                                    ID VARCHAR2(36) NOT NULL,
                                    EXPENSES_CODE VARCHAR2(128) NULL,
                                    EXPENSES_NAME VARCHAR2(128) NULL,
                                    EXPENSES_DESC VARCHAR2(512) NULL,
                                    EXPENSES_TYPE VARCHAR2(64) NULL,
                                    BILLING_PLAN VARCHAR2(64) NULL,
                                    PRICE NUMBER(32,8) NULL,
                                    BILLING_CODE VARCHAR2(128) NULL,
                                    CREATED_BY NUMBER NULL,
                                    CREATED_TIME TIMESTAMP NULL,
                                    UPDATED_BY NUMBER NULL,
                                    UPDATED_TIME TIMESTAMP NULL,
                                    EFFECTIVE_DATE TIMESTAMP NULL,
                                    EXPIRE_TIME TIMESTAMP NULL,
                                    STATUS VARCHAR2(16) NULL,
                                    "OPEN" VARCHAR2(16) NULL,
                                    BILLING_TYPE VARCHAR2(16) NULL,
                                    DURATION NUMBER(1,0) NULL,
                                    CONSTRAINT INDEX33564805 PRIMARY KEY (ID)
);


-- SLGZT.CPC_PROJECT definition

CREATE TABLE SLGZT.CPC_PROJECT (
                                   ID VARCHAR2(32) NOT NULL,
                                   SYSTEM_CODE VARCHAR2(64) NULL,
                                   SYSTEM_NAME VARCHAR2(255) NULL,
                                   NETWORK_TYPE VARCHAR2(32) NULL,
                                   DESCRIPTION VARCHAR2(512) NULL,
                                   USER_ID VARCHAR2(64) NULL,
                                   TENANT_ID VARCHAR2(64) NULL,
                                   DOMAIN_CODE VARCHAR2(64) NULL,
                                   COMPANY_NAME VARCHAR2(128) NULL,
                                   CONTACTS VARCHAR2(128) NULL,
                                   CONTACT_PHONE VARCHAR2(32) NULL,
                                   TEST_PROJECT NUMBER NULL,
                                   PREPAYMENT NUMBER NULL,
                                   UNIFY_PAYMENT NUMBER NULL,
                                   CREATED_BY NUMBER NULL,
                                   CREATED_TIME TIMESTAMP NULL,
                                   UPDATED_BY NUMBER NULL,
                                   UPDATED_TIME TIMESTAMP NULL,
                                   STATUS NUMBER DEFAULT 1 NULL,
                                   ACCOUNT_FLAG NUMBER NULL,
                                   EXTERNAL_SYSTEM_CODE VARCHAR2(64) NULL,
                                   CUSTOM_ID VARCHAR2(36) NULL,
                                   BILL_ID VARCHAR2(36) NULL,
                                   CITY_CODE VARCHAR2(36) NULL,
                                   REGION_ID NUMBER NULL,
                                   CLOUD_PROJECT_ID VARCHAR2(64) NULL,
                                   VDC_ID NUMBER(12,0) NULL,
                                   CONSTRAINT INDEX33564170 PRIMARY KEY (ID)
);


-- SLGZT.CPC_RESOURCE_POOL definition

CREATE TABLE SLGZT.CPC_RESOURCE_POOL (
                                         ID VARCHAR2(32) NOT NULL,
                                         POOL_CODE VARCHAR2(64) NULL,
                                         POOL_NAME VARCHAR2(128) NULL,
                                         POOL_DESC VARCHAR2(512) NULL,
                                         AZ_CODE VARCHAR2(64) NULL,
                                         POOL_LEVEL VARCHAR2(32) NULL,
                                         DOMAIN_CODE VARCHAR2(32) NULL,
                                         REGION_CODE VARCHAR2(64) NULL,
                                         PLATFORM_ID VARCHAR2(32) NULL,
                                         CREATED_BY NUMBER NULL,
                                         CREATED_TIME TIMESTAMP DEFAULT SYSDATE NULL,
                                         UPDATED_BY NUMBER NULL,
                                         UPDATED_TIME TIMESTAMP NULL,
                                         STATUS NUMBER DEFAULT 1 NULL,
                                         "OPEN" NUMBER DEFAULT 1 NULL,
                                         EXTERNAL_RESOURCE_POOL_CODE VARCHAR2(64) NULL,
                                         CITY_CODE NUMBER NULL,
                                         "TYPE" VARCHAR2(10) NULL,
                                         LOCATION VARCHAR2(36) NULL,
                                         PROVIDER VARCHAR2(36) NULL,
                                         AUTHURL VARCHAR2(36) NULL,
                                         AREA_CODE VARCHAR2(100) NULL,
                                         RESOURCE_CITY_TYPE VARCHAR2(100) NULL,
                                         CONSTRAINT INDEX33564171 PRIMARY KEY (ID)
);


-- SLGZT.CPC_SERVICE definition

CREATE TABLE SLGZT.CPC_SERVICE (
                                   ID VARCHAR2(36) NOT NULL,
                                   SERVICE_CODE VARCHAR2(255) NULL,
                                   SERVICE_NAME VARCHAR2(255) NULL,
                                   SERVICE_DESC VARCHAR2(2000) NULL,
                                   STACK_CODE VARCHAR2(64) NULL,
                                   BILLING_PLAN VARCHAR2(64) NULL,
                                   PRICE NUMBER(32,8) NULL,
                                   BILLING_CODE VARCHAR2(128) NULL,
                                   CREATED_BY NUMBER NULL,
                                   CREATED_TIME TIMESTAMP NULL,
                                   UPDATED_BY NUMBER NULL,
                                   UPDATED_TIME TIMESTAMP NULL,
                                   STATUS VARCHAR2(16) NULL,
                                   ENABLED VARCHAR2(32) NULL,
                                   SPEC_CODE VARCHAR2(64) NULL,
                                   "OPEN" VARCHAR2(16) NULL,
                                   SERVICE_TYPE VARCHAR2(32) NULL,
                                   SERVICE_CATEGORY_ID VARCHAR2(32) NULL,
                                   PLATFORM_ID VARCHAR2(64) NULL,
                                   DOMAIN_CODE VARCHAR2(32) NULL,
                                   SUBSCRIBE_TYPE VARCHAR2(32) NULL,
                                   MIN_RANGE NUMBER NULL,
                                   MAX_RANGE NUMBER NULL,
                                   SCOPES NUMBER NULL,
                                   RESOURCE_TYPE VARCHAR2(128) NULL,
                                   SPEC_NAME VARCHAR2(50) NULL,
                                   SPEC_INFO VARCHAR2(32) NULL,
                                   PACKAGE_TYPE_CODE VARCHAR2(32) NULL,
                                   CONSTRAINT INDEX33564809 PRIMARY KEY (ID)
);


-- SLGZT.CPC_SERVICE_EXPENSES_REL definition

CREATE TABLE SLGZT.CPC_SERVICE_EXPENSES_REL (
                                                ID NUMBER(10,0) NOT NULL AUTO_INCREMENT,
                                                SERVICE_ID VARCHAR2(32) NOT NULL,
                                                EXPENSES_ID VARCHAR2(32) NOT NULL,
                                                EFFECTIVE_DATE TIMESTAMP NULL,
                                                EXPIRE_TIME TIMESTAMP NULL,
                                                REL_LEVEL NUMBER NULL,
                                                CREATED_BY NUMBER NULL,
                                                CREATED_TIME TIMESTAMP NULL,
                                                STATUS NUMBER DEFAULT 1 NULL,
                                                VERSION_CODE VARCHAR2(32) NULL,
                                                CONSTRAINT INDEX33564817 PRIMARY KEY (ID)
);


-- SLGZT.ERC_LAYOUT_API definition

CREATE TABLE SLGZT.ERC_LAYOUT_API (
                                      ID VARCHAR2(32) NOT NULL,
                                      NAME VARCHAR2(128) NULL,
                                      CLASS_NAME VARCHAR2(128) NULL,
                                      METHOD_NAME VARCHAR2(128) NULL,
                                      API_TYPE VARCHAR2(32) NULL,
                                      DESCRIPTION VARCHAR2(32) NULL,
                                      CREATED_TIME TIMESTAMP NULL,
                                      UPDATED_TIME TIMESTAMP NULL,
                                      CREATED_BY NUMBER NULL,
                                      UPDATED_BY NUMBER NULL,
                                      STATUS NUMBER DEFAULT 1 NULL,
                                      CONSTRAINT INDEX33564222 PRIMARY KEY (ID)
);


-- SLGZT.ERC_LAYOUT_API_DEF definition

CREATE TABLE SLGZT.ERC_LAYOUT_API_DEF (
                                          ID VARCHAR2(32) NOT NULL,
                                          API_NAME VARCHAR2(128) NULL,
                                          API_CODE VARCHAR2(32) NULL,
                                          CLASS_NAME VARCHAR2(128) NULL,
                                          METHOD_NAME VARCHAR2(128) NULL,
                                          API_TYPE VARCHAR2(32) NULL,
                                          DESCRIPTION VARCHAR2(512) NULL,
                                          CREATED_TIME TIMESTAMP NULL,
                                          UPDATED_TIME TIMESTAMP NULL,
                                          CREATED_BY NUMBER NULL,
                                          UPDATED_BY NUMBER NULL,
                                          STATUS NUMBER DEFAULT 1 NULL,
                                          CONSTRAINT INDEX33564223 PRIMARY KEY (ID)
);


-- SLGZT.ERC_LAYOUT_PARAM definition

CREATE TABLE SLGZT.ERC_LAYOUT_PARAM (
                                        ID VARCHAR2(32) NOT NULL,
                                        PARAM_NAME VARCHAR2(128) NULL,
                                        PARAM_CODE VARCHAR2(32) NULL,
                                        PARAM_VALUE CLOB NULL,
                                        BEAN_NAME VARCHAR2(128) NULL,
                                        DESCRIPTION VARCHAR2(512) NULL,
                                        REVISION NUMBER DEFAULT 0 NULL,
                                        CREATED_TIME TIMESTAMP NULL,
                                        UPDATED_TIME TIMESTAMP NULL,
                                        CREATED_BY NUMBER NULL,
                                        UPDATED_BY NUMBER NULL,
                                        STATUS NUMBER DEFAULT 1 NULL,
                                        CONSTRAINT INDEX33564224 PRIMARY KEY (ID)
);


-- SLGZT.ERC_LAYOUT_PARAM_DEF definition

CREATE TABLE SLGZT.ERC_LAYOUT_PARAM_DEF (
                                            ID VARCHAR2(32) NOT NULL,
                                            PARAM_NAME VARCHAR2(128) NULL,
                                            PARAM_CODE VARCHAR2(36) NULL,
                                            PARAM_VALUE CLOB NULL,
                                            BEAN_NAME VARCHAR2(128) NULL,
                                            DESCRIPTION VARCHAR2(512) NULL,
                                            CREATED_TIME TIMESTAMP NULL,
                                            UPDATED_TIME TIMESTAMP NULL,
                                            CREATED_BY NUMBER NULL,
                                            UPDATED_BY NUMBER NULL,
                                            STATUS NUMBER DEFAULT 1 NULL,
                                            CONSTRAINT INDEX33564225 PRIMARY KEY (ID)
);


-- SLGZT.ERC_LAYOUT_TASK definition

CREATE TABLE SLGZT.ERC_LAYOUT_TASK (
                                       ID VARCHAR2(32) NOT NULL,
                                       TASK_NAME VARCHAR2(64) NULL,
                                       TEMPLATE_ID VARCHAR2(32) NULL,
                                       SUB_ORDER_ID VARCHAR2(64) NULL,
                                       TASK_TYPE VARCHAR2(32) NULL,
                                       START_TIME TIMESTAMP NULL,
                                       END_TIME TIMESTAMP NULL,
                                       STATE VARCHAR2(32) NULL,
                                       REVISION NUMBER DEFAULT 0 NULL,
                                       CREATED_TIME TIMESTAMP NULL,
                                       UPDATED_TIME TIMESTAMP NULL,
                                       CREATED_BY NUMBER NULL,
                                       UPDATED_BY NUMBER NULL,
                                       STATUS NUMBER DEFAULT 1 NULL,
                                       MESSAGE VARCHAR2(4000) NULL,
                                       RETRY_COUNT NUMBER DEFAULT 0 NULL,
                                       TASK_CODE VARCHAR2(32) NULL,
                                       INSTANCE_ID VARCHAR2(36) NULL,
                                       TASK_SOURCE NUMBER NULL,
                                       SOURCE_EXT_TYPE VARCHAR(500) NULL,
                                       CONSTRAINT INDEX33564226 PRIMARY KEY (ID)
);


-- SLGZT.ERC_LAYOUT_TASK_DEF definition

CREATE TABLE SLGZT.ERC_LAYOUT_TASK_DEF (
                                           ID VARCHAR2(36) NOT NULL,
                                           TASK_NAME VARCHAR2(128) NULL,
                                           TASK_CODE VARCHAR2(32) NULL,
                                           TASK_TYPE VARCHAR2(32) NULL,
                                           DESCRIPTION VARCHAR2(512) NULL,
                                           CREATED_TIME TIMESTAMP NULL,
                                           UPDATED_TIME TIMESTAMP NULL,
                                           CREATED_BY NUMBER NULL,
                                           UPDATED_BY NUMBER NULL,
                                           STATUS NUMBER DEFAULT 1 NULL,
                                           API_TYPE VARCHAR2(32) NULL,
                                           API_ID VARCHAR2(32) NULL,
                                           PRODUCT_TYPE VARCHAR2(64) NULL,
                                           CONSTRAINT INDEX33564227 PRIMARY KEY (ID)
);


-- SLGZT.ERC_LAYOUT_TASK_NODE definition

CREATE TABLE SLGZT.ERC_LAYOUT_TASK_NODE (
                                            ID VARCHAR2(36) NOT NULL,
                                            TASK_NAME VARCHAR2(128) NULL,
                                            TASK_CODE VARCHAR2(32) NULL,
                                            MASTER_TASK_ID VARCHAR2(32) NULL,
                                            ORDER_ID VARCHAR2(64) NULL,
                                            TASK_TYPE VARCHAR2(32) NULL,
                                            CLOUD_TASK_ID VARCHAR2(64) NULL,
                                            RESOURCE_ID VARCHAR2(64) NULL,
                                            RETRY_COUNT NUMBER DEFAULT 0 NULL,
                                            MONITOR_COUNT NUMBER NULL,
                                            MONITOR_INTERVAL NUMBER NULL,
                                            MESSAGE VARCHAR2(3072) NULL,
                                            START_TIME TIMESTAMP NULL,
                                            END_TIME TIMESTAMP NULL,
                                            REVISION NUMBER DEFAULT 0 NULL,
                                            CREATED_TIME TIMESTAMP NULL,
                                            UPDATED_TIME TIMESTAMP NULL,
                                            CREATED_BY NUMBER NULL,
                                            UPDATED_BY NUMBER NULL,
                                            STATE VARCHAR2(32) NULL,
                                            STATUS NUMBER DEFAULT 1 NULL,
                                            GID VARCHAR2(50) NULL,
                                            INSTANCE_ID VARCHAR2(50) NULL,
                                            NOTICE_STATE VARCHAR2(50) NULL,
                                            PRODUCT_TYPE VARCHAR2(50) NULL,
                                            CONSTRAINT INDEX33564228 PRIMARY KEY (ID)
);


-- SLGZT.ERC_LAYOUT_TEMPLATE_DEF definition

CREATE TABLE SLGZT.ERC_LAYOUT_TEMPLATE_DEF (
                                               ID VARCHAR2(36) NOT NULL,
                                               TEMPLATE_NAME VARCHAR2(128) NULL,
                                               TEMPLATE_CODE VARCHAR2(64) NULL,
                                               TEMPLATE_TYPE VARCHAR2(32) NULL,
                                               RESOURCE_TYPE VARCHAR2(32) NULL,
                                               PURPOSE VARCHAR2(512) NULL,
                                               CREATED_TIME TIMESTAMP NULL,
                                               UPDATED_TIME TIMESTAMP NULL,
                                               CREATED_BY NUMBER NULL,
                                               UPDATED_BY NUMBER NULL,
                                               STATUS NUMBER DEFAULT 1 NULL,
                                               VERSIONS VARCHAR2(32) NULL,
                                               CONSTRAINT INDEX33564229 PRIMARY KEY (ID)
);


-- SLGZT.ERC_TASK_API_DEF definition

CREATE TABLE SLGZT.ERC_TASK_API_DEF (
                                        ID VARCHAR2(36) NOT NULL,
                                        TEMPLATE_ID VARCHAR2(32) NULL,
                                        TASK_ID VARCHAR2(32) NULL,
                                        API_ID VARCHAR2(32) NULL,
                                        MONITOR_COUNT NUMBER NULL,
                                        MONITOR_INTERVAL NUMBER NULL,
                                        CREATED_TIME TIMESTAMP NULL,
                                        UPDATED_TIME TIMESTAMP NULL,
                                        CREATED_BY NUMBER NULL,
                                        UPDATED_BY NUMBER NULL,
                                        STATUS NUMBER DEFAULT 1 NULL,
                                        CONSTRAINT INDEX33564232 PRIMARY KEY (ID)
);


-- SLGZT.ERC_TASK_NODE_API definition

CREATE TABLE SLGZT.ERC_TASK_NODE_API (
                                         ID VARCHAR2(36) NOT NULL,
                                         TASK_ID VARCHAR2(32) NULL,
                                         API_ID VARCHAR2(32) NULL,
                                         CREATED_TIME TIMESTAMP NULL,
                                         UPDATED_TIME TIMESTAMP NULL,
                                         CREATED_BY NUMBER NULL,
                                         UPDATED_BY NUMBER NULL,
                                         STATUS NUMBER DEFAULT 1 NULL,
                                         CONSTRAINT INDEX33564233 PRIMARY KEY (ID)
);


-- SLGZT.ERC_TASK_PARAM definition

CREATE TABLE SLGZT.ERC_TASK_PARAM (
                                      ID VARCHAR2(36) NOT NULL,
                                      MASTER_TASK_ID VARCHAR2(32) NULL,
                                      PARAM_ID VARCHAR2(32) NULL,
                                      CREATED_TIME TIMESTAMP NULL,
                                      UPDATED_TIME TIMESTAMP NULL,
                                      CREATED_BY NUMBER NULL,
                                      UPDATED_BY NUMBER NULL,
                                      STATUS NUMBER DEFAULT 1 NULL,
                                      CONSTRAINT INDEX33564234 PRIMARY KEY (ID)
);


-- SLGZT.ERC_TASK_PARAM_DEF definition

CREATE TABLE SLGZT.ERC_TASK_PARAM_DEF (
                                          ID VARCHAR2(36) NOT NULL,
                                          TEMPLATE_ID VARCHAR2(32) NULL,
                                          PARAM_ID VARCHAR2(32) NULL,
                                          CREATED_TIME TIMESTAMP NULL,
                                          UPDATED_TIME TIMESTAMP NULL,
                                          CREATED_BY NUMBER NULL,
                                          UPDATED_BY NUMBER NULL,
                                          STATUS NUMBER DEFAULT 1 NULL,
                                          CONSTRAINT INDEX33564235 PRIMARY KEY (ID)
);


-- SLGZT.ERC_TASKS_REL definition

CREATE TABLE SLGZT.ERC_TASKS_REL (
                                     ID VARCHAR2(36) NOT NULL,
                                     TASK_ID VARCHAR2(32) NULL,
                                     REL_TASK_ID VARCHAR2(32) NULL,
                                     CREATED_TIME TIMESTAMP NULL,
                                     UPDATED_TIME TIMESTAMP NULL,
                                     CREATED_BY NUMBER NULL,
                                     UPDATED_BY NUMBER NULL,
                                     STATUS NUMBER DEFAULT 1 NULL,
                                     DESCRIPTION VARCHAR2(255) NULL,
                                     REL_TYPE VARCHAR2(32) NULL,
                                     REL_STATE NUMBER NULL,
                                     CONSTRAINT INDEX33564230 PRIMARY KEY (ID)
);


-- SLGZT.ERC_TASKS_REL_DEF definition

CREATE TABLE SLGZT.ERC_TASKS_REL_DEF (
                                         ID VARCHAR2(36) NOT NULL,
                                         TEMPLATE_ID VARCHAR2(32) NULL,
                                         TASK_ID VARCHAR2(36) NULL,
                                         REL_TASK_ID VARCHAR2(36) NULL,
                                         CREATED_TIME TIMESTAMP NULL,
                                         UPDATED_TIME TIMESTAMP NULL,
                                         CREATED_BY NUMBER NULL,
                                         UPDATED_BY NUMBER NULL,
                                         STATUS NUMBER DEFAULT 1 NULL,
                                         REL_TYPE VARCHAR2(36) NULL,
                                         DESCRIPTION VARCHAR2(255) NULL,
                                         REL_STATE NUMBER NULL,
                                         CONSTRAINT INDEX33564231 PRIMARY KEY (ID)
);


-- SLGZT.ERC_TEMPLATE_TASK_DEF definition

CREATE TABLE SLGZT.ERC_TEMPLATE_TASK_DEF (
                                             ID VARCHAR2(36) NOT NULL,
                                             TEMPLATE_ID VARCHAR2(36) NULL,
                                             TASK_ID VARCHAR2(36) NULL,
                                             RETRY_COUNT NUMBER DEFAULT 0 NULL,
                                             REL_RULE VARCHAR2(32) NULL,
                                             CREATED_TIME TIMESTAMP NULL,
                                             UPDATED_TIME TIMESTAMP NULL,
                                             CREATED_BY NUMBER NULL,
                                             UPDATED_BY NUMBER NULL,
                                             STATUS NUMBER DEFAULT 1 NULL,
                                             CONSTRAINT INDEX33564236 PRIMARY KEY (ID)
);


-- SLGZT.MC_AZ_NETWORK_T definition

CREATE TABLE SLGZT.MC_AZ_NETWORK_T (
                                       ID NVARCHAR2(128) NOT NULL,
                                       NETWORK_ID VARCHAR2(36) NULL,
                                       AZ_ID NUMBER NULL,
                                       CREATED_TIME TIMESTAMP NULL,
                                       UPDATED_TIME TIMESTAMP NULL,
                                       DELETED NUMBER DEFAULT 1 NULL,
                                       CONSTRAINT "D_sys_c007696" PRIMARY KEY (ID)
);


-- SLGZT.MC_AZ_T definition

CREATE TABLE SLGZT.MC_AZ_T (
                               ID NUMBER NOT NULL,
                               NAME VARCHAR2(64) NULL,
                               LABEL_NAME VARCHAR2(64) NULL,
                               DESCRIPTION VARCHAR2(512) NULL,
                               REGION_ID NUMBER NULL,
                               DOMAIN_ID NUMBER NULL,
                               "TYPE" VARCHAR2(36) NULL,
                               RESOURCE_ID VARCHAR2(50) NULL,
                               CREATED_TIME TIMESTAMP NULL,
                               UPDATED_TIME TIMESTAMP NULL,
                               DELETED NUMBER DEFAULT 1 NULL,
                               RESOURCE_POOL_CODE VARCHAR2(36) NULL,
                               CODE VARCHAR2(255) NULL,
                               CREATED_BY VARCHAR2(255) NULL,
                               CREATED_BY_NAME VARCHAR2(128) NULL,
                               CONSTRAINT INDEX33564237 PRIMARY KEY (ID)
);


-- SLGZT.MC_BACKUP_SERVICE_T definition

CREATE TABLE SLGZT.MC_BACKUP_SERVICE_T (
                                           ID VARCHAR2(32) NOT NULL,
                                           NAME VARCHAR2(256) NULL,
                                           BACKUP_TYPE VARCHAR2(32) NULL,
                                           CLOUD_PLATFORM_CODE VARCHAR2(128) NULL,
                                           REGION_ID NUMBER NULL,
                                           CLOUD_TENANT_ID VARCHAR2(128) NULL,
                                           TENANT_ID VARCHAR2(64) NULL,
                                           VDC_ID VARCHAR2(128) NULL,
                                           VDC_CODE VARCHAR2(50) NULL,
                                           STATUS VARCHAR2(32) NULL,
                                           RESOURCE_ID VARCHAR2(64) NULL,
                                           "Gid" VARCHAR2(64) NULL,
                                           CREATED_BY VARCHAR2(128) NULL,
                                           CREATED_TIME TIMESTAMP NULL,
                                           UPDATED_BY VARCHAR2(128) NULL,
                                           UPDATED_TIME TIMESTAMP NULL,
                                           INSTANCE_UUID VARCHAR2(128) NULL,
                                           ZONE_ID VARCHAR2(64) NULL,
                                           DEVICE_ID VARCHAR2(64) NULL,
                                           CONSTRAINT INDEX33565357 PRIMARY KEY (ID)
);


-- SLGZT.MC_CLOUD_PORT_T definition

CREATE TABLE SLGZT.MC_CLOUD_PORT_T (
                                       ID VARCHAR(255) NOT NULL,
                                       NAME VARCHAR(255) NULL,
                                       ORDER_AREA VARCHAR(255) NULL,
                                       GROUP_ID VARCHAR(255) NULL,
                                       BILL_ID VARCHAR(255) NULL,
                                       PLATFORM_TYPE VARCHAR(255) NULL,
                                       PLATFORM_CODE VARCHAR(255) NULL,
                                       TENANT_ID VARCHAR(255) NULL,
                                       REGION_CODE VARCHAR(255) NULL,
                                       VPC_ID VARCHAR(255) NULL,
                                       VDC_CODE VARCHAR(255) NULL,
                                       SERVICE_ID VARCHAR(255) NULL,
                                       SERVER_FIREWALL_TYPE VARCHAR(255) NULL,
                                       FIREWALL_ORDER_ID VARCHAR(255) NULL,
                                       VPN_NAME VARCHAR(255) NULL,
                                       PEER_AS_NUMBER VARCHAR(255) NULL,
                                       VLAN_ID VARCHAR(255) NULL,
                                       PEER_IP VARCHAR(255) NULL,
                                       SRC_IP VARCHAR(255) NULL,
                                       PEER_PASSWORD VARCHAR(255) NULL,
                                       DESCRIPTION TEXT NULL,
                                       SUBNET_ID VARCHAR2(64) NULL,
                                       LOCAL_BGP_AS_NUM VARCHAR2(64) NULL,
                                       PHYSICAL_CONNECTION VARCHAR2(64) NULL,
                                       ROUTE_MODEL VARCHAR2(64) NULL,
                                       REMOTE_CIDR VARCHAR2(64) NULL,
                                       RESOURCE_ACCESS_ID VARCHAR2(64) NULL,
                                       RESOURCE_ID VARCHAR(255) NULL,
                                       CREATED_TIME TIMESTAMP NULL,
                                       UPDATED_TIME TIMESTAMP NULL,
                                       DELETED NUMBER(19,0) NULL,
                                       CREATED_BY NUMBER(19,0) NULL,
                                       UPDATED_BY NUMBER(19,0) NULL,
                                       INSTANCE_UUID VARCHAR(255) NULL,
                                       CONSTRAINT INDEX33564238 PRIMARY KEY (ID)
);


-- SLGZT.MC_COM_RES_USAGE_T definition

CREATE TABLE SLGZT.MC_COM_RES_USAGE_T (
                                          ID VARCHAR2(64) NOT NULL,
                                          PLAT_FORM_CODE VARCHAR2(64) NULL,
                                          CITY_CODE VARCHAR2(64) NULL,
                                          CITY VARCHAR2(64) NULL,
                                          REGION_ID VARCHAR2(64) NULL,
                                          REGION_CODE VARCHAR2(64) NULL,
                                          REGION_NAME VARCHAR2(64) NULL,
                                          PHYS_CPU_TOTAL VARCHAR2(64) NULL,
                                          PHYS_MEMORY_TOTAL VARCHAR2(64) NULL,
                                          CPU_TOTAL VARCHAR2(64) NULL,
                                          CPU_AVI VARCHAR2(64) NULL,
                                          CPU_USED VARCHAR2(64) NULL,
                                          MEMORY_TOTAL VARCHAR2(64) NULL,
                                          MEMORY_AVI VARCHAR2(64) NULL,
                                          MEMORY_USED VARCHAR2(64) NULL,
                                          VCPU_TOTAL VARCHAR2(64) NULL,
                                          VCPU_AVI VARCHAR2(64) NULL,
                                          VCPU_USED VARCHAR2(64) NULL,
                                          STORAGE_TOTAL VARCHAR2(64) NULL,
                                          STORAGE_AVI VARCHAR2(64) NULL,
                                          STORAGE_USED VARCHAR2(64) NULL,
                                          BANDWIDTH_TOTAL VARCHAR2(64) NULL,
                                          BANDWIDTH_AVI VARCHAR2(64) NULL,
                                          BANDWIDTH_USED VARCHAR2(64) NULL,
                                          EIP_TOTAL VARCHAR2(64) NULL,
                                          EIP_AVI VARCHAR2(64) NULL,
                                          EIP_USED VARCHAR2(64) NULL,
                                          DCN_TOTAL VARCHAR2(64) NULL,
                                          DCN_AVI VARCHAR2(64) NULL,
                                          DCN_USED VARCHAR2(64) NULL,
                                          SLB_TOTAL VARCHAR2(64) NULL,
                                          HOST_NUM VARCHAR2(64) NULL,
                                          VM_NUM VARCHAR2(64) NULL,
                                          CREATED_TIME TIMESTAMP NULL,
                                          CONSTRAINT INDEX33565741 PRIMARY KEY (ID)
);


-- SLGZT.MC_DNAT_T definition

CREATE TABLE SLGZT.MC_DNAT_T (
                                 ID VARCHAR2(64) NOT NULL,
                                 NAT_ID VARCHAR2(64) NULL,
                                 EIP_ID VARCHAR2(64) NULL,
                                 PUBLIC_IP VARCHAR2(64) NULL,
                                 EIP_PORT_NUM VARCHAR2(64) NULL,
                                 PORT_ID VARCHAR2(64) NULL,
                                 PRIVATE_IP VARCHAR2(64) NULL,
                                 PORT_NUM VARCHAR2(64) NULL,
                                 PROTOCAL VARCHAR2(64) NULL,
                                 CREATED_TIME TIMESTAMP NULL,
                                 UPDATED_TIME TIMESTAMP NULL,
                                 DELETED NUMBER DEFAULT 1 NULL,
                                 CONSTRAINT INDEX33564239 PRIMARY KEY (ID)
);


-- SLGZT.MC_DVSWITCH_HOST_T definition

CREATE TABLE SLGZT.MC_DVSWITCH_HOST_T (
                                          ID VARCHAR2(36) NULL,
                                          DVSWITCH_ID VARCHAR2(36) NULL,
                                          HOST_ID VARCHAR2(36) NULL,
                                          CREATED_TIME TIMESTAMP NULL,
                                          REGION_ID NUMBER NULL
);


-- SLGZT.MC_DVSWITCH_T definition

CREATE TABLE SLGZT.MC_DVSWITCH_T (
                                     ID VARCHAR2(36) NOT NULL,
                                     NAME VARCHAR2(255) NULL,
                                     REGION_ID NUMBER NULL,
                                     RESOURCE_ID VARCHAR2(36) NULL,
                                     CREATED_TIME TIMESTAMP NULL,
                                     UPDATED_TIME TIMESTAMP NULL,
                                     STATUS VARCHAR2(36) NULL,
                                     CONSTRAINT INDEX33564240 PRIMARY KEY (ID)
);


-- SLGZT.MC_FLAVOR_METADATA_T definition

CREATE TABLE SLGZT.MC_FLAVOR_METADATA_T (
                                            ID VARCHAR2(255) NOT NULL,
                                            NAME VARCHAR2(255) NULL,
                                            EXTRA_SPECS VARCHAR2(255) NULL,
                                            STATUS VARCHAR2(255) NULL,
                                            CREATED_TIME TIMESTAMP NULL,
                                            AZ_CODE VARCHAR2(32) NULL,
                                            HOST_GROUP VARCHAR2(32) NULL,
                                            CONSTRAINT D_SYS_C009443 PRIMARY KEY (ID)
);


-- SLGZT.MC_FLAVOR_MODEL_T definition

CREATE TABLE SLGZT.MC_FLAVOR_MODEL_T (
                                         ID VARCHAR2(40) NOT NULL,
                                         FLAVOR_MODEL_CODE VARCHAR2(40) NULL,
                                         SERVICE_NAME VARCHAR2(50) NULL,
                                         FLAVOR_RESOURCE_TYPE VARCHAR2(30) NULL,
                                         CREATED_AT TIMESTAMP NULL,
                                         UPDATED_AT TIMESTAMP NULL,
                                         DELETED NUMBER DEFAULT 1 NULL,
                                         FLAVOR_TYPE VARCHAR2(40) NULL,
                                         VCPUS NUMBER NULL,
                                         RAM NUMBER NULL,
                                         VGPUS NUMBER(12,0) NULL,
                                         PLATFORM_CODE VARCHAR2(50) NULL,
                                         FLAVOR_TYPE_NAME VARCHAR2(50) NULL,
                                         DOMAIN_CODE VARCHAR2(50) NULL,
                                         SPEC_CODE VARCHAR2(50) NULL,
                                         SPEC_INFO VARCHAR2(128) NULL,
                                         CATEGORY_CODE VARCHAR2(50) NULL,
                                         CATEGORY_NAME VARCHAR2(50) NULL,
                                         STACK_CODE VARCHAR2(50) NULL,
                                         CONSTRAINT INDEX33564241 PRIMARY KEY (ID)
);


-- SLGZT.MC_FLAVOR_T definition

CREATE TABLE SLGZT.MC_FLAVOR_T (
                                   ID VARCHAR2(32) NOT NULL,
                                   NAME VARCHAR2(128) NULL,
                                   DESCRIPTION VARCHAR2(512) NULL,
                                   REGION_ID NUMBER NULL,
                                   TENANT_ID NUMBER NULL,
                                   RAM NUMBER NULL,
                                   "DISK" NUMBER NULL,
                                   VCPUS NUMBER NULL,
                                   SHARES NUMBER NULL,
                                   "TYPE" VARCHAR2(32) NULL,
                                   RESOURCE_ID VARCHAR2(36) NULL,
                                   CREATED_AT TIMESTAMP NULL,
                                   UPDATED_AT TIMESTAMP NULL,
                                   DELETED NUMBER DEFAULT 1 NULL,
                                   FLAVOR_MODEL_CODE VARCHAR2(50) NULL,
                                   VGPUS NUMBER NULL,
                                   PGPUS VARCHAR2(100) NULL,
                                   FPGAS NUMBER NULL,
                                   FLAVOR_FAMILY VARCHAR2(100) NULL,
                                   FLAVOR_METADATA_ID VARCHAR2(100) NULL,
                                   GPU_TYPE VARCHAR2(50) NULL,
                                   FLAVOR_NAME VARCHAR2(128) NULL,
                                   AZ_ID NUMBER NULL,
                                   IS_INIT NUMBER DEFAULT 1 NULL,
                                   STATUS CHAR(1) DEFAULT 1 NULL,
                                   CONSTRAINT INDEX33564242 PRIMARY KEY (ID)
);


-- SLGZT.MC_GPU_T definition

CREATE TABLE SLGZT.MC_GPU_T (
                                ID VARCHAR(32) NOT NULL,
                                "TYPE" VARCHAR(32) NULL,
                                MODEL VARCHAR(36) NULL,
                                MEMORY VARCHAR(32) NULL,
                                CREATED_TIME DATETIME NULL,
                                UPDATED_TIME DATETIME NULL,
                                VM_ID VARCHAR(64) NULL,
                                RESOURCE_ID VARCHAR(64) NULL,
                                REGION_ID NUMBER NULL,
                                DELETED NUMBER(10,0) NULL,
                                CONSTRAINT INDEX33565556 PRIMARY KEY (ID)
);


-- SLGZT.MC_H3_RES_USAGE_LATEST_T definition

CREATE TABLE SLGZT.MC_H3_RES_USAGE_LATEST_T (
                                                ID VARCHAR2(64) NOT NULL,
                                                PLAT_FORM_CODE VARCHAR2(64) NULL,
                                                CITY_CODE VARCHAR2(64) NULL,
                                                CITY VARCHAR2(64) NULL,
                                                REGION_ID VARCHAR2(64) NULL,
                                                REGION_CODE VARCHAR2(64) NULL,
                                                REGION_NAME VARCHAR2(64) NULL,
                                                HOST_NUM VARCHAR2(64) NULL,
                                                CPU_TOTAL VARCHAR2(64) NULL,
                                                CPU_AVI VARCHAR2(64) NULL,
                                                CPU_USED VARCHAR2(64) NULL,
                                                MEMORY_TOTAL VARCHAR2(64) NULL,
                                                MEMORY_AVI VARCHAR2(64) NULL,
                                                MEMORY_USED VARCHAR2(64) NULL,
                                                VCPU_TOTAL VARCHAR2(64) NULL,
                                                VCPU_AVI VARCHAR2(64) NULL,
                                                VCPU_USED VARCHAR2(64) NULL,
                                                STORAGE_TOTAL VARCHAR2(64) NULL,
                                                STORAGE_AVI VARCHAR2(64) NULL,
                                                STORAGE_USED VARCHAR2(64) NULL,
                                                BANDWIDTH_TOTAL VARCHAR2(64) NULL,
                                                BANDWIDTH_AVI VARCHAR2(64) NULL,
                                                BANDWIDTH_USED VARCHAR2(64) NULL,
                                                EIP_TOTAL VARCHAR2(64) NULL,
                                                EIP_AVI VARCHAR2(64) NULL,
                                                EIP_USED VARCHAR2(64) NULL,
                                                SLB_TOTAL VARCHAR2(64) NULL,
                                                CREATED_TIME TIMESTAMP NULL,
                                                VM_NUM VARCHAR2(64) NULL,
                                                CONSTRAINT INDEX33564243 PRIMARY KEY (ID)
);


-- SLGZT.MC_H3_RES_USAGE_T definition

CREATE TABLE SLGZT.MC_H3_RES_USAGE_T (
                                         ID VARCHAR2(64) NOT NULL,
                                         PLAT_FORM_CODE VARCHAR2(64) NULL,
                                         CITY_CODE VARCHAR2(64) NULL,
                                         CITY VARCHAR2(64) NULL,
                                         REGION_ID VARCHAR2(64) NULL,
                                         REGION_CODE VARCHAR2(64) NULL,
                                         REGION_NAME VARCHAR2(64) NULL,
                                         CPU_TOTAL VARCHAR2(64) NULL,
                                         CPU_AVI VARCHAR2(64) NULL,
                                         CPU_USED VARCHAR2(64) NULL,
                                         MEMORY_TOTAL VARCHAR2(64) NULL,
                                         MEMORY_AVI VARCHAR2(64) NULL,
                                         MEMORY_USED VARCHAR2(64) NULL,
                                         VCPU_TOTAL VARCHAR2(64) NULL,
                                         VCPU_AVI VARCHAR2(64) NULL,
                                         VCPU_USED VARCHAR2(64) NULL,
                                         STORAGE_TOTAL VARCHAR2(64) NULL,
                                         STORAGE_AVI VARCHAR2(64) NULL,
                                         STORAGE_USED VARCHAR2(64) NULL,
                                         BANDWIDTH_TOTAL VARCHAR2(64) NULL,
                                         BANDWIDTH_AVI VARCHAR2(64) NULL,
                                         BANDWIDTH_USED VARCHAR2(64) NULL,
                                         EIP_TOTAL VARCHAR2(64) NULL,
                                         EIP_AVI VARCHAR2(64) NULL,
                                         EIP_USED VARCHAR2(64) NULL,
                                         SLB_TOTAL VARCHAR2(64) NULL,
                                         HOST_NUM VARCHAR2(64) NULL,
                                         CREATED_TIME TIMESTAMP NULL,
                                         VM_NUM VARCHAR2(64) NULL,
                                         CONSTRAINT INDEX33564244 PRIMARY KEY (ID)
);


-- SLGZT.MC_HOST_STORAGE_POOL_T definition

CREATE TABLE SLGZT.MC_HOST_STORAGE_POOL_T (
                                              ID VARCHAR2(36) NOT NULL,
                                              HOST_ID VARCHAR2(36) NULL,
                                              STORAGE_POOL_ID VARCHAR2(36) NULL,
                                              CREATED_TIME TIMESTAMP NULL,
                                              REGION_ID NUMBER NULL,
                                              CONSTRAINT INDEX33564245 PRIMARY KEY (ID)
);


-- SLGZT.MC_HOST_T definition

CREATE TABLE SLGZT.MC_HOST_T (
                                 ID VARCHAR2(36) NOT NULL,
                                 NAME VARCHAR2(255) NULL,
                                 IP VARCHAR2(36) NULL,
                                 REGION_ID NUMBER NULL,
                                 AZ_ID NUMBER NULL,
                                 VENDOR VARCHAR2(64) NULL,
                                 MODEL VARCHAR2(64) NULL,
                                 CPU_MODEL VARCHAR2(64) NULL,
                                 CPU_CORES NUMBER NULL,
                                 CPU_NUM NUMBER NULL,
                                 AVAIL_CPU_HZ NUMBER NULL,
                                 USED_CPU_HZ NUMBER NULL,
                                 MEMORY NUMBER NULL,
                                 AVAIL_MEMORY NUMBER NULL,
                                 STORAGE NUMBER NULL,
                                 AVAIL_STORAGE NUMBER NULL,
                                 RESOURCE_ID VARCHAR2(36) NULL,
                                 CREATED_TIME TIMESTAMP NULL,
                                 UPDATED_TIME TIMESTAMP NULL,
                                 STATUS VARCHAR2(36) NULL,
                                 OPS_STATUS VARCHAR2(64) NULL,
                                 CHAS VARCHAR2(64) NULL,
                                 CONSTRAINT INDEX33564246 PRIMARY KEY (ID)
);


-- SLGZT.MC_HW_RES_USAGE_LATEST_T definition

CREATE TABLE SLGZT.MC_HW_RES_USAGE_LATEST_T (
                                                ID VARCHAR2(64) NOT NULL,
                                                PLAT_FORM_CODE VARCHAR2(64) NULL,
                                                CITY_CODE VARCHAR2(64) NULL,
                                                CITY VARCHAR2(64) NULL,
                                                REGION_ID VARCHAR2(64) NULL,
                                                REGION_CODE VARCHAR2(64) NULL,
                                                REGION_NAME VARCHAR2(64) NULL,
                                                HOST_NUM VARCHAR2(64) NULL,
                                                CPU_TOTAL VARCHAR2(64) NULL,
                                                CPU_AVI VARCHAR2(64) NULL,
                                                CPU_USED VARCHAR2(64) NULL,
                                                MEMORY_TOTAL VARCHAR2(64) NULL,
                                                MEMORY_AVI VARCHAR2(64) NULL,
                                                MEMORY_USED VARCHAR2(64) NULL,
                                                VCPU_TOTAL VARCHAR2(64) NULL,
                                                VCPU_AVI VARCHAR2(64) NULL,
                                                VCPU_USED VARCHAR2(64) NULL,
                                                STORAGE_TOTAL VARCHAR2(64) NULL,
                                                STORAGE_AVI VARCHAR2(64) NULL,
                                                STORAGE_USED VARCHAR2(64) NULL,
                                                BANDWIDTH_USED VARCHAR2(64) NULL,
                                                EIP_TOTAL VARCHAR2(64) NULL,
                                                EIP_AVI VARCHAR2(64) NULL,
                                                EIP_USED VARCHAR2(64) NULL,
                                                CREATED_TIME TIMESTAMP NULL,
                                                VM_NUM VARCHAR2(64) NULL,
                                                CONSTRAINT INDEX33564868 PRIMARY KEY (ID)
);


-- SLGZT."MC_HW_RES_USAGE_LATEST_T_bak" definition

CREATE TABLE SLGZT."MC_HW_RES_USAGE_LATEST_T_bak" (
                                                      ID VARCHAR2(64) NOT NULL,
                                                      PLAT_FORM_CODE VARCHAR2(64) NULL,
                                                      CITY_CODE VARCHAR2(64) NULL,
                                                      CITY VARCHAR2(64) NULL,
                                                      REGION_ID VARCHAR2(64) NULL,
                                                      REGION_CODE VARCHAR2(64) NULL,
                                                      REGION_NAME VARCHAR2(64) NULL,
                                                      VCPU_TOTAL VARCHAR2(64) NULL,
                                                      VCPU_REMAIN VARCHAR2(64) NULL,
                                                      VCPU_USED VARCHAR2(64) NULL,
                                                      VMEM_TOTAL VARCHAR2(64) NULL,
                                                      VMEM_REMAIN VARCHAR2(64) NULL,
                                                      VMEM_USED VARCHAR2(64) NULL,
                                                      VDISK_TOTAL VARCHAR2(64) NULL,
                                                      VDISK_REMAIN VARCHAR2(64) NULL,
                                                      VDISK_USED VARCHAR2(64) NULL,
                                                      HOST_NUM VARCHAR2(64) NULL,
                                                      VM_NUM VARCHAR2(64) NULL,
                                                      CREATED_TIME TIMESTAMP NULL,
                                                      CONSTRAINT INDEX33564247 PRIMARY KEY (ID)
);


-- SLGZT.MC_HW_RES_USAGE_T definition

CREATE TABLE SLGZT.MC_HW_RES_USAGE_T (
                                         ID VARCHAR2(64) NOT NULL,
                                         PLAT_FORM_CODE VARCHAR2(64) NULL,
                                         CITY_CODE VARCHAR2(64) NULL,
                                         CITY VARCHAR2(64) NULL,
                                         REGION_ID VARCHAR2(64) NULL,
                                         REGION_CODE VARCHAR2(64) NULL,
                                         REGION_NAME VARCHAR2(64) NULL,
                                         CPU_TOTAL VARCHAR2(64) NULL,
                                         CPU_AVI VARCHAR2(64) NULL,
                                         CPU_USED VARCHAR2(64) NULL,
                                         MEMORY_TOTAL VARCHAR2(64) NULL,
                                         MEMORY_AVI VARCHAR2(64) NULL,
                                         MEMORY_USED VARCHAR2(64) NULL,
                                         VCPU_TOTAL VARCHAR2(64) NULL,
                                         VCPU_AVI VARCHAR2(64) NULL,
                                         VCPU_USED VARCHAR2(64) NULL,
                                         STORAGE_TOTAL VARCHAR2(64) NULL,
                                         STORAGE_AVI VARCHAR2(64) NULL,
                                         STORAGE_USED VARCHAR2(64) NULL,
                                         BANDWIDTH_USED VARCHAR2(64) NULL,
                                         EIP_TOTAL VARCHAR2(64) NULL,
                                         EIP_AVI VARCHAR2(64) NULL,
                                         EIP_USED VARCHAR2(64) NULL,
                                         HOST_NUM VARCHAR2(64) NULL,
                                         CREATED_TIME TIMESTAMP NULL,
                                         VM_NUM VARCHAR2(64) NULL,
                                         CONSTRAINT INDEX33564866 PRIMARY KEY (ID)
);


-- SLGZT."MC_HW_RES_USAGE_T_bak" definition

CREATE TABLE SLGZT."MC_HW_RES_USAGE_T_bak" (
                                               ID VARCHAR2(64) NOT NULL,
                                               PLAT_FORM_CODE VARCHAR2(64) NULL,
                                               CITY_CODE VARCHAR2(64) NULL,
                                               CITY VARCHAR2(64) NULL,
                                               REGION_ID VARCHAR2(64) NULL,
                                               REGION_CODE VARCHAR2(64) NULL,
                                               REGION_NAME VARCHAR2(64) NULL,
                                               VCPU_TOTAL VARCHAR2(64) NULL,
                                               VCPU_REMAIN VARCHAR2(64) NULL,
                                               VCPU_USED VARCHAR2(64) NULL,
                                               VMEM_TOTAL VARCHAR2(64) NULL,
                                               VMEM_REMAIN VARCHAR2(64) NULL,
                                               VMEM_USED VARCHAR2(64) NULL,
                                               VDISK_TOTAL VARCHAR2(64) NULL,
                                               VDISK_REMAIN VARCHAR2(64) NULL,
                                               VDISK_USED VARCHAR2(64) NULL,
                                               HOST_NUM VARCHAR2(64) NULL,
                                               VM_NUM VARCHAR2(64) NULL,
                                               CREATED_TIME TIMESTAMP NULL,
                                               CONSTRAINT INDEX33564248 PRIMARY KEY (ID)
);


-- SLGZT.MC_IMAGES_T definition

CREATE TABLE SLGZT.MC_IMAGES_T (
                                   ID VARCHAR2(32) NOT NULL,
                                   NAME VARCHAR2(256) NULL,
                                   DESCRIPTION VARCHAR2(512) NULL,
                                   REGION_ID NUMBER NULL,
                                   SHARES NUMBER NULL,
                                   STATUS VARCHAR2(32) NULL,
                                   TENANT_ID NUMBER NULL,
                                   PROJECT_ID VARCHAR2(36) NULL,
                                   OS_TYPE_ID VARCHAR2(36) NULL,
                                   OS_TYPE VARCHAR2(200) NULL,
                                   DISK_FORMAT VARCHAR2(32) NULL,
                                   VIRT_TYPE VARCHAR2(32) NULL,
                                   IMAGE_SIZE NUMBER NULL,
                                   MIN_CPU NUMBER NULL,
                                   MIN_RAM NUMBER NULL,
                                   MIN_DISK NUMBER NULL,
                                   PASSWORD VARCHAR2(36) NULL,
                                   RESOURCE_ID VARCHAR2(64) NULL,
                                   CREATED_TIME TIMESTAMP NULL,
                                   UPDATED_TIME TIMESTAMP NULL,
                                   DELETED NUMBER DEFAULT 1 NULL,
                                   VISIBILITY VARCHAR2(32) NULL,
                                   OS_TYPE_SOURCE VARCHAR2(20) NULL,
                                   IMAGE_PATH VARCHAR2(20) NULL,
                                   TAG_ID VARCHAR2(36) NULL,
                                   TAG_NAME VARCHAR2(36) NULL,
                                   IMAGE_ID VARCHAR2(36) NULL,
                                   HASH VARCHAR2(255) NULL,
                                   AZ_ID NUMBER NULL,
                                   VDC_ID VARCHAR2(50) NULL,
                                   IMAGE_TYPE VARCHAR2(10) NULL,
                                   DOMAIN_CODE VARCHAR2(64) NULL,
                                   DEPOSITORY_ID VARCHAR2(64) NULL,
                                   VERSION VARCHAR2(50) NULL,
                                   CONSTRAINT INDEX33564249 PRIMARY KEY (ID)
);


-- SLGZT.MC_IP_ADDRESS_T definition

CREATE TABLE SLGZT.MC_IP_ADDRESS_T (
                                       ID VARCHAR2(32) NOT NULL,
                                       IP VARCHAR2(128) NULL,
                                       NAME VARCHAR2(2048) NULL,
                                       DESCRIPTION VARCHAR2(512) NULL,
                                       REGION_ID NUMBER NULL,
                                       TENANT_ID NUMBER NULL,
                                       PROJECT_ID VARCHAR2(36) NULL,
                                       DEVICE_ID VARCHAR2(36) NULL,
                                       NETWORK_ID VARCHAR2(36) NULL,
                                       SUBNET_ID VARCHAR2(36) NULL,
                                       "TYPE" VARCHAR2(32) NULL,
                                       DEVICE_TYPE VARCHAR2(32) NULL,
                                       GATEWAY VARCHAR2(32) NULL,
                                       PORT_ID VARCHAR2(36) NULL,
                                       MAC VARCHAR2(32) NULL,
                                       RESOURCE_ID VARCHAR2(36) NULL,
                                       CREATED_TIME TIMESTAMP NULL,
                                       UPDATED_TIME TIMESTAMP NULL,
                                       DELETED NUMBER DEFAULT 1 NULL,
                                       GID VARCHAR2(50) NULL,
                                       BANDWIDTH NUMBER NULL,
                                       SYNC_UPDATED_TIME TIMESTAMP NULL,
                                       VDC_CODE VARCHAR2(60) NULL,
                                       NIC_TYPE VARCHAR2(32) NULL,
                                       MAX_INGRESS VARCHAR2(64) NULL,
                                       MAX_ENGRESS VARCHAR2(64) NULL,
                                       INSTANCE_UUID VARCHAR2(200) NULL,
                                       PORT_NAME VARCHAR2(128) NULL,
                                       ZONE_ID NUMBER(16,0) NULL,
                                       CONSTRAINT INDEX33564250 PRIMARY KEY (ID)
);


-- SLGZT.MC_LC_RES_USAGE_LATEST_T definition

CREATE TABLE SLGZT.MC_LC_RES_USAGE_LATEST_T (
                                                ID VARCHAR2(64) NOT NULL,
                                                PLAT_FORM_CODE VARCHAR2(64) NULL,
                                                CITY_CODE VARCHAR2(64) NULL,
                                                CITY VARCHAR2(64) NULL,
                                                REGION_ID VARCHAR2(64) NULL,
                                                REGION_CODE VARCHAR2(64) NULL,
                                                REGION_NAME VARCHAR2(64) NULL,
                                                PHYS_CPU_TOTAL VARCHAR2(64) NULL,
                                                PHYS_CPU_USAGE VARCHAR2(64) NULL,
                                                PHYS_MEMORY_TOTAL VARCHAR2(64) NULL,
                                                PHYS_MEMORY_USAGE VARCHAR2(64) NULL,
                                                VCPU_TOTAL VARCHAR2(64) NULL,
                                                VCPU_AVI VARCHAR2(64) NULL,
                                                VCPU_USED VARCHAR2(64) NULL,
                                                MEMORY_TOTAL VARCHAR2(64) NULL,
                                                MEMORY_AVI VARCHAR2(64) NULL,
                                                MEMORY_USED VARCHAR2(64) NULL,
                                                STORAGE_TOTAL VARCHAR2(64) NULL,
                                                STORAGE_AVI VARCHAR2(64) NULL,
                                                STORAGE_USED VARCHAR2(64) NULL,
                                                BANDWIDTH_TOTAL VARCHAR2(64) NULL,
                                                BANDWIDTH_AVI VARCHAR2(64) NULL,
                                                BANDWIDTH_USED VARCHAR2(64) NULL,
                                                EIP_TOTAL VARCHAR2(64) NULL,
                                                EIP_AVI VARCHAR2(64) NULL,
                                                EIP_USED VARCHAR2(64) NULL,
                                                DCN_TOTAL VARCHAR2(64) NULL,
                                                DCN_AVI VARCHAR2(64) NULL,
                                                DCN_USED VARCHAR2(64) NULL,
                                                SLB_TOTAL VARCHAR2(64) NULL,
                                                HOST_NUM VARCHAR2(64) NULL,
                                                VM_NUM VARCHAR2(64) NULL,
                                                CREATED_TIME TIMESTAMP NULL,
                                                CONSTRAINT INDEX33565739 PRIMARY KEY (ID)
);


-- SLGZT.MC_LC_RES_USAGE_T definition

CREATE TABLE SLGZT.MC_LC_RES_USAGE_T (
                                         ID VARCHAR2(64) NOT NULL,
                                         PLAT_FORM_CODE VARCHAR2(64) NULL,
                                         CITY_CODE VARCHAR2(64) NULL,
                                         CITY VARCHAR2(64) NULL,
                                         REGION_ID VARCHAR2(64) NULL,
                                         REGION_CODE VARCHAR2(64) NULL,
                                         REGION_NAME VARCHAR2(64) NULL,
                                         PHYS_CPU_TOTAL VARCHAR2(64) NULL,
                                         PHYS_CPU_USAGE VARCHAR2(64) NULL,
                                         PHYS_MEMORY_TOTAL VARCHAR2(64) NULL,
                                         PHYS_MEMORY_USAGE VARCHAR2(64) NULL,
                                         VCPU_TOTAL VARCHAR2(64) NULL,
                                         VCPU_AVI VARCHAR2(64) NULL,
                                         VCPU_USED VARCHAR2(64) NULL,
                                         MEMORY_TOTAL VARCHAR2(64) NULL,
                                         MEMORY_AVI VARCHAR2(64) NULL,
                                         MEMORY_USED VARCHAR2(64) NULL,
                                         STORAGE_TOTAL VARCHAR2(64) NULL,
                                         STORAGE_AVI VARCHAR2(64) NULL,
                                         STORAGE_USED VARCHAR2(64) NULL,
                                         BANDWIDTH_TOTAL VARCHAR2(64) NULL,
                                         BANDWIDTH_AVI VARCHAR2(64) NULL,
                                         BANDWIDTH_USED VARCHAR2(64) NULL,
                                         EIP_TOTAL VARCHAR2(64) NULL,
                                         EIP_AVI VARCHAR2(64) NULL,
                                         EIP_USED VARCHAR2(64) NULL,
                                         DCN_TOTAL VARCHAR2(64) NULL,
                                         DCN_AVI VARCHAR2(64) NULL,
                                         DCN_USED VARCHAR2(64) NULL,
                                         SLB_TOTAL VARCHAR2(64) NULL,
                                         HOST_NUM VARCHAR2(64) NULL,
                                         VM_NUM VARCHAR2(64) NULL,
                                         CREATED_TIME TIMESTAMP NULL,
                                         CONSTRAINT INDEX33565737 PRIMARY KEY (ID)
);


-- SLGZT.MC_NAT_EIP_REL_T definition

CREATE TABLE SLGZT.MC_NAT_EIP_REL_T (
                                        ID VARCHAR2(64) NOT NULL,
                                        NAT_ID VARCHAR2(64) NULL,
                                        EIP_ID VARCHAR2(64) NULL,
                                        EIP VARCHAR2(64) NULL,
                                        CREATED_TIME TIMESTAMP NULL,
                                        UPDATED_TIME TIMESTAMP NULL,
                                        DELETED NUMBER DEFAULT 1 NULL,
                                        CONSTRAINT INDEX33564251 PRIMARY KEY (ID)
);


-- SLGZT.MC_NAT_RULE_T definition

CREATE TABLE SLGZT.MC_NAT_RULE_T (
                                     ID VARCHAR2(64) NOT NULL,
                                     REGION_ID NUMBER NULL,
                                     TENANT_ID NUMBER NULL,
                                     PROJECT_ID VARCHAR2(64) NULL,
                                     GID VARCHAR2(64) NULL,
                                     NAT_ID VARCHAR2(64) NULL,
                                     SOURCE_ADDRESS VARCHAR2(64) NULL,
                                     TRANSFORM_ADDRESS VARCHAR2(64) NULL,
                                     RULE_TYPE VARCHAR2(10) NULL,
                                     RESOURCE_ID VARCHAR2(64) NULL,
                                     STATUS VARCHAR2(50) NULL,
                                     CREATED_TIME TIMESTAMP NULL,
                                     UPDATED_TIME TIMESTAMP NULL,
                                     DELETED NUMBER NULL,
                                     CONSTRAINT INDEX33564252 PRIMARY KEY (ID)
);


-- SLGZT.MC_NAT_T definition

CREATE TABLE SLGZT.MC_NAT_T (
                                ID VARCHAR2(64) NOT NULL,
                                NAME VARCHAR2(64) NULL,
                                DESCRIPTION VARCHAR2(64) NULL,
                                REGION_ID NUMBER NULL,
                                AZ_ID NUMBER NULL,
                                TENANT_ID NUMBER NULL,
                                CMP_TENANT_ID NUMBER NULL,
                                PROJECT_ID VARCHAR2(64) NULL,
                                CLOUD_PLATFORM_CODE VARCHAR2(64) NULL,
                                FLAVOR_ID VARCHAR2(64) NULL,
                                VPC_ID VARCHAR2(64) NULL,
                                RESOURCE_ID VARCHAR2(64) NULL,
                                CREATED_BY NUMBER NULL,
                                CREATED_TIME TIMESTAMP NULL,
                                UPDATED_BY NUMBER NULL,
                                UPDATED_TIME TIMESTAMP NULL,
                                SYNC_UPDATED_TIME TIMESTAMP NULL,
                                DELETED NUMBER DEFAULT 1 NULL,
                                GID VARCHAR2(64) NULL,
                                SUBNET_ID VARCHAR2(64) NULL,
                                STATUS VARCHAR2(64) NULL,
                                EIP_ID VARCHAR2(64) NULL,
                                FLAVOR_CODE VARCHAR2(64) NULL,
                                VDC_CODE VARCHAR2(60) NULL,
                                INSTANCE_UUID VARCHAR2(36) NULL,
                                CONSTRAINT INDEX33564253 PRIMARY KEY (ID)
);


-- SLGZT.MC_NETWORKS_T definition

CREATE TABLE SLGZT.MC_NETWORKS_T (
                                     ID VARCHAR2(36) NULL,
                                     DESCRIPTION NVARCHAR2(2048) NULL,
                                     NAME VARCHAR2(64) NULL,
                                     REGION_ID NUMBER NULL,
                                     TENANT_ID NUMBER NULL,
                                     PROJECT_ID VARCHAR2(36) NULL,
                                     SHARES NUMBER NULL,
                                     "TYPE" NVARCHAR2(128) NULL,
                                     "EXTERNAL" NUMBER NULL,
                                     RESOURCE_ID VARCHAR2(36) NULL,
                                     CREATED_TIME TIMESTAMP NULL,
                                     UPDATED_TIME TIMESTAMP NULL,
                                     DELETED NUMBER DEFAULT 1 NULL,
                                     SEGMENTATION_ID NUMBER(5,0) NULL,
                                     PHYSICAL_NETWORK VARCHAR2(60) NULL,
                                     ADMIN_STATE_UP NUMBER(1,0) NULL,
                                     GLOBAL_ID VARCHAR2(40) NULL,
                                     STATUS VARCHAR2(60) NULL,
                                     VPC_ID VARCHAR2(32) NULL,
                                     VDC_CODE VARCHAR2(60) NULL,
                                     CIDR VARCHAR2(512) NULL,
                                     MANAGER_STATUS VARCHAR2(32) NULL,
                                     INSTANCE_UUID VARCHAR2(512) NULL,
                                     CMDB_VLAN_INSTANCE_ID VARCHAR2(200) NULL,
                                     AZ_ID NUMBER NULL
);


-- SLGZT.MC_NFVO_RES_USAGE_LATEST_T definition

CREATE TABLE SLGZT.MC_NFVO_RES_USAGE_LATEST_T (
                                                  ID VARCHAR2(64) NULL,
                                                  PLAT_FORM_CODE VARCHAR2(64) NULL,
                                                  CITY_CODE VARCHAR2(64) NULL,
                                                  CITY VARCHAR2(64) NULL,
                                                  REGION_ID VARCHAR2(64) NULL,
                                                  REGION_CODE VARCHAR2(64) NULL,
                                                  REGION_NAME VARCHAR2(64) NULL,
                                                  HOST_NUM VARCHAR2(64) NULL,
                                                  CPU_TOTAL VARCHAR2(64) NULL,
                                                  CPU_AVI VARCHAR2(64) NULL,
                                                  CPU_USED VARCHAR2(64) NULL,
                                                  MEMORY_TOTAL VARCHAR2(64) NULL,
                                                  MEMORY_AVI VARCHAR2(64) NULL,
                                                  MEMORY_USED VARCHAR2(64) NULL,
                                                  VCPU_TOTAL VARCHAR2(64) NULL,
                                                  VCPU_AVI VARCHAR2(64) NULL,
                                                  VCPU_USED VARCHAR2(64) NULL,
                                                  STORAGE_TOTAL VARCHAR2(64) NULL,
                                                  STORAGE_AVI VARCHAR2(64) NULL,
                                                  STORAGE_USED VARCHAR2(64) NULL,
                                                  BANDWIDTH_TOTAL VARCHAR2(64) NULL,
                                                  BANDWIDTH_AVI VARCHAR2(64) NULL,
                                                  BANDWIDTH_USED VARCHAR2(64) NULL,
                                                  EIP_TOTAL VARCHAR2(64) NULL,
                                                  EIP_AVI VARCHAR2(64) NULL,
                                                  EIP_USED VARCHAR2(64) NULL,
                                                  SLB_TOTAL VARCHAR2(64) NULL,
                                                  CREATED_TIME TIMESTAMP NULL,
                                                  CREATED_DATE NUMBER(8,0) NULL,
                                                  VM_NUM VARCHAR2(64) NULL,
                                                  VIM_ID VARCHAR2(64) NULL,
                                                  DCN_IPV4_TOTAL VARCHAR2(64) NULL,
                                                  DCN_IPV4_AVI VARCHAR2(64) NULL,
                                                  DCN_IPV4_USED VARCHAR2(64) NULL
);


-- SLGZT.MC_NFVO_RES_USAGE_T definition

CREATE TABLE SLGZT.MC_NFVO_RES_USAGE_T (
                                           ID VARCHAR2(64) NOT NULL,
                                           PLAT_FORM_CODE VARCHAR2(64) NULL,
                                           CITY_CODE VARCHAR2(64) NULL,
                                           CITY VARCHAR2(64) NULL,
                                           REGION_ID VARCHAR2(64) NULL,
                                           REGION_CODE VARCHAR2(64) NULL,
                                           REGION_NAME VARCHAR2(64) NULL,
                                           HOST_NUM VARCHAR2(64) NULL,
                                           CPU_TOTAL VARCHAR2(64) NULL,
                                           CPU_AVI VARCHAR2(64) NULL,
                                           CPU_USED VARCHAR2(64) NULL,
                                           MEMORY_TOTAL VARCHAR2(64) NULL,
                                           MEMORY_AVI VARCHAR2(64) NULL,
                                           MEMORY_USED VARCHAR2(64) NULL,
                                           VCPU_TOTAL VARCHAR2(64) NULL,
                                           VCPU_AVI VARCHAR2(64) NULL,
                                           VCPU_USED VARCHAR2(64) NULL,
                                           STORAGE_TOTAL VARCHAR2(64) NULL,
                                           STORAGE_AVI VARCHAR2(64) NULL,
                                           STORAGE_USED VARCHAR2(64) NULL,
                                           BANDWIDTH_TOTAL VARCHAR2(64) NULL,
                                           BANDWIDTH_AVI VARCHAR2(64) NULL,
                                           BANDWIDTH_USED VARCHAR2(64) NULL,
                                           EIP_TOTAL VARCHAR2(64) NULL,
                                           EIP_AVI VARCHAR2(64) NULL,
                                           EIP_USED VARCHAR2(64) NULL,
                                           SLB_TOTAL VARCHAR2(64) NULL,
                                           CREATED_TIME TIMESTAMP NULL,
                                           CREATED_DATE NUMBER(8,0) NULL,
                                           VM_NUM VARCHAR2(64) NULL,
                                           VIM_ID VARCHAR2(64) NULL,
                                           DCN_IPV4_TOTAL VARCHAR2(64) NULL,
                                           DCN_IPV4_AVI VARCHAR2(64) NULL,
                                           DCN_IPV4_USED VARCHAR2(64) NULL,
                                           CONSTRAINT INDEX33564254 PRIMARY KEY (ID)
);
CREATE INDEX D_NFVO_RES_CREATED_DATE_IDX ON SLGZT.MC_NFVO_RES_USAGE_T (CREATED_DATE);
CREATE INDEX D_NFVO_RES_CREATED_TIME_IDX ON SLGZT.MC_NFVO_RES_USAGE_T (CREATED_TIME);


-- SLGZT.MC_OBS_BUCKET_T definition

CREATE TABLE SLGZT.MC_OBS_BUCKET_T (
                                       ID VARCHAR2(40) NOT NULL,
                                       NAME VARCHAR2(40) NULL,
                                       BUCKET_SIZE NUMBER NULL,
                                       WRITE_PROTECT NUMBER NULL,
                                       MULTI_VERSION NUMBER NULL,
                                       STORAGE_TYPE VARCHAR2(40) NULL,
                                       STRATEGY VARCHAR2(40) NULL,
                                       STATUS VARCHAR2(40) NULL,
                                       ENDPOINT VARCHAR2(40) NULL,
                                       RESOURCE_ID VARCHAR2(40) NULL,
                                       CREATED_BY VARCHAR2(40) NULL,
                                       CREATED_TIME TIMESTAMP NULL,
                                       UPDATED_BY VARCHAR2(40) NULL,
                                       UPDATED_TIME TIMESTAMP NULL,
                                       GID VARCHAR2(40) NULL,
                                       REGION_ID NUMBER(20,0) NULL,
                                       STORAGE_POLOCY VARCHAR2(50) NULL,
                                       ENCRYPTION_ALGORITHMS VARCHAR2(50) NULL,
                                       DELETED NUMBER(10,0) DEFAULT 1 NULL,
                                       OBS_ID VARCHAR2(50) NULL,
                                       VDC_CODE VARCHAR2(60) NULL,
                                       TENANT_ID NUMBER NULL,
                                       CONSTRAINT INDEX33564255 PRIMARY KEY (ID)
);


-- SLGZT.MC_OBS_EXPANSION_T definition

CREATE TABLE SLGZT.MC_OBS_EXPANSION_T (
                                          ID VARCHAR2(64) NOT NULL,
                                          OBS_ID VARCHAR2(64) NULL,
                                          ACCESS_KEY VARCHAR2(64) NULL,
                                          SECRET_KEY VARCHAR2(64) NULL,
                                          PUBLIC_ADDRESS VARCHAR2(64) NULL,
                                          INTERNAL_ADDRESS VARCHAR2(64) NULL,
                                          REGION_ID NUMBER(20,0) NULL,
                                          DELETED NUMBER(10,0) DEFAULT 1 NULL,
                                          CREATED_TIME TIMESTAMP NULL,
                                          CREATED_BY VARCHAR2(40) NULL,
                                          UPDATED_TIME TIMESTAMP NULL,
                                          UPDATED_BY VARCHAR2(40) NULL,
                                          CONSTRAINT INDEX33564256 PRIMARY KEY (ID)
);


-- SLGZT.MC_OBS_T definition

CREATE TABLE SLGZT.MC_OBS_T (
                                ID VARCHAR2(40) NOT NULL,
                                NAME VARCHAR2(40) NULL,
                                CLOUD_PLATFORM_CODE VARCHAR2(40) NULL,
                                CLOUD_TENANT_ID VARCHAR2(40) NULL,
                                PROJECT_ID VARCHAR2(40) NULL,
                                QUOTA NUMBER NULL,
                                RESOURCE_ID VARCHAR2(40) NULL,
                                STATUS VARCHAR2(40) NULL,
                                CREATED_BY VARCHAR2(40) NULL,
                                CREATED_TIME TIMESTAMP NULL,
                                UPDATED_BY VARCHAR2(40) NULL,
                                UPDATED_TIME TIMESTAMP NULL,
                                GID VARCHAR2(40) NULL,
                                SYNC_UPDATED_TIME TIMESTAMP NULL,
                                REGION_ID NUMBER(20,0) NULL,
                                DELETED NUMBER(10,0) DEFAULT 1 NULL,
                                TENANT_ID NUMBER(20,0) NULL,
                                VDC_CODE VARCHAR2(60) NULL,
                                CMP_TENANT_ID NUMBER(16,0) NULL,
                                INSTANCE_UUID VARCHAR2(36) NULL,
                                CONSTRAINT INDEX33564257 PRIMARY KEY (ID)
);


-- SLGZT.MC_OS_TYPE_T definition

CREATE TABLE SLGZT.MC_OS_TYPE_T (
                                    ID VARCHAR2(32) NOT NULL,
                                    REGION_ID NUMBER NULL,
                                    NAME VARCHAR2(200) NULL,
                                    DESCRIPTION VARCHAR2(512) NULL,
                                    VIRT_TYPE VARCHAR2(32) NULL,
                                    OS_TYPE VARCHAR2(32) NULL,
                                    OS_NAME VARCHAR2(32) NULL,
                                    RESOURCE_ID VARCHAR2(36) NULL,
                                    CREATED_TIME TIMESTAMP NULL,
                                    UPDATED_TIME TIMESTAMP NULL,
                                    DELETED NUMBER DEFAULT 1 NULL,
                                    CONSTRAINT INDEX33564258 PRIMARY KEY (ID)
);


-- SLGZT.MC_PLATFORM_ACCOUNT_T definition

CREATE TABLE SLGZT.MC_PLATFORM_ACCOUNT_T (
                                             ID VARCHAR2(36) NOT NULL,
                                             NAME VARCHAR2(36) NULL,
                                             NICK_NAME VARCHAR2(36) NULL,
                                             PASSWORD NVARCHAR2(512) NULL,
                                             DESCRIPTION NVARCHAR2(2048) NULL,
                                             ACCOUNT_TYPE VARCHAR2(36) NULL,
                                             TENANT_ID NUMBER NULL,
                                             PROJECT_ID VARCHAR2(36) NULL,
                                             REGION_ID NUMBER NULL,
                                             RESOURCE_ID VARCHAR2(36) NULL,
                                             CREATED_TIME TIMESTAMP NULL,
                                             UPDATED_TIME TIMESTAMP NULL,
                                             DELETED NUMBER DEFAULT 1 NULL,
                                             ACCESS_KEY VARCHAR2(256) NULL,
                                             SECRET_KEY VARCHAR2(256) NULL,
                                             USER_GROUP_ID VARCHAR2(255) NULL,
                                             AUTH_TYPE VARCHAR2(36) NULL,
                                             TOKEN VARCHAR2(36) NULL,
                                             PLATFORM_ID VARCHAR2(255) NULL,
                                             CONSTRAINT INDEX33564259 PRIMARY KEY (ID)
);


-- SLGZT.MC_PLATFORM_T definition

CREATE TABLE SLGZT.MC_PLATFORM_T (
                                     ID VARCHAR2(32) NOT NULL,
                                     NAME VARCHAR2(64) NULL,
                                     DESCRIPTION VARCHAR2(512) NULL,
                                     CODE VARCHAR2(32) NULL,
                                     "TYPE" VARCHAR2(36) NULL,
                                     VERSION VARCHAR2(32) NULL,
                                     CREATED_TIME TIMESTAMP NULL,
                                     UPDATED_TIME TIMESTAMP NULL,
                                     DELETED NUMBER DEFAULT 1 NULL,
                                     PLATFORM_TYPE VARCHAR2(200) NULL,
                                     PROVIDER_PRODUCT VARCHAR2(128) NULL,
                                     PROVIDER_CODE VARCHAR2(128) NULL,
                                     CREATED_BY NUMBER(12,0) NULL,
                                     UPDATED_BY NUMBER(12,0) NULL,
                                     PLATFORM_ADDR VARCHAR2(255) NULL,
                                     PROVIDER_NAME VARCHAR2(128) NULL,
                                     CREATED_BY_NAME VARCHAR2(128) NULL,
                                     DOMAIN_CODE VARCHAR2(64) NULL,
                                     CONSTRAINT INDEX33564260 PRIMARY KEY (ID)
);


-- SLGZT.MC_PLATFORM_T_BAK definition

CREATE TABLE SLGZT.MC_PLATFORM_T_BAK (
                                         ID VARCHAR2(32) NOT NULL,
                                         NAME VARCHAR2(64) NULL,
                                         DESCRIPTION VARCHAR2(512) NULL,
                                         CODE VARCHAR2(32) NULL,
                                         "TYPE" VARCHAR2(36) NULL,
                                         VERSION VARCHAR2(32) NULL,
                                         CREATED_TIME TIMESTAMP NULL,
                                         UPDATED_TIME TIMESTAMP NULL,
                                         DELETED NUMBER DEFAULT 1 NULL,
                                         PLATFORM_TYPE VARCHAR2(200) NULL,
                                         PROVIDER_PRODUCT VARCHAR2(128) NULL,
                                         PROVIDER_CODE VARCHAR2(128) NULL,
                                         CREATED_BY NUMBER(12,0) NULL,
                                         UPDATED_BY NUMBER(12,0) NULL,
                                         PLATFORM_ADDR VARCHAR2(255) NULL,
                                         PROVIDER_NAME VARCHAR2(128) NULL,
                                         CREATED_BY_NAME VARCHAR2(128) NULL,
                                         DOMAIN_CODE VARCHAR(50) NULL,
                                         CONSTRAINT INDEX33565504 PRIMARY KEY (ID)
);


-- SLGZT.MC_PLATFORM_TENANT_T definition

CREATE TABLE SLGZT.MC_PLATFORM_TENANT_T (
                                            ID NUMBER(12,0) NOT NULL,
                                            NAME VARCHAR2(100) NULL,
                                            EMAIL VARCHAR2(128) NULL,
                                            ADDRESS VARCHAR2(128) NULL,
                                            DESCRIPTION VARCHAR2(512) NULL,
                                            STATUS NUMBER(1,0) DEFAULT 1 NULL,
                                            CREATED_BY NUMBER(12,0) NULL,
                                            CREATED_TIME TIMESTAMP NULL,
                                            UPDATED_BY NUMBER(12,0) NULL,
                                            UPDATED_TIME TIMESTAMP NULL,
                                            CUSTOM_NO VARCHAR2(128) NULL,
                                            REGION_CODE VARCHAR2(128) NULL,
                                            RESERVE_TWO VARCHAR2(32) NULL,
                                            RESERVE_THREE VARCHAR2(32) NULL,
                                            CUSTOM_ID VARCHAR2(32) NULL,
                                            USER_ID VARCHAR2(32) NULL,
                                            DOMAIN_CODE VARCHAR2(32) NULL,
                                            SET_MEAL_CODE VARCHAR2(32) NULL,
                                            CLOUD_PLATFORM_CODE VARCHAR2(32) NULL,
                                            CLOUD_PLATFORM_CITY_CODE VARCHAR2(32) NULL,
                                            BILL_ID VARCHAR2(32) NULL,
                                            CLOUD_DOMIAN VARCHAR2(255) NULL,
                                            CITY_CODE VARCHAR2(32) NULL,
                                            CLOUD_TENANT_ID VARCHAR2(255) NULL,
                                            CLOUD_TENANT_NAME VARCHAR2(255) NULL,
                                            TENANT_TYPE NUMBER(1,0) NULL,
                                            TENANT_TAG VARCHAR2(16) NULL,
                                            CMP_TENANT_ID NUMBER NULL,
                                            CONSTRAINT INDEX33564261 PRIMARY KEY (ID)
);


-- SLGZT.MC_PORT_IP_T definition

CREATE TABLE SLGZT.MC_PORT_IP_T (
                                    ID NVARCHAR2(128) NOT NULL,
                                    PORT_ID VARCHAR2(36) NULL,
                                    IP NVARCHAR2(128) NULL,
                                    SUBNET_ID VARCHAR2(36) NULL,
                                    IP_VERSION NUMBER NULL,
                                    "TYPE" NVARCHAR2(128) NULL,
                                    CREATED_TIME TIMESTAMP NULL,
                                    UPDATED_TIME TIMESTAMP NULL,
                                    STATUS NUMBER DEFAULT 1 NULL,
                                    CONSTRAINT INDEX33564262 PRIMARY KEY (ID)
);


-- SLGZT.MC_PORT_T definition

CREATE TABLE SLGZT.MC_PORT_T (
                                 ID VARCHAR(32) NOT NULL,
                                 REGION_ID NUMBER NOT NULL,
                                 NAME VARCHAR(128) NOT NULL,
                                 DEVICE_TYPE VARCHAR2(50) NULL,
                                 DEVICE_ID VARCHAR2(50) NULL,
                                 DESCRIPTION VARCHAR2(50) NULL,
                                 NETWORK_ID VARCHAR2(50) NULL,
                                 MAC VARCHAR2(50) NULL,
                                 TENANT_ID NUMBER NULL,
                                 PROJECT_ID VARCHAR2(50) NULL,
                                 RESOURCE_ID VARCHAR2(50) NULL,
                                 STATUS VARCHAR2(20) NULL,
                                 CREATED_TIME TIMESTAMP NULL,
                                 UPDATED_TIME TIMESTAMP NULL,
                                 DELETED NUMBER DEFAULT 1 NOT NULL,
                                 SUBNET_ID VARCHAR2(50) NULL,
                                 VPC_ID VARCHAR2(50) NULL,
                                 CONSTRAINT INDEX33564841 PRIMARY KEY (ID)
);


-- SLGZT.MC_PRODUCT_T definition

CREATE TABLE SLGZT.MC_PRODUCT_T (
                                    ID VARCHAR2(36) NOT NULL,
                                    DOMAIN_CODE VARCHAR2(50) NULL,
                                    DOMAIN_NAME VARCHAR2(50) NULL,
                                    RES_TYPE VARCHAR2(32) NULL,
                                    RES_NAME VARCHAR2(32) NULL,
                                    PRODUCT_CODE VARCHAR2(50) NULL,
                                    PRODUCT_NAME VARCHAR2(50) NULL,
                                    STATUS NUMBER NULL,
                                    CONSTRAINT INDEX33565779 PRIMARY KEY (ID)
);


-- SLGZT.MC_PROJECT_QUOTAS_T definition

CREATE TABLE SLGZT.MC_PROJECT_QUOTAS_T (
                                           ID NUMBER NOT NULL,
                                           PROJECT_ID VARCHAR(64) NOT NULL,
                                           QUOTA_TYPE VARCHAR(64) NOT NULL,
                                           TOTAL VARCHAR(64) NOT NULL,
                                           USED VARCHAR(64) NULL,
                                           AVI VARCHAR(64) NULL,
                                           CREATED_TIME TIMESTAMP NOT NULL,
                                           UPDATED_TIME TIMESTAMP NULL,
                                           DELETED NUMBER DEFAULT 1 NOT NULL,
                                           CONSTRAINT INDEX33564803 PRIMARY KEY (ID)
);


-- SLGZT.MC_PROJECT_VNF_T definition

CREATE TABLE SLGZT.MC_PROJECT_VNF_T (
                                        ID VARCHAR2(255) NULL,
                                        NAME VARCHAR2(255) NULL,
                                        DESCRIPTION VARCHAR2(255) NULL,
                                        VNF_NAME VARCHAR2(255) NULL,
                                        QUOTA_TYPE VARCHAR2(255) NULL,
                                        CPU_QUOTA NUMBER NULL,
                                        MEMORY_QUOTA NUMBER NULL,
                                        DISK_QUOTA NUMBER NULL,
                                        VGPU_QUOTA NUMBER NULL,
                                        PGPU_QUOTA VARCHAR2(255) NULL,
                                        FPGA_QUOTA NUMBER NULL,
                                        REGION_ID NUMBER NULL,
                                        TENANT_ID NUMBER NULL,
                                        PROJECT_ID VARCHAR2(255) NULL,
                                        RESOURCE_ID VARCHAR2(255) NULL,
                                        STATUS NUMBER DEFAULT 1 NULL,
                                        CREATED_BY NUMBER NULL,
                                        CREATED_TIME TIMESTAMP NULL,
                                        UPDATED_BY NUMBER NULL,
                                        UPDATED_TIME TIMESTAMP NULL
);


-- SLGZT.MC_REGION_CMDB definition

CREATE TABLE SLGZT.MC_REGION_CMDB (
                                      CODE VARCHAR2(256) NOT NULL,
                                      NAME VARCHAR2(512) NOT NULL,
                                      CMDB_NAME VARCHAR2(512) NOT NULL,
                                      CLOUD VARCHAR2(512) NOT NULL,
                                      BUCKET_POOL VARCHAR2(512) NULL,
                                      HARDWARE_POOL_CODE VARCHAR2(512) NULL,
                                      VIRTUAL_POOL_NAME VARCHAR2(512) NULL,
                                      VIRTUAL_POOL_CODE VARCHAR2(512) NULL,
                                      CONSTRAINT INDEX33564263 PRIMARY KEY (CODE)
);


-- SLGZT.MC_REGION_CMDB_BAK0414 definition

CREATE TABLE SLGZT.MC_REGION_CMDB_BAK0414 (
                                              CODE VARCHAR2(256) NULL,
                                              NAME VARCHAR2(512) NOT NULL,
                                              CMDB_NAME VARCHAR2(512) NOT NULL,
                                              CLOUD VARCHAR2(512) NOT NULL,
                                              BUCKET_POOL VARCHAR2(512) NULL
);


-- SLGZT.MC_REGION_CODE_T definition

CREATE TABLE SLGZT.MC_REGION_CODE_T (
                                        ID NUMBER(10,0) NOT NULL AUTO_INCREMENT,
                                        CLOUD_CODE VARCHAR(50) NULL,
                                        CLOUD_NAME VARCHAR(100) NULL,
                                        PLATFORM VARCHAR(50) NULL,
                                        REGION_CODE VARCHAR(50) NULL,
                                        REGION_NAME VARCHAR(100) NULL
);


-- SLGZT.MC_REGION_T definition

CREATE TABLE SLGZT.MC_REGION_T (
                                   ID NUMBER NOT NULL,
                                   NAME VARCHAR2(64) NULL,
                                   CODE VARCHAR2(64) NULL,
                                   DESCRIPTION VARCHAR2(512) NULL,
                                   CLOUD_PLATFORM_ID VARCHAR2(36) NULL,
                                   "TYPE" VARCHAR2(36) NULL,
                                   ENDPOINT VARCHAR2(64) NULL,
                                   CREATED_TIME TIMESTAMP NULL,
                                   UPDATED_TIME TIMESTAMP NULL,
                                   DELETED NUMBER DEFAULT 1 NULL,
                                   CITY_CODE VARCHAR2(64) NULL,
                                   RESOURCE_CODE VARCHAR2(64) NULL,
                                   CITY_NAME VARCHAR2(64) NULL,
                                   REALM_TYPE VARCHAR2(6) NULL,
                                   CREATED_BY VARCHAR2(255) NULL,
                                   PLATFORM_NAME VARCHAR2(255) NULL,
                                   CREATED_BY_NAME VARCHAR2(128) NULL,
                                   DOMAIN_CODE VARCHAR2(50) NULL,
                                   OFFLINE_REGION VARCHAR(50) NULL,
                                   AREA_CODE VARCHAR2(64) NULL,
                                   CONSTRAINT INDEX33564264 PRIMARY KEY (ID)
);


-- SLGZT.MC_RELEASED_IPS_T definition

CREATE TABLE SLGZT.MC_RELEASED_IPS_T (
                                         ID VARCHAR(36) NOT NULL,
                                         RESERVED_ID VARCHAR(36) NOT NULL,
                                         IP VARCHAR(128) NOT NULL,
                                         CONSTRAINT INDEX33564847 PRIMARY KEY (ID)
);


-- SLGZT.MC_RESOURCE_CAPACITY_T definition

CREATE TABLE SLGZT.MC_RESOURCE_CAPACITY_T (
                                              ID VARCHAR2(64) NOT NULL,
                                              PLAT_FORM_CODE VARCHAR2(64) NULL,
                                              CITY_CODE VARCHAR2(64) NULL,
                                              CITY VARCHAR2(64) NULL,
                                              RESOURCE_POOL_CODE VARCHAR2(64) NULL,
                                              RESOURCE_POOL VARCHAR2(64) NULL,
                                              RESOURCE_TYPE VARCHAR2(64) NULL,
                                              TOTAL VARCHAR2(64) NULL,
                                              AVI VARCHAR2(64) NULL,
                                              USED VARCHAR2(64) NULL,
                                              AVI_RATE VARCHAR2(64) NULL,
                                              UPDATED_TIME TIMESTAMP NULL,
                                              DELETED NUMBER DEFAULT 1 NULL,
                                              CREATED_TIME NUMBER(20,0) NULL,
                                              CONSTRAINT INDEX33564265 PRIMARY KEY (ID)
);


-- SLGZT.MC_SECURITYGROUP_RULE_T definition

CREATE TABLE SLGZT.MC_SECURITYGROUP_RULE_T (
                                               ID VARCHAR2(32) NOT NULL,
                                               REGION_ID NUMBER NULL,
                                               TENANT_ID NUMBER NULL,
                                               PROJECT_ID VARCHAR2(36) NULL,
                                               SECURITYGROUP_ID VARCHAR2(36) NULL,
                                               DIRECTION VARCHAR2(64) NULL,
                                               PROTOCOL VARCHAR2(64) NULL,
                                               ETHERTYPE NVARCHAR2(128) NULL,
                                               PORT_RANGE_MIN NUMBER NULL,
                                               PORT_RANGE_MAX NUMBER NULL,
                                               REMOTE_IP_PREFIX VARCHAR2(255) NULL,
                                               REMOTEGROUP_ID VARCHAR2(36) NULL,
                                               RESOURCE_ID VARCHAR2(64) NULL,
                                               CREATED_TIME TIMESTAMP NULL,
                                               UPDATED_TIME TIMESTAMP NULL,
                                               DELETED NUMBER DEFAULT 1 NULL,
                                               PRIORITY VARCHAR2(20) NULL,
                                               ACCESS_STATUS NUMBER NULL,
                                               PORT_RANGE VARCHAR2(50) NULL,
                                               DEST_IP VARCHAR2(1024) NULL,
                                               RULE_NAME VARCHAR2(34) NULL,
                                               IS_DEFAULT NUMBER DEFAULT 0 NULL,
                                               CONSTRAINT INDEX33564266 PRIMARY KEY (ID)
);


-- SLGZT.MC_SECURITYGROUP_T definition

CREATE TABLE SLGZT.MC_SECURITYGROUP_T (
                                          ID VARCHAR2(32) NOT NULL,
                                          NAME VARCHAR2(64) NULL,
                                          DESCRIPTION NVARCHAR2(2048) NULL,
                                          REGION_ID NUMBER NULL,
                                          TENANT_ID NUMBER NULL,
                                          PROJECT_ID VARCHAR2(36) NULL,
                                          RESOURCE_ID VARCHAR2(36) NULL,
                                          CREATED_TIME TIMESTAMP NULL,
                                          UPDATED_TIME TIMESTAMP NULL,
                                          DELETED NUMBER DEFAULT 1 NULL,
                                          VPC_ID VARCHAR2(200) NULL,
                                          VDC_CODE VARCHAR2(60) NULL,
                                          INSTANCE_UUID VARCHAR2(60) NULL,
                                          GLOBAL_ID VARCHAR2(64) NULL,
                                          IS_DEFAULT NUMBER NULL,
                                          CONSTRAINT INDEX33564267 PRIMARY KEY (ID)
);


-- SLGZT.MC_SHARED_BANDWIDTH_EIP_REL_T definition

CREATE TABLE SLGZT.MC_SHARED_BANDWIDTH_EIP_REL_T (
                                                     ID VARCHAR2(32) NOT NULL,
                                                     SHARED_BANDWIDTH_ID VARCHAR2(64) NULL,
                                                     EIP_ID VARCHAR2(64) NULL,
                                                     CLOUD_VIM_ID VARCHAR2(128) NULL,
                                                     REGION_ID NUMBER NULL,
                                                     TENANT_ID NUMBER NULL,
                                                     DELETED NUMBER DEFAULT 1 NULL,
                                                     CREATED_TIME TIMESTAMP NULL,
                                                     UPDATED_TIME TIMESTAMP NULL,
                                                     CONSTRAINT INDEX33565697 PRIMARY KEY (ID)
);


-- SLGZT.MC_SHARED_BANDWIDTH_T definition

CREATE TABLE SLGZT.MC_SHARED_BANDWIDTH_T (
                                             ID VARCHAR2(32) NOT NULL,
                                             NAME VARCHAR2(64) NULL,
                                             BANDWIDTH NUMBER NULL,
                                             VPC_ID VARCHAR2(200) NULL,
                                             ZONE_ID NUMBER NULL,
                                             REGION_ID NUMBER NULL,
                                             TENANT_ID NUMBER NULL,
                                             VDC_CODE VARCHAR2(60) NULL,
                                             PROJECT_ID VARCHAR2(36) NULL,
                                             RESOURCE_ID VARCHAR2(36) NULL,
                                             CLOUD_ROUTER_ID VARCHAR2(128) NULL,
                                             GLOBAL_ID VARCHAR2(64) NULL,
                                             INSTANCE_UUID VARCHAR2(60) NULL,
                                             DELETED NUMBER DEFAULT 1 NULL,
                                             STATUS VARCHAR2(64) NULL,
                                             CREATED_TIME TIMESTAMP NULL,
                                             UPDATED_TIME TIMESTAMP NULL,
                                             CONSTRAINT INDEX33565695 PRIMARY KEY (ID)
);


-- SLGZT.MC_SLB_GROUP_SERVER_REL_T definition

CREATE TABLE SLGZT.MC_SLB_GROUP_SERVER_REL_T (
                                                 ID VARCHAR2(32) NULL,
                                                 SERVER_GROUP_ID VARCHAR2(32) NULL,
                                                 SERVER_ID VARCHAR2(32) NULL,
                                                 SERVICE_PORT VARCHAR2(32) NULL,
                                                 SERVER_RESOURCE_ID VARCHAR2(32) NULL,
                                                 DELETED NUMBER DEFAULT 1 NULL,
                                                 CREATED_BY VARCHAR2(32) NULL,
                                                 CREATED_TIME TIMESTAMP NULL,
                                                 UPDATED_BY VARCHAR2(32) NULL,
                                                 UPDATED_TIME TIMESTAMP NULL,
                                                 PRIORITY_GROUP NUMBER NULL,
                                                 PRIVATE_IP VARCHAR2(128) NULL,
                                                 SYS_SERVER_ID VARCHAR2(128) NULL
);


-- SLGZT.MC_SLB_LISTENER_T definition

CREATE TABLE SLGZT.MC_SLB_LISTENER_T (
                                         ID VARCHAR2(32) NULL,
                                         GLOBAL_ID VARCHAR2(32) NULL,
                                         NAME VARCHAR2(32) NULL,
                                         PROTOCOL VARCHAR2(32) NULL,
                                         PORT VARCHAR2(32) NULL,
                                         MAX_CONNECTION VARCHAR2(32) NULL,
                                         LB_MODE VARCHAR2(32) NULL,
                                         SESSION_PERSISTENCE NUMBER NULL,
                                         MONITOR_STATUS NUMBER NULL,
                                         MONITOR_PROTOCOL VARCHAR2(32) NULL,
                                         MONITOR_PORT VARCHAR2(32) NULL,
                                         CHECKED_INTERVAL VARCHAR2(32) NULL,
                                         TIMEOUT NUMBER NULL,
                                         ATTEMPT_CNT VARCHAR2(32) NULL,
                                         MONITOR_URL VARCHAR2(32) NULL,
                                         SERVER_GROUP_ID VARCHAR2(32) NULL,
                                         CA_CERT_ID VARCHAR2(32) NULL,
                                         COOKIE_METHOD NUMBER NULL,
                                         COOKIE_TIMEOUT NUMBER NULL,
                                         REGION_CODE VARCHAR2(32) NULL,
                                         TENANT_ID NUMBER NULL,
                                         PROJECT_ID VARCHAR2(32) NULL,
                                         SLB_GLOBAL_ID VARCHAR2(32) NULL,
                                         RESOURCE_ID VARCHAR2(32) NULL,
                                         STATUS VARCHAR2(32) NULL,
                                         DELETED NUMBER DEFAULT 1 NULL,
                                         CREATED_BY VARCHAR2(32) NULL,
                                         CREATED_TIME TIMESTAMP NULL,
                                         UPDATED_BY VARCHAR2(32) NULL,
                                         UPDATED_TIME TIMESTAMP NULL,
                                         SERVER_CERT_ID VARCHAR2(32) NULL,
                                         SYNC_UPDATED_TIME TIMESTAMP NULL,
                                         SLB_ID VARCHAR2(36) NULL,
                                         DESCRIPTION VARCHAR2(512) NULL,
                                         CONNECTION_LIMIT VARCHAR2(32) NULL
);


-- SLGZT.MC_SLB_SERVER_GROUP_T definition

CREATE TABLE SLGZT.MC_SLB_SERVER_GROUP_T (
                                             ID VARCHAR2(32) NULL,
                                             GLOBAL_ID VARCHAR2(32) NULL,
                                             NAME VARCHAR2(32) NULL,
                                             SLB_GLOBAL_ID VARCHAR2(32) NULL,
                                             REGION_CODE VARCHAR2(32) NULL,
                                             TENANT_ID NUMBER NULL,
                                             PROJECT_ID VARCHAR2(32) NULL,
                                             RESOURCE_ID VARCHAR2(32) NULL,
                                             DELETED NUMBER DEFAULT 1 NULL,
                                             CREATED_BY VARCHAR2(32) NULL,
                                             CREATED_TIME TIMESTAMP NULL,
                                             UPDATED_BY VARCHAR2(32) NULL,
                                             UPDATED_TIME TIMESTAMP NULL,
                                             GROUP_TYPE NUMBER NULL,
                                             GROUP_PRIORITY_GROUP NUMBER NULL,
                                             SYNC_UPDATED_TIME TIMESTAMP NULL,
                                             SLB_ID VARCHAR2(32) NULL,
                                             LISTENER_ID VARCHAR2(128) NULL,
                                             DESCRIPTION VARCHAR2(1024) NULL,
                                             ALGORITHM VARCHAR2(1024) NULL,
                                             PROTOCOL VARCHAR2(128) NULL,
                                             SESSION_TYPE VARCHAR2(128) NULL,
                                             SESSION_TIMEOUT NUMBER NULL
);


-- SLGZT.MC_SLB_T definition

CREATE TABLE SLGZT.MC_SLB_T (
                                ID VARCHAR2(32) NOT NULL,
                                NAME VARCHAR2(32) NULL,
                                FLAVOR_CODE VARCHAR2(32) NULL,
                                ADDRESS_TYPE VARCHAR2(32) NULL,
                                VPC_ID VARCHAR2(32) NULL,
                                SUBNET_ID VARCHAR2(32) NULL,
                                REGION_CODE VARCHAR2(64) NULL,
                                TENANT_ID NUMBER NULL,
                                PROJECT_ID VARCHAR2(32) NULL,
                                STATUS VARCHAR2(32) NULL,
                                GLOBAL_ID VARCHAR2(32) NULL,
                                RESOURCE_ID VARCHAR2(42) NULL,
                                DELETED NUMBER DEFAULT 1 NULL,
                                CREATED_BY VARCHAR2(32) NULL,
                                CREATED_TIME TIMESTAMP NULL,
                                UPDATED_BY VARCHAR2(32) NULL,
                                UPDATED_TIME TIMESTAMP NULL,
                                SYNC_UPDATED_TIME TIMESTAMP NULL,
                                VDC_CODE VARCHAR2(60) NULL,
                                INSTANCE_UUID VARCHAR2(36) NULL,
                                VIP VARCHAR2(128) NULL,
                                ZONE_ID NUMBER NULL,
                                CONSTRAINT INDEX33564129 PRIMARY KEY (ID)
);


-- SLGZT.MC_SNAT_T definition

CREATE TABLE SLGZT.MC_SNAT_T (
                                 ID VARCHAR2(64) NOT NULL,
                                 NAT_ID VARCHAR2(64) NULL,
                                 EIP_ID VARCHAR2(64) NULL,
                                 EIP VARCHAR2(64) NULL,
                                 VPC_ID VARCHAR2(64) NULL,
                                 SUBNET_ID VARCHAR2(64) NULL,
                                 CIDR VARCHAR2(64) NULL,
                                 RESOURCE_ID VARCHAR2(64) NULL,
                                 CREATED_TIME TIMESTAMP NULL,
                                 UPDATED_TIME TIMESTAMP NULL,
                                 DELETED NUMBER DEFAULT 1 NULL,
                                 GID VARCHAR2(50) NULL,
                                 REGION_ID NUMBER NULL,
                                 TENANT_ID NUMBER NULL,
                                 PROJECT_ID VARCHAR2(64) NULL,
                                 SOURCE_ADDRESS VARCHAR2(64) NULL,
                                 STATUS VARCHAR2(64) NULL,
                                 NAME VARCHAR2(255) NULL,
                                 CONSTRAINT INDEX33564130 PRIMARY KEY (ID)
);


-- SLGZT.MC_STORAGE_POOL_T definition

CREATE TABLE SLGZT.MC_STORAGE_POOL_T (
                                         ID VARCHAR2(36) NOT NULL,
                                         NAME VARCHAR2(255) NULL,
                                         REGION_ID NUMBER NULL,
                                         SHARED NUMBER NULL,
                                         TOTAL NUMBER NULL,
                                         USED NUMBER NULL,
                                         AVAIL NUMBER NULL,
                                         RESOURCE_ID VARCHAR2(128) NULL,
                                         CREATED_TIME TIMESTAMP NULL,
                                         UPDATED_TIME TIMESTAMP NULL,
                                         STATUS VARCHAR2(36) NULL,
                                         AZ_NAME VARCHAR2(32) NULL,
                                         STORAGE_CLUSTER VARCHAR2(64) NULL,
                                         POOL_NAME VARCHAR2(32) NULL,
                                         CONSTRAINT INDEX33564131 PRIMARY KEY (ID)
);


-- SLGZT.MC_SUBNET_RESERVED_IPS_T definition

CREATE TABLE SLGZT.MC_SUBNET_RESERVED_IPS_T (
                                                ID VARCHAR(36) NOT NULL,
                                                SUBNET_ID VARCHAR(36) NOT NULL,
                                                START_IP VARCHAR(128) NOT NULL,
                                                END_IP VARCHAR(128) NOT NULL,
                                                RESERVED_IP VARCHAR(128) NOT NULL,
                                                CONSTRAINT INDEX33564845 PRIMARY KEY (ID)
);


-- SLGZT.MC_SUBNETS_T definition

CREATE TABLE SLGZT.MC_SUBNETS_T (
                                    ID VARCHAR2(36) NOT NULL,
                                    NAME VARCHAR2(250) NULL,
                                    DESCRIPTION VARCHAR2(512) NULL,
                                    REGION_ID NUMBER NULL,
                                    TENANT_ID NUMBER NULL,
                                    PROJECT_ID VARCHAR2(36) NULL,
                                    ENABLE_DHCP NUMBER NULL,
                                    NETWORK_ID VARCHAR2(36) NULL,
                                    DNS_NAMES VARCHAR2(512) NULL,
                                    GATEWAY VARCHAR2(36) NULL,
                                    IP_VERSION NUMBER NULL,
                                    CIDR VARCHAR2(50) NULL,
                                    START_IP VARCHAR2(64) NULL,
                                    END_IP VARCHAR2(64) NULL,
                                    RESOURCE_ID VARCHAR2(64) NULL,
                                    CREATED_TIME TIMESTAMP NULL,
                                    UPDATED_TIME TIMESTAMP NULL,
                                    DELETED NUMBER DEFAULT 1 NULL,
                                    VPC_ID VARCHAR2(36) NULL,
                                    GLOBAL_ID VARCHAR2(32) NULL,
                                    ZONE_ID VARCHAR2(32) NULL,
                                    IPV6_ADDRESS_MODE VARCHAR2(100) NULL,
                                    IPV6_RA_MODE VARCHAR2(100) NULL,
                                    STATUS VARCHAR2(60) NULL,
                                    SEGMENT_PLAN_ID VARCHAR2(36) NULL,
                                    VDC_CODE VARCHAR2(60) NULL,
                                    NETMASK VARCHAR2(50) NULL,
                                    MANAGE_STATUS VARCHAR2(64) NULL,
                                    SHARE_TYPE VARCHAR2(64) NULL,
                                    "TYPE" VARCHAR2(64) NULL,
                                    INSTANCE_UUID VARCHAR2(200) NULL,
                                    IPV6_ENABLE NUMBER NULL,
                                    CMDB_IP_INSTANCE_ID VARCHAR2(200) NULL,
                                    NET_TYPE VARCHAR2(5) NULL,
                                    CONSTRAINT INDEX33564132 PRIMARY KEY (ID)
);


-- SLGZT.MC_TASK_T definition

CREATE TABLE SLGZT.MC_TASK_T (
                                 ID NVARCHAR2(128) NOT NULL,
                                 NAME VARCHAR2(64) NULL,
                                 STATUS VARCHAR2(32) NULL,
                                 REGION_ID NUMBER NULL,
                                 RESOURCE_TYPE NVARCHAR2(128) NULL,
                                 OPERATION_TYPE NVARCHAR2(512) NULL,
                                 OPERATION_STATUS NUMBER DEFAULT 0 NULL,
                                 TENANT_ID NUMBER NULL,
                                 PROJECT_ID VARCHAR2(36) NULL,
                                 RESOURCE_ID VARCHAR2(36) NULL,
                                 ATTACH_RESOURCE_ID VARCHAR2(512) NULL,
                                 START_TIME TIMESTAMP NULL,
                                 CREATE_TIME TIMESTAMP NULL,
                                 UPDATED_TIME TIMESTAMP NULL,
                                 MESSAGE NVARCHAR2(2048) NULL,
                                 DELETED NUMBER DEFAULT 1 NULL,
                                 TASK_TYPE NVARCHAR2(128) NULL,
                                 ORDER_ID VARCHAR2(36) NULL,
                                 INSTANCE_ID VARCHAR2(64) NULL,
                                 OUT_INSTANCE_ID VARCHAR2(64) NULL,
                                 JOB_ID VARCHAR2(100) NULL,
                                 EXTRA VARCHAR2(3000) NULL,
                                 CLOUD_PLATFORM_CODE VARCHAR2(60) NULL,
                                 CONSTRAINT INDEX33564133 PRIMARY KEY (ID)
);


-- SLGZT.MC_TENANT_QUOTAS_T definition

CREATE TABLE SLGZT.MC_TENANT_QUOTAS_T (
                                          ID NUMBER NOT NULL,
                                          TENANT_ID NUMBER NOT NULL,
                                          QUOTA_TYPE VARCHAR(64) NOT NULL,
                                          TOTAL VARCHAR(64) NOT NULL,
                                          AVI VARCHAR(64) NULL,
                                          USED VARCHAR(64) NULL,
                                          CREATED_TIME TIMESTAMP NOT NULL,
                                          UPDATED_TIME TIMESTAMP NULL,
                                          DELETED NUMBER DEFAULT 1 NOT NULL,
                                          CONSTRAINT INDEX33564801 PRIMARY KEY (ID)
);


-- SLGZT.MC_VC_RES_USAGE_LATEST_T definition

CREATE TABLE SLGZT.MC_VC_RES_USAGE_LATEST_T (
                                                ID VARCHAR2(64) NULL,
                                                PLAT_FORM_CODE VARCHAR2(64) NULL,
                                                CITY_CODE VARCHAR2(64) NULL,
                                                CITY VARCHAR2(64) NULL,
                                                REGION_ID VARCHAR2(64) NULL,
                                                REGION_CODE VARCHAR2(64) NULL,
                                                REGION_NAME VARCHAR2(64) NULL,
                                                CPU_TOTAL VARCHAR2(64) NULL,
                                                CPU_AVI VARCHAR2(64) NULL,
                                                CPU_USED VARCHAR2(64) NULL,
                                                MEMORY_TOTAL VARCHAR2(64) NULL,
                                                MEMORY_AVI VARCHAR2(64) NULL,
                                                MEMORY_USED VARCHAR2(64) NULL,
                                                VCPU_TOTAL VARCHAR2(64) NULL,
                                                VCPU_AVI VARCHAR2(64) NULL,
                                                VCPU_USED VARCHAR2(64) NULL,
                                                STORAGE_TOTAL VARCHAR2(64) NULL,
                                                STORAGE_AVI VARCHAR2(64) NULL,
                                                STORAGE_USED VARCHAR2(64) NULL,
                                                DCN_IPV4_TOTAL VARCHAR2(64) NULL,
                                                DCN_IPV4_AVI VARCHAR2(64) NULL,
                                                DCN_IPV4_USED VARCHAR2(64) NULL,
                                                DCN_IPV6_TOTAL VARCHAR2(64) NULL,
                                                DCN_IPV6_AVI VARCHAR2(64) NULL,
                                                DCN_IPV6_USED VARCHAR2(64) NULL,
                                                SLB_TOTAL VARCHAR2(64) NULL,
                                                HOST_NUM VARCHAR2(64) NULL,
                                                VM_NUM VARCHAR2(64) NULL,
                                                CREATED_TIME TIMESTAMP NULL
);


-- SLGZT.MC_VC_RES_USAGE_T definition

CREATE TABLE SLGZT.MC_VC_RES_USAGE_T (
                                         ID VARCHAR2(64) NOT NULL,
                                         PLAT_FORM_CODE VARCHAR2(64) NULL,
                                         CITY_CODE VARCHAR2(64) NULL,
                                         CITY VARCHAR2(64) NULL,
                                         REGION_ID VARCHAR2(64) NULL,
                                         REGION_CODE VARCHAR2(64) NULL,
                                         REGION_NAME VARCHAR2(64) NULL,
                                         CPU_TOTAL VARCHAR2(64) NULL,
                                         CPU_AVI VARCHAR2(64) NULL,
                                         CPU_USED VARCHAR2(64) NULL,
                                         MEMORY_TOTAL VARCHAR2(64) NULL,
                                         MEMORY_AVI VARCHAR2(64) NULL,
                                         MEMORY_USED VARCHAR2(64) NULL,
                                         VCPU_TOTAL VARCHAR2(64) NULL,
                                         VCPU_AVI VARCHAR2(64) NULL,
                                         VCPU_USED VARCHAR2(64) NULL,
                                         STORAGE_TOTAL VARCHAR2(64) NULL,
                                         STORAGE_AVI VARCHAR2(64) NULL,
                                         STORAGE_USED VARCHAR2(64) NULL,
                                         DCN_IPV4_TOTAL VARCHAR2(64) NULL,
                                         DCN_IPV4_AVI VARCHAR2(64) NULL,
                                         DCN_IPV4_USED VARCHAR2(64) NULL,
                                         DCN_IPV6_TOTAL VARCHAR2(64) NULL,
                                         DCN_IPV6_AVI VARCHAR2(64) NULL,
                                         DCN_IPV6_USED VARCHAR2(64) NULL,
                                         SLB_TOTAL VARCHAR2(64) NULL,
                                         HOST_NUM VARCHAR2(64) NULL,
                                         VM_NUM VARCHAR2(64) NULL,
                                         CREATED_TIME TIMESTAMP NULL,
                                         CONSTRAINT INDEX33564134 PRIMARY KEY (ID)
);


-- SLGZT.MC_VM_GROUP_T definition

CREATE TABLE SLGZT.MC_VM_GROUP_T (
                                     ID VARCHAR2(255) NULL,
                                     NAME VARCHAR2(255) NULL,
                                     GROUP_POLICY VARCHAR2(255) NULL,
                                     MAX_SERVER_PER_HOST VARCHAR2(255) NULL,
                                     REGION_ID VARCHAR2(255) NULL,
                                     CLOUD_TENANT_ID VARCHAR2(255) NULL,
                                     PLATFORM_TENANT_ID NUMBER NULL,
                                     PROJECT_ID VARCHAR2(255) NULL,
                                     RESOURCE_ID VARCHAR2(255) NULL,
                                     STATUS VARCHAR2(255) NULL,
                                     CREATED_BY VARCHAR2(32) NULL,
                                     CREATED_TIME TIMESTAMP NULL,
                                     UPDATED_BY VARCHAR2(32) NULL,
                                     UPDATED_TIME TIMESTAMP NULL
);


-- SLGZT.MC_VM_SECURITYGROUP_T definition

CREATE TABLE SLGZT.MC_VM_SECURITYGROUP_T (
                                             ID NVARCHAR2(128) NOT NULL,
                                             VM_ID VARCHAR2(36) NULL,
                                             SECURITYGROUP_ID VARCHAR2(36) NULL,
                                             CREATED_TIME TIMESTAMP NULL,
                                             UPDATED_TIME TIMESTAMP NULL,
                                             DELETED NUMBER DEFAULT 1 NULL,
                                             CONSTRAINT INDEX33564135 PRIMARY KEY (ID)
);


-- SLGZT.MC_VM_T definition

CREATE TABLE SLGZT.MC_VM_T (
                               ID VARCHAR2(32) NOT NULL,
                               NAME VARCHAR2(512) NULL,
                               DESCRIPTION VARCHAR2(512) NULL,
                               REGION_ID NUMBER NULL,
                               ZONE_ID NUMBER NULL,
                               USER_ID VARCHAR2(36) NULL,
                               FLAVOR_ID VARCHAR2(36) NULL,
                               IMAGE_ID VARCHAR2(36) NULL,
                               TENANT_ID NUMBER NULL,
                               PROJECT_ID VARCHAR2(36) NULL,
                               HOST_ID VARCHAR2(36) NULL,
                               "TYPE" VARCHAR2(32) NULL,
                               STATUS VARCHAR2(36) NULL,
                               V_SYSTEM_DISK NUMBER NULL,
                               V_DATA_DISK NUMBER NULL,
                               USER_NAME VARCHAR2(32) NULL,
                               PASSWORD VARCHAR2(32) NULL,
                               RESOURCE_ID VARCHAR2(64) NULL,
                               LAUNCHED_TIME TIMESTAMP NULL,
                               TERMINATED_TIME TIMESTAMP NULL,
                               CREATED_TIME TIMESTAMP NULL,
                               UPDATED_TIME TIMESTAMP NULL,
                               SYNC_UPDATED_TIME TIMESTAMP NULL,
                               DELETED NUMBER DEFAULT 1 NULL,
                               GID VARCHAR2(50) NULL,
                               VM_GROUP_ID VARCHAR2(40) NULL,
                               INSTANCE_UUID VARCHAR2(36) NULL,
                               USE VARCHAR2(100) NULL,
                               VDC_CODE VARCHAR2(60) NULL,
                               USER_NANE VARCHAR2(64) NULL,
                               PWR_STATUS VARCHAR2(256) NULL,
                               CONSTRAINT INDEX33564136 PRIMARY KEY (ID)
);


-- SLGZT.MC_VMWARE_RES_USAGE_LATEST_T definition

CREATE TABLE SLGZT.MC_VMWARE_RES_USAGE_LATEST_T (
                                                    ID VARCHAR2(64) NULL,
                                                    PLAT_FORM_CODE VARCHAR2(64) NULL,
                                                    CITY_CODE VARCHAR2(64) NULL,
                                                    CITY VARCHAR2(64) NULL,
                                                    REGION_ID VARCHAR2(64) NULL,
                                                    REGION_CODE VARCHAR2(64) NULL,
                                                    REGION_NAME VARCHAR2(64) NULL,
                                                    CPU_TOTAL VARCHAR2(64) NULL,
                                                    CPU_AVI VARCHAR2(64) NULL,
                                                    CPU_USED VARCHAR2(64) NULL,
                                                    MEMORY_TOTAL VARCHAR2(64) NULL,
                                                    MEMORY_AVI VARCHAR2(64) NULL,
                                                    MEMORY_USED VARCHAR2(64) NULL,
                                                    VCPU_TOTAL VARCHAR2(64) NULL,
                                                    VCPU_AVI VARCHAR2(64) NULL,
                                                    VCPU_USED VARCHAR2(64) NULL,
                                                    STORAGE_TOTAL VARCHAR2(64) NULL,
                                                    STORAGE_AVI VARCHAR2(64) NULL,
                                                    STORAGE_USED VARCHAR2(64) NULL,
                                                    BANDWIDTH_TOTAL VARCHAR2(64) NULL,
                                                    BANDWIDTH_AVI VARCHAR2(64) NULL,
                                                    BANDWIDTH_USED VARCHAR2(64) NULL,
                                                    EIP_TOTAL VARCHAR2(64) NULL,
                                                    EIP_AVI VARCHAR2(64) NULL,
                                                    EIP_USED VARCHAR2(64) NULL,
                                                    SLB_TOTAL VARCHAR2(64) NULL,
                                                    HOST_NUM VARCHAR2(64) NULL,
                                                    CREATED_TIME TIMESTAMP NULL,
                                                    GPU_TOTAL VARCHAR2(64) NULL,
                                                    GPU_USED VARCHAR2(64) NULL,
                                                    GPU_AVI VARCHAR2(64) NULL,
                                                    DCN_TOTAL VARCHAR2(64) NULL,
                                                    DCN_USED VARCHAR2(64) NULL,
                                                    DCN_AVI VARCHAR2(64) NULL,
                                                    VM_NUM VARCHAR2(64) NULL,
                                                    GPU_T4_TOTAL VARCHAR2(64) NULL,
                                                    GPU_T4_USED VARCHAR2(64) NULL,
                                                    GPU_T4_AVI VARCHAR2(64) NULL,
                                                    GPU_A10_TOTAL VARCHAR2(64) NULL,
                                                    GPU_A10_USED VARCHAR2(64) NULL,
                                                    GPU_A10_AVI VARCHAR2(64) NULL,
                                                    GPU_A40_TOTAL VARCHAR2(64) NULL,
                                                    GPU_A40_USED VARCHAR2(64) NULL,
                                                    GPU_A40_AVI VARCHAR2(64) NULL
);


-- SLGZT.MC_VMWARE_RES_USAGE_T definition

CREATE TABLE SLGZT.MC_VMWARE_RES_USAGE_T (
                                             ID VARCHAR2(64) NULL,
                                             PLAT_FORM_CODE VARCHAR2(64) NULL,
                                             CITY_CODE VARCHAR2(64) NULL,
                                             CITY VARCHAR2(64) NULL,
                                             REGION_ID VARCHAR2(64) NULL,
                                             REGION_CODE VARCHAR2(64) NULL,
                                             REGION_NAME VARCHAR2(64) NULL,
                                             CPU_TOTAL VARCHAR2(64) NULL,
                                             CPU_AVI VARCHAR2(64) NULL,
                                             CPU_USED VARCHAR2(64) NULL,
                                             MEMORY_TOTAL VARCHAR2(64) NULL,
                                             MEMORY_AVI VARCHAR2(64) NULL,
                                             MEMORY_USED VARCHAR2(64) NULL,
                                             VCPU_TOTAL VARCHAR2(64) NULL,
                                             VCPU_AVI VARCHAR2(64) NULL,
                                             VCPU_USED VARCHAR2(64) NULL,
                                             STORAGE_TOTAL VARCHAR2(64) NULL,
                                             STORAGE_AVI VARCHAR2(64) NULL,
                                             STORAGE_USED VARCHAR2(64) NULL,
                                             BANDWIDTH_TOTAL VARCHAR2(64) NULL,
                                             BANDWIDTH_AVI VARCHAR2(64) NULL,
                                             BANDWIDTH_USED VARCHAR2(64) NULL,
                                             EIP_TOTAL VARCHAR2(64) NULL,
                                             EIP_AVI VARCHAR2(64) NULL,
                                             EIP_USED VARCHAR2(64) NULL,
                                             SLB_TOTAL VARCHAR2(64) NULL,
                                             HOST_NUM VARCHAR2(64) NULL,
                                             CREATED_TIME TIMESTAMP NULL,
                                             GPU_TOTAL VARCHAR2(64) NULL,
                                             GPU_USED VARCHAR2(64) NULL,
                                             GPU_AVI VARCHAR2(64) NULL,
                                             DCN_TOTAL VARCHAR2(64) NULL,
                                             DCN_USED VARCHAR2(64) NULL,
                                             DCN_AVI VARCHAR2(64) NULL,
                                             VM_NUM VARCHAR2(64) NULL,
                                             GPU_T4_TOTAL VARCHAR2(64) NULL,
                                             GPU_T4_USED VARCHAR2(64) NULL,
                                             GPU_T4_AVI VARCHAR2(64) NULL,
                                             GPU_A10_TOTAL VARCHAR2(64) NULL,
                                             GPU_A10_USED VARCHAR2(64) NULL,
                                             GPU_A10_AVI VARCHAR2(64) NULL,
                                             GPU_A40_TOTAL VARCHAR2(64) NULL,
                                             GPU_A40_USED VARCHAR2(64) NULL,
                                             GPU_A40_AVI VARCHAR2(64) NULL
);


-- SLGZT.MC_VOLUME_ATTACH_VM_T definition

CREATE TABLE SLGZT.MC_VOLUME_ATTACH_VM_T (
                                             ID VARCHAR2(32) NOT NULL,
                                             VOLUME_ID VARCHAR2(36) NULL,
                                             VM_ID VARCHAR2(36) NULL,
                                             CREATED_TIME TIMESTAMP NULL,
                                             UPDATED_TIME TIMESTAMP NULL,
                                             DELETED NUMBER DEFAULT 1 NULL,
                                             CONSTRAINT INDEX33564137 PRIMARY KEY (ID)
);


-- SLGZT.MC_VOLUME_T definition

CREATE TABLE SLGZT.MC_VOLUME_T (
                                   ID VARCHAR2(32) NOT NULL,
                                   NAME VARCHAR2(256) NULL,
                                   DESCRIPTION VARCHAR2(512) NULL,
                                   VOLUME_SIZE NUMBER NULL,
                                   REGION_ID NUMBER NULL,
                                   ZONE_ID VARCHAR2(36) NULL,
                                   USER_ID VARCHAR2(36) NULL,
                                   TENANT_ID NUMBER NULL,
                                   PROJECT_ID VARCHAR2(36) NULL,
                                   STATUS VARCHAR2(32) NULL,
                                   TYPE_ID VARCHAR2(36) NULL,
                                   TYPE_NAME VARCHAR2(36) NULL,
                                   STORAGE_TYPE VARCHAR2(50) NULL,
                                   MULTIATTACH NUMBER NULL,
                                   IMAGE_ID VARCHAR2(36) NULL,
                                   SNAPSHOT_ID VARCHAR2(36) NULL,
                                   SOURCE_VOLID VARCHAR2(36) NULL,
                                   BOOTABLE NUMBER NULL,
                                   DEVICE VARCHAR2(32) NULL,
                                   RESOURCE_ID VARCHAR2(36) NULL,
                                   CREATED_AT TIMESTAMP NULL,
                                   UPDATED_AT TIMESTAMP NULL,
                                   DELETED NUMBER DEFAULT 1 NULL,
                                   GID VARCHAR2(50) NULL,
                                   SYNC_UPDATED_TIME TIMESTAMP NULL,
                                   VDC_CODE VARCHAR2(60) NULL,
                                   STORAGE_POOL_ID VARCHAR2(50) NULL,
                                   INSTANCE_UUID VARCHAR2(200) NULL,
                                   FILE_NAME VARCHAR2(512) NULL,
                                   CONSTRAINT INDEX33564138 PRIMARY KEY (ID)
);


-- SLGZT.MC_VOLUME_TYPE_T definition

CREATE TABLE SLGZT.MC_VOLUME_TYPE_T (
                                        ID NVARCHAR2(128) NOT NULL,
                                        NAME NVARCHAR2(512) NULL,
                                        DESCRIPTION NVARCHAR2(2048) NULL,
                                        VOLUME_MODE NVARCHAR2(128) NULL,
                                        SHARES NUMBER NULL,
                                        REGION_ID NUMBER NULL,
                                        RESOURCE_ID VARCHAR2(36) NULL,
                                        CREATED_TIME TIMESTAMP NULL,
                                        UPDATED_TIME TIMESTAMP NULL,
                                        DELETED NUMBER DEFAULT 1 NULL,
                                        CONSTRAINT INDEX33564139 PRIMARY KEY (ID)
);


-- SLGZT.MC_VPC_NETWORK_T definition

CREATE TABLE SLGZT.MC_VPC_NETWORK_T (
                                        ID NVARCHAR2(128) NOT NULL,
                                        VPC_ID VARCHAR2(36) NULL,
                                        NETWORK_ID VARCHAR2(36) NULL,
                                        GATEWAY NVARCHAR2(128) NULL,
                                        CREATED_TIME TIMESTAMP NULL,
                                        UPDATED_TIME TIMESTAMP NULL,
                                        DELETED NUMBER DEFAULT 1 NULL,
                                        CONSTRAINT INDEX33564140 PRIMARY KEY (ID)
);


-- SLGZT.MC_VPC_T definition

CREATE TABLE SLGZT.MC_VPC_T (
                                ID VARCHAR2(32) NOT NULL,
                                NAME VARCHAR2(64) NULL,
                                DESCRIPTION VARCHAR2(512) NULL,
                                REGION_ID NUMBER NULL,
                                TENANT_ID NUMBER NULL,
                                PROJECT_ID VARCHAR2(36) NULL,
                                IPV4_CIDR VARCHAR2(32) NULL,
                                IPV6_CIDR VARCHAR2(32) NULL,
                                EXTERNAL_NETWORK_ID VARCHAR2(36) NULL,
                                CREATE_TIME TIMESTAMP NULL,
                                UPDATED_TIME TIMESTAMP NULL,
                                RESOURCE_ID VARCHAR2(36) NULL,
                                DELETED NUMBER DEFAULT 1 NULL,
                                RESOURCE_POOL_TYPE VARCHAR2(10) NULL,
                                SHARE_TYPE VARCHAR2(10) NULL,
                                VERSION NUMBER DEFAULT 1 NULL,
                                GID VARCHAR2(50) NULL,
                                DATA_FROM NUMBER NULL,
                                VDC_CODE VARCHAR2(60) NULL,
                                INSTANCE_UUID VARCHAR2(200) NULL,
                                CONSTRAINT INDEX33564141 PRIMARY KEY (ID)
);


-- SLGZT.MC_VPN_GATEWAY_T definition

CREATE TABLE SLGZT.MC_VPN_GATEWAY_T (
                                        ID VARCHAR2(60) NOT NULL,
                                        GLOBAL_ID VARCHAR2(60) NULL,
                                        NAME VARCHAR2(60) NULL,
                                        CLOUD_PLATFORM_CODE VARCHAR2(60) NULL,
                                        REGION_CODE VARCHAR2(60) NULL,
                                        AZ_CODE VARCHAR2(60) NULL,
                                        PROJECT_ID VARCHAR2(60) NULL,
                                        ADDRESS_TYPE VARCHAR2(60) NULL,
                                        MAX_CONNECTION NUMBER NULL,
                                        EIP_ID VARCHAR2(60) NULL,
                                        VPC_ID VARCHAR2(60) NULL,
                                        SUBNET_ID VARCHAR2(60) NULL,
                                        TENANT_ID VARCHAR2(60) NULL,
                                        VDC_ID NUMBER NULL,
                                        RESOURCE_ID VARCHAR2(60) NULL,
                                        STATUS VARCHAR2(60) NULL,
                                        CREATED_BY NUMBER NULL,
                                        CREATED_TIME TIMESTAMP NULL,
                                        UPDATED_BY NUMBER NULL,
                                        UPDATED_TIME TIMESTAMP NULL,
                                        LOCAL_ID VARCHAR2(100) NULL,
                                        VDC_CODE VARCHAR2(60) NULL,
                                        INSTANCE_UUID VARCHAR2(36) NULL,
                                        CONSTRAINT "D_sys_c0019891" PRIMARY KEY (ID)
);


-- SLGZT.MC_VROPS_CLUSTER_STATS_T definition

CREATE TABLE SLGZT.MC_VROPS_CLUSTER_STATS_T (
                                                ID VARCHAR(64) NOT NULL,
                                                NAME VARCHAR(255) NULL,
                                                RESOURCE_ID VARCHAR(255) NULL,
                                                VROPS_REGION_ID NUMBER(19,0) NULL,
                                                CLOUD_VROPS_REGION_ID VARCHAR(255) NULL,
                                                DELETED VARCHAR(10) NULL,
                                                STATUS VARCHAR(20) NULL,
                                                CREATED_TIME TIMESTAMP NULL,
                                                UPDATED_TIME TIMESTAMP NULL,
                                                TIMESTAMPS NUMBER(19,0) NULL,
                                                SUMMARY_TOTAL_NUMBER_HOSTS NUMBER(10,0) NULL,
                                                SUMMARY_TOTAL_NUMBER_VMS NUMBER(10,0) NULL,
                                                SUMMARY_NUMBER_VM_TEMPLATES NUMBER(10,0) NULL,
                                                SUMMARY_TOTAL_NUMBER_DATASTORES NUMBER(10,0) NULL,
                                                SUMMARY_NUMBER_RUNNING_HOSTS NUMBER(10,0) NULL,
                                                SUMMARY_NUMBER_RUNNING_VMS NUMBER(10,0) NULL,
                                                CPU_VCPUS_ALLOCATED_ON_ALL_POWERED_ON_VMS NUMBER(10,0) NULL,
                                                CPU_VCPUS_ALLOCATED_ON_ALL_VMS NUMBER(10,0) NULL,
                                                CPU_HACORECOUNT_PROVISIONED NUMBER(10,0) NULL,
                                                CPU_CORECOUNT_PROVISIONED NUMBER(10,0) NULL,
                                                CPU_CAPACITY_USAGEPCT_AVERAGE DOUBLE NULL,
                                                MEM_HOST_PROVISIONED DOUBLE NULL,
                                                MEM_MEMORY_ALLOCATED_ON_ALL_POWERED_ON_VMS DOUBLE NULL,
                                                MEM_HOST_USAGE DOUBLE NULL,
                                                MEM_HOST_USAGEPCT DOUBLE NULL,
                                                MEM_HATOTALCAPACITY_AVERAGE DOUBLE NULL,
                                                MEM_MEMORY_ALLOCATED_ON_ALL_VMS DOUBLE NULL,
                                                MEM_USAGE_AVERAGE DOUBLE NULL,
                                                MEM_DEMAND_USABLECAPACITY DOUBLE NULL,
                                                MEM_HOST_USABLE DOUBLE NULL,
                                                DISKSPACE_TOTAL_CAPACITY DOUBLE NULL,
                                                DISKSPACE_TOTAL_USAGE DOUBLE NULL,
                                                DISKSPACE_TOTAL_PROVISIONED DOUBLE NULL,
                                                DISKSPACE_USED DOUBLE NULL,
                                                DISKSPACE_DISK_USED DOUBLE NULL,
                                                CONSTRAINT INDEX33565703 PRIMARY KEY (ID)
);


-- SLGZT.MC_VROPS_DATACENTER_STATS_T definition

CREATE TABLE SLGZT.MC_VROPS_DATACENTER_STATS_T (
                                                   ID VARCHAR(64) NOT NULL,
                                                   NAME VARCHAR(255) NULL,
                                                   RESOURCE_NAME VARCHAR(255) NULL,
                                                   RESOURCE_ID VARCHAR(255) NULL,
                                                   VROPS_REGION_ID NUMBER(19,0) NULL,
                                                   DELETED VARCHAR(10) NULL,
                                                   STATUS VARCHAR(20) NULL,
                                                   CREATED_TIME TIMESTAMP NULL,
                                                   UPDATED_TIME TIMESTAMP NULL,
                                                   TIMESTAMPS NUMBER(19,0) NULL,
                                                   SUMMARY_TOTAL_NUMBER_HOSTS NUMBER(10,0) NULL,
                                                   SUMMARY_TOTAL_NUMBER_VMS NUMBER(10,0) NULL,
                                                   SUMMARY_NUMBER_VM_TEMPLATES NUMBER(10,0) NULL,
                                                   SUMMARY_TOTAL_NUMBER_DATASTORES NUMBER(10,0) NULL,
                                                   SUMMARY_NUMBER_RUNNING_HOSTS NUMBER(10,0) NULL,
                                                   SUMMARY_NUMBER_RUNNING_VMS NUMBER(10,0) NULL,
                                                   CPU_VCPUS_ALLOCATED_ON_ALL_POWERED_ON_VMS NUMBER(10,0) NULL,
                                                   CPU_VCPUS_ALLOCATED_ON_ALL_VMS NUMBER(10,0) NULL,
                                                   CPU_HACORECOUNT_PROVISIONED NUMBER(10,0) NULL,
                                                   CPU_CORECOUNT_PROVISIONED NUMBER(10,0) NULL,
                                                   CPU_CAPACITY_USAGEPCT_AVERAGE DOUBLE NULL,
                                                   MEM_HOST_PROVISIONED DOUBLE NULL,
                                                   MEM_MEMORY_ALLOCATED_ON_ALL_POWERED_ON_VMS DOUBLE NULL,
                                                   MEM_HOST_USAGE DOUBLE NULL,
                                                   MEM_HOST_USAGEPCT DOUBLE NULL,
                                                   MEM_HATOTALCAPACITY_AVERAGE DOUBLE NULL,
                                                   MEM_MEMORY_ALLOCATED_ON_ALL_VMS DOUBLE NULL,
                                                   MEM_USAGE_AVERAGE DOUBLE NULL,
                                                   MEM_DEMAND_USABLECAPACITY DOUBLE NULL,
                                                   MEM_HOST_USABLE DOUBLE NULL,
                                                   DISKSPACE_TOTAL_CAPACITY DOUBLE NULL,
                                                   DISKSPACE_TOTAL_USAGE DOUBLE NULL,
                                                   DISKSPACE_TOTAL_PROVISIONED DOUBLE NULL,
                                                   DISKSPACE_USED DOUBLE NULL,
                                                   DISKSPACE_DISK_USED DOUBLE NULL,
                                                   CONSTRAINT INDEX33565701 PRIMARY KEY (ID)
);


-- SLGZT.MC_VROPS_DATASTORE_STATS_T definition

CREATE TABLE SLGZT.MC_VROPS_DATASTORE_STATS_T (
                                                  ID VARCHAR(64) NOT NULL,
                                                  NAME VARCHAR(255) NULL,
                                                  RESOURCE_ID VARCHAR(255) NULL,
                                                  VROPS_REGION_ID NUMBER(19,0) NULL,
                                                  CLOUD_VROPS_REGION_ID VARCHAR(255) NULL,
                                                  DELETED VARCHAR(10) NULL,
                                                  STATUS VARCHAR(20) NULL,
                                                  CREATED_TIME TIMESTAMP NULL,
                                                  UPDATED_TIME TIMESTAMP NULL,
                                                  TIMESTAMPS NUMBER(19,0) NULL,
                                                  DISKSPACE_FREESPACE DOUBLE NULL,
                                                  DISKSPACE_CAPACITY DOUBLE NULL,
                                                  SUMMARY_TOTAL_NUMBER_HOSTS NUMBER(10,0) NULL,
                                                  SUMMARY_TOTAL_NUMBER_VMS NUMBER(10,0) NULL,
                                                  SUMMARY_NUMBER_VM_TEMPLATES NUMBER(10,0) NULL,
                                                  SUMMARY_TOTAL_NUMBER_DATASTORES NUMBER(10,0) NULL,
                                                  SUMMARY_NUMBER_RUNNING_HOSTS NUMBER(10,0) NULL,
                                                  SUMMARY_NUMBER_RUNNING_VMS NUMBER(10,0) NULL,
                                                  CPU_VCPUS_ALLOCATED_ON_ALL_POWERED_ON_VMS NUMBER(10,0) NULL,
                                                  CPU_VCPUS_ALLOCATED_ON_ALL_VMS NUMBER(10,0) NULL,
                                                  DISKSPACE_TOTAL_CAPACITY DOUBLE NULL,
                                                  DISKSPACE_TOTAL_USAGE DOUBLE NULL,
                                                  DISKSPACE_TOTAL_PROVISIONED DOUBLE NULL,
                                                  DISKSPACE_USED DOUBLE NULL,
                                                  DISKSPACE_DISK_USED DOUBLE NULL,
                                                  CONSTRAINT INDEX33565709 PRIMARY KEY (ID)
);


-- SLGZT.MC_VROPS_HOST_STATS_T definition

CREATE TABLE SLGZT.MC_VROPS_HOST_STATS_T (
                                             ID VARCHAR(64) NOT NULL,
                                             NAME VARCHAR(255) NULL,
                                             RESOURCE_ID VARCHAR(255) NULL,
                                             VROPS_REGION_ID NUMBER(19,0) NULL,
                                             CLOUD_VROPS_REGION_ID VARCHAR(255) NULL,
                                             CLUSTGER_ID VARCHAR(255) NULL,
                                             CLOUD_CLUSTGER_ID VARCHAR(255) NULL,
                                             DELETED VARCHAR(10) NULL,
                                             STATUS VARCHAR(20) NULL,
                                             CREATED_TIME TIMESTAMP NULL,
                                             UPDATED_TIME TIMESTAMP NULL,
                                             TIMESTAMPS NUMBER(19,0) NULL,
                                             SUMMARY_TOTAL_NUMBER_HOSTS NUMBER(10,0) NULL,
                                             SUMMARY_TOTAL_NUMBER_VMS NUMBER(10,0) NULL,
                                             SUMMARY_NUMBER_VM_TEMPLATES NUMBER(10,0) NULL,
                                             SUMMARY_TOTAL_NUMBER_DATASTORES NUMBER(10,0) NULL,
                                             SUMMARY_NUMBER_RUNNING_HOSTS NUMBER(10,0) NULL,
                                             SUMMARY_NUMBER_RUNNING_VMS NUMBER(10,0) NULL,
                                             CPU_VCPUS_ALLOCATED_ON_ALL_POWERED_ON_VMS NUMBER(10,0) NULL,
                                             CPU_VCPUS_ALLOCATED_ON_ALL_VMS NUMBER(10,0) NULL,
                                             CPU_HACORECOUNT_PROVISIONED NUMBER(10,0) NULL,
                                             CPU_CORECOUNT_PROVISIONED NUMBER(10,0) NULL,
                                             CPU_CAPACITY_USAGEPCT_AVERAGE DOUBLE NULL,
                                             MEM_HOST_PROVISIONED DOUBLE NULL,
                                             MEM_MEMORY_ALLOCATED_ON_ALL_POWERED_ON_VMS DOUBLE NULL,
                                             MEM_HOST_USAGE DOUBLE NULL,
                                             MEM_HOST_USAGEPCT DOUBLE NULL,
                                             MEM_HATOTALCAPACITY_AVERAGE DOUBLE NULL,
                                             MEM_MEMORY_ALLOCATED_ON_ALL_VMS DOUBLE NULL,
                                             MEM_USAGE_AVERAGE DOUBLE NULL,
                                             MEM_DEMAND_USABLECAPACITY DOUBLE NULL,
                                             MEM_HOST_USABLE DOUBLE NULL,
                                             DISKSPACE_TOTAL_CAPACITY DOUBLE NULL,
                                             DISKSPACE_TOTAL_USAGE DOUBLE NULL,
                                             DISKSPACE_TOTAL_PROVISIONED DOUBLE NULL,
                                             DISKSPACE_USED DOUBLE NULL,
                                             DISKSPACE_DISK_USED DOUBLE NULL,
                                             CONSTRAINT INDEX33565705 PRIMARY KEY (ID)
);


-- SLGZT.MC_VROPS_REGION_T definition

CREATE TABLE SLGZT.MC_VROPS_REGION_T (
                                         ID NUMBER NOT NULL,
                                         NAME VARCHAR2(64) NULL,
                                         CODE VARCHAR2(64) NULL,
                                         DESCRIPTION VARCHAR2(512) NULL,
                                         CLOUD_PLATFORM_ID VARCHAR2(36) NULL,
                                         "TYPE" VARCHAR2(36) NULL,
                                         RESOURCE_ID VARCHAR2(64) NULL,
                                         ENDPOINT VARCHAR2(64) NULL,
                                         USERNAME VARCHAR2(64) NULL,
                                         PASSWORD VARCHAR2(64) NULL,
                                         CREATED_TIME TIMESTAMP NULL,
                                         UPDATED_TIME TIMESTAMP NULL,
                                         DELETED NUMBER DEFAULT 1 NULL,
                                         CITY_NAME VARCHAR2(64) NULL,
                                         CITY_CODE VARCHAR2(64) NULL,
                                         AREA_CODE VARCHAR2(64) NULL,
                                         RESOURCE_CODE VARCHAR2(64) NULL,
                                         REALM_TYPE VARCHAR2(6) NULL,
                                         CREATED_BY VARCHAR2(255) NULL,
                                         PLATFORM_NAME VARCHAR2(255) NULL,
                                         CREATED_BY_NAME VARCHAR2(128) NULL,
                                         DOMAIN_CODE VARCHAR2(50) NULL,
                                         OVERVIEW_PLATFORM_ID VARCHAR2(64) NULL,
                                         SPECIFIED_CLUSTER VARCHAR2(64) NULL,
                                         CONSTRAINT INDEX33565715 PRIMARY KEY (ID)
);


-- SLGZT.MC_VROPS_RES_USAGE_LATEST_T definition

CREATE TABLE SLGZT.MC_VROPS_RES_USAGE_LATEST_T (
                                                   ID VARCHAR2(64) NOT NULL,
                                                   PLAT_FORM_CODE VARCHAR2(64) NULL,
                                                   CITY_CODE VARCHAR2(64) NULL,
                                                   CITY VARCHAR2(64) NULL,
                                                   REGION_ID VARCHAR2(64) NULL,
                                                   REGION_CODE VARCHAR2(64) NULL,
                                                   REGION_NAME VARCHAR2(64) NULL,
                                                   CPU_TOTAL VARCHAR2(64) NULL,
                                                   CPU_AVI VARCHAR2(64) NULL,
                                                   CPU_USED VARCHAR2(64) NULL,
                                                   MEMORY_TOTAL VARCHAR2(64) NULL,
                                                   MEMORY_AVI VARCHAR2(64) NULL,
                                                   MEMORY_USED VARCHAR2(64) NULL,
                                                   VCPU_TOTAL VARCHAR2(64) NULL,
                                                   VCPU_AVI VARCHAR2(64) NULL,
                                                   VCPU_USED VARCHAR2(64) NULL,
                                                   STORAGE_TOTAL VARCHAR2(64) NULL,
                                                   STORAGE_AVI VARCHAR2(64) NULL,
                                                   STORAGE_USED VARCHAR2(64) NULL,
                                                   DCN_IPV4_TOTAL VARCHAR2(64) NULL,
                                                   DCN_IPV4_AVI VARCHAR2(64) NULL,
                                                   DCN_IPV4_USED VARCHAR2(64) NULL,
                                                   DCN_IPV6_TOTAL VARCHAR2(64) NULL,
                                                   DCN_IPV6_AVI VARCHAR2(64) NULL,
                                                   DCN_IPV6_USED VARCHAR2(64) NULL,
                                                   SLB_TOTAL VARCHAR2(64) NULL,
                                                   HOST_NUM VARCHAR2(64) NULL,
                                                   VM_NUM VARCHAR2(64) NULL,
                                                   CREATED_TIME TIMESTAMP NULL,
                                                   CONSTRAINT INDEX33565713 PRIMARY KEY (ID)
);


-- SLGZT.MC_VROPS_RES_USAGE_T definition

CREATE TABLE SLGZT.MC_VROPS_RES_USAGE_T (
                                            ID VARCHAR2(64) NOT NULL,
                                            PLAT_FORM_CODE VARCHAR2(64) NULL,
                                            CITY_CODE VARCHAR2(64) NULL,
                                            CITY VARCHAR2(64) NULL,
                                            REGION_ID VARCHAR2(64) NULL,
                                            REGION_CODE VARCHAR2(64) NULL,
                                            REGION_NAME VARCHAR2(64) NULL,
                                            CPU_TOTAL VARCHAR2(64) NULL,
                                            CPU_AVI VARCHAR2(64) NULL,
                                            CPU_USED VARCHAR2(64) NULL,
                                            MEMORY_TOTAL VARCHAR2(64) NULL,
                                            MEMORY_AVI VARCHAR2(64) NULL,
                                            MEMORY_USED VARCHAR2(64) NULL,
                                            VCPU_TOTAL VARCHAR2(64) NULL,
                                            VCPU_AVI VARCHAR2(64) NULL,
                                            VCPU_USED VARCHAR2(64) NULL,
                                            STORAGE_TOTAL VARCHAR2(64) NULL,
                                            STORAGE_AVI VARCHAR2(64) NULL,
                                            STORAGE_USED VARCHAR2(64) NULL,
                                            DCN_IPV4_TOTAL VARCHAR2(64) NULL,
                                            DCN_IPV4_AVI VARCHAR2(64) NULL,
                                            DCN_IPV4_USED VARCHAR2(64) NULL,
                                            DCN_IPV6_TOTAL VARCHAR2(64) NULL,
                                            DCN_IPV6_AVI VARCHAR2(64) NULL,
                                            DCN_IPV6_USED VARCHAR2(64) NULL,
                                            SLB_TOTAL VARCHAR2(64) NULL,
                                            HOST_NUM VARCHAR2(64) NULL,
                                            VM_NUM VARCHAR2(64) NULL,
                                            CREATED_TIME TIMESTAMP NULL,
                                            CONSTRAINT INDEX33565711 PRIMARY KEY (ID)
);


-- SLGZT.MC_VROPS_VIRTUALMACHINE_STATS_T definition

CREATE TABLE SLGZT.MC_VROPS_VIRTUALMACHINE_STATS_T (
                                                       ID VARCHAR(64) NOT NULL,
                                                       NAME VARCHAR(255) NULL,
                                                       RESOURCE_ID VARCHAR(255) NULL,
                                                       VROPS_REGION_ID NUMBER(19,0) NULL,
                                                       CLOUD_VROPS_REGION_ID VARCHAR(255) NULL,
                                                       CLUSTGER_ID VARCHAR(255) NULL,
                                                       CLOUD_CLUSTGER_ID VARCHAR(255) NULL,
                                                       HOST_ID VARCHAR(255) NULL,
                                                       CLOUD_HOST_ID VARCHAR(255) NULL,
                                                       DELETED VARCHAR(10) NULL,
                                                       STATUS VARCHAR(20) NULL,
                                                       POWERSTATE VARCHAR(20) NULL,
                                                       CREATED_TIME TIMESTAMP NULL,
                                                       UPDATED_TIME TIMESTAMP NULL,
                                                       TIMESTAMPS NUMBER(19,0) NULL,
                                                       VCPU NUMBER(10,0) NULL,
                                                       MEM_USED DOUBLE NULL,
                                                       MEM_CAPACITY DOUBLE NULL,
                                                       DISKSPACE_CAPACITY DOUBLE NULL,
                                                       DISKSPACE_FREESPACE DOUBLE NULL,
                                                       DISKSPACE_PROVISIONEDSPACE DOUBLE NULL,
                                                       CONSTRAINT INDEX33565707 PRIMARY KEY (ID)
);


-- SLGZT.SREF_CON_TAB134232731_LEVEL definition

CREATE TABLE SLGZT.SREF_CON_TAB134232731_LEVEL (
                                                   N_LEVEL NUMBER(10,0) NULL
);


-- SLGZT.SREF_CON_TAB134232731_REFED definition

CREATE TABLE SLGZT.SREF_CON_TAB134232731_REFED (
                                                   COL1 NVARCHAR2(256) NULL
);


-- SLGZT.SREF_CON_TAB134232731_REFING definition

CREATE TABLE SLGZT.SREF_CON_TAB134232731_REFING (
                                                    COL1 NVARCHAR2(256) NULL
);


-- SLGZT.SREF_CON_TAB134232732_LEVEL definition

CREATE TABLE SLGZT.SREF_CON_TAB134232732_LEVEL (
                                                   N_LEVEL NUMBER(10,0) NULL
);


-- SLGZT.SREF_CON_TAB134232732_REFED definition

CREATE TABLE SLGZT.SREF_CON_TAB134232732_REFED (
                                                   COL1 NVARCHAR2(256) NULL
);


-- SLGZT.SREF_CON_TAB134232732_REFING definition

CREATE TABLE SLGZT.SREF_CON_TAB134232732_REFING (
                                                    COL1 NVARCHAR2(256) NULL
);


-- SLGZT.SREF_CON_TAB134232733_LEVEL definition

CREATE TABLE SLGZT.SREF_CON_TAB134232733_LEVEL (
                                                   N_LEVEL NUMBER(10,0) NULL
);


-- SLGZT.SREF_CON_TAB134232733_REFED definition

CREATE TABLE SLGZT.SREF_CON_TAB134232733_REFED (
                                                   COL1 NVARCHAR2(256) NULL
);


-- SLGZT.SREF_CON_TAB134232733_REFING definition

CREATE TABLE SLGZT.SREF_CON_TAB134232733_REFING (
                                                    COL1 NVARCHAR2(256) NULL
);


-- SLGZT.USERS_P_T definition

CREATE TABLE SLGZT.USERS_P_T (
                                 ACCOUNT VARCHAR(64) NOT NULL,
                                 PWD VARCHAR(128) NULL,
                                 CREATED_TIME VARCHAR(128) NULL,
                                 CONSTRAINT INDEX33564142 PRIMARY KEY (ACCOUNT)
);


-- SLGZT.WOC_ACTIVE_STATISTICS definition

CREATE TABLE SLGZT.WOC_ACTIVE_STATISTICS (
                                             ID VARCHAR2(36) NOT NULL,
                                             STAT_DATE TIMESTAMP NOT NULL,
                                             ACTIVE_USER_COUNT NUMBER(10,0) NULL,
                                             API_ACCESS_COUNT NUMBER(19,0) NULL,
                                             REMARK VARCHAR2(255) NULL,
                                             ENABLED NUMBER(1,0) DEFAULT 1 NULL,
                                             CREATE_TIME TIMESTAMP NULL,
                                             MODIFY_TIME TIMESTAMP NULL,
                                             CONSTRAINT INDEX33565691 PRIMARY KEY (ID)
);


-- SLGZT.WOC_CHANGE_WORK_ORDER definition

CREATE TABLE SLGZT.WOC_CHANGE_WORK_ORDER (
                                             ID VARCHAR2(32) NOT NULL,
                                             ORDER_TYPE VARCHAR2(32) NULL,
                                             ORDER_TITLE VARCHAR2(255) NULL,
                                             ORDER_CODE VARCHAR2(64) NULL,
                                             ORDER_STATUS VARCHAR2(32) NULL,
                                             CREATED_BY NUMBER NULL,
                                             CREATED_USER_NAME VARCHAR2(64) NULL,
                                             DEPARTMENT_NAME VARCHAR2(128) NULL,
                                             UPDATED_BY NUMBER NULL,
                                             BUREAU_USER_NAME VARCHAR2(64) NULL,
                                             LEVEL_THREE_LEADER_NAME VARCHAR2(64) NULL,
                                             LEVEL_THREE_LEADER_ID NUMBER NULL,
                                             BUSINESS_DEPART_LEADER_ID NUMBER NULL,
                                             BUSINESS_DEPART_LEADER_NAME VARCHAR2(64) NULL,
                                             SECOND_LEVEL_CLOUD_LEADER_ID NUMBER NULL,
                                             SECOND_LEVEL_CLOUD_LEADER_NAME VARCHAR2(64) NULL,
                                             THREE_LEVEL_CLOUD_LEADER_ID NUMBER NULL,
                                             THREE_LEVEL_CLOUD_LEADER_NAME VARCHAR2(64) NULL,
                                             BUSINESS_SYSTEM_ID NUMBER NULL,
                                             BUSINESS_SYSTEM_NAME VARCHAR2(128) NULL,
                                             BUSINESS_SYSTEM_CODE VARCHAR2(128) NULL,
                                             MODULE_ID NUMBER NULL,
                                             MODULE_NAME VARCHAR2(128) NULL,
                                             TENANT_ID NUMBER NULL,
                                             TENANT_NAME VARCHAR2(128) NULL,
                                             BILL_ID VARCHAR2(64) NULL,
                                             CUSTOM_NO VARCHAR2(64) NULL,
                                             MANUFACTURER VARCHAR2(300) NULL,
                                             MANUFACTURER_CONTACTS VARCHAR2(64) NULL,
                                             MANUFACTURER_MOBILE VARCHAR2(11) NULL,
                                             STATUS NUMBER DEFAULT 1 NULL,
                                             ACTIVITI_ID VARCHAR2(255) NULL,
                                             ACTIVITE_KEY VARCHAR2(128) NULL,
                                             CURRENT_NODE_CODE VARCHAR2(50) NULL,
                                             CURRENT_NODE_NAME VARCHAR2(64) NULL,
                                             ORDER_DESC VARCHAR2(1024) NULL,
                                             RESOURCE_APPLY_FILES TEXT NULL,
                                             CREATE_TIME DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                             MODIFY_TIME DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                             WORK_ORDER_START_TIME DATETIME DEFAULT CURRENT_TIMESTAMP NULL,
                                             WORK_ORDER_END_TIME DATETIME DEFAULT CURRENT_TIMESTAMP NULL,
                                             CURRENT_NODE_START_TIME DATETIME DEFAULT CURRENT_TIMESTAMP NULL,
                                             CONSTRAINT INDEX33565546 PRIMARY KEY (ID)
);


-- SLGZT.WOC_CHANGE_WORK_ORDER_0506 definition

CREATE TABLE SLGZT.WOC_CHANGE_WORK_ORDER_0506 (
                                                  ID VARCHAR2(32) NULL,
                                                  ORDER_TYPE VARCHAR2(32) NULL,
                                                  ORDER_TITLE VARCHAR2(255) NULL,
                                                  ORDER_CODE VARCHAR2(64) NULL,
                                                  ORDER_STATUS VARCHAR2(32) NULL,
                                                  CREATED_BY NUMBER NULL,
                                                  CREATED_USER_NAME VARCHAR2(64) NULL,
                                                  DEPARTMENT_NAME VARCHAR2(128) NULL,
                                                  UPDATED_BY NUMBER NULL,
                                                  BUREAU_USER_NAME VARCHAR2(64) NULL,
                                                  LEVEL_THREE_LEADER_NAME VARCHAR2(64) NULL,
                                                  LEVEL_THREE_LEADER_ID NUMBER NULL,
                                                  BUSINESS_DEPART_LEADER_ID NUMBER NULL,
                                                  BUSINESS_DEPART_LEADER_NAME VARCHAR2(64) NULL,
                                                  SECOND_LEVEL_CLOUD_LEADER_ID NUMBER NULL,
                                                  SECOND_LEVEL_CLOUD_LEADER_NAME VARCHAR2(64) NULL,
                                                  THREE_LEVEL_CLOUD_LEADER_ID NUMBER NULL,
                                                  THREE_LEVEL_CLOUD_LEADER_NAME VARCHAR2(64) NULL,
                                                  BUSINESS_SYSTEM_ID NUMBER NULL,
                                                  BUSINESS_SYSTEM_NAME VARCHAR2(128) NULL,
                                                  BUSINESS_SYSTEM_CODE VARCHAR2(128) NULL,
                                                  MODULE_ID NUMBER NULL,
                                                  MODULE_NAME VARCHAR2(128) NULL,
                                                  TENANT_ID NUMBER NULL,
                                                  TENANT_NAME VARCHAR2(128) NULL,
                                                  BILL_ID VARCHAR2(64) NULL,
                                                  CUSTOM_NO VARCHAR2(64) NULL,
                                                  MANUFACTURER VARCHAR2(300) NULL,
                                                  MANUFACTURER_CONTACTS VARCHAR2(64) NULL,
                                                  MANUFACTURER_MOBILE VARCHAR2(11) NULL,
                                                  STATUS NUMBER NULL,
                                                  ACTIVITI_ID VARCHAR2(255) NULL,
                                                  ACTIVITE_KEY VARCHAR2(128) NULL,
                                                  CURRENT_NODE_CODE VARCHAR2(50) NULL,
                                                  CURRENT_NODE_NAME VARCHAR2(64) NULL,
                                                  ORDER_DESC VARCHAR2(1024) NULL,
                                                  RESOURCE_APPLY_FILES TEXT NULL,
                                                  CREATE_TIME DATETIME NOT NULL,
                                                  MODIFY_TIME DATETIME NOT NULL,
                                                  WORK_ORDER_START_TIME DATETIME NULL,
                                                  WORK_ORDER_END_TIME DATETIME NULL,
                                                  CURRENT_NODE_START_TIME DATETIME NULL
);


-- SLGZT.WOC_CHANGE_WORK_ORDER_PRODUCT definition

CREATE TABLE SLGZT.WOC_CHANGE_WORK_ORDER_PRODUCT (
                                                     ID NUMBER(19,0) NOT NULL,
                                                     WORK_ORDER_ID VARCHAR(32) NOT NULL,
                                                     CREATE_WORK_ORDER_ID VARCHAR(32) NULL,
                                                     CHANGE_TYPE VARCHAR(50) NOT NULL,
                                                     PRODUCT_TYPE VARCHAR(50) NOT NULL,
                                                     PROPERTY_SNAPSHOT TEXT NULL,
                                                     PARENT_PRODUCT_ID NUMBER(19,0) DEFAULT 0 NOT NULL,
                                                     CHANGE_STATUS VARCHAR(50) NOT NULL,
                                                     MESSAGE VARCHAR(500) NULL,
                                                     ENABLED BIT DEFAULT 1 NOT NULL,
                                                     CREATE_TIME DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                                     MODIFY_TIME DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                                     GID VARCHAR2(30) NULL,
                                                     SUB_ORDER_ID VARCHAR(50) NULL,
                                                     RESOURCE_DETAIL_ID VARCHAR(50) NULL,
                                                     BLOCK_WARNING BIT DEFAULT 0 NOT NULL,
                                                     TENANT_CONFIRM BIT DEFAULT 0 NOT NULL,
                                                     CONSTRAINT INDEX33565558 PRIMARY KEY (ID)
);


-- SLGZT.WOC_CONFIG definition

CREATE TABLE SLGZT.WOC_CONFIG (
                                  ID NUMBER NOT NULL,
                                  CONFIG_TYPE VARCHAR2(1020) NULL,
                                  CONFIG_CODE VARCHAR2(1020) NULL,
                                  CONFIG_NAME VARCHAR2(1020) NULL,
                                  CONFIG_DESC VARCHAR2(1020) NULL,
                                  SORT NUMBER NULL,
                                  CREATED_BY NUMBER NULL,
                                  CREATED_TIME TIMESTAMP NULL,
                                  UPDATED_BY NUMBER NULL,
                                  UPDATED_TIME TIMESTAMP NULL,
                                  STATUS NUMBER NULL,
                                  CONFIG_GROUP VARCHAR2(1020) NULL,
                                  CONFIG_VALUE CLOB NULL,
                                  CONSTRAINT INDEX33565445 PRIMARY KEY (ID)
);


-- SLGZT.WOC_DYNAMIC_CONFIG definition

CREATE TABLE SLGZT.WOC_DYNAMIC_CONFIG (
                                          ID NUMBER NOT NULL,
                                          "TYPE" VARCHAR2(32) NULL,
                                          CONFIG_JSON VARCHAR2(255) NULL,
                                          CREATED_BY NUMBER NULL,
                                          UPDATED_BY NUMBER NULL,
                                          STATUS NUMBER DEFAULT 1 NULL,
                                          CREATED_TIME TIMESTAMP DEFAULT SYSDATE NULL,
                                          UPDATED_TIME TIMESTAMP DEFAULT SYSDATE NULL,
                                          CONSTRAINT INDEX33565453 PRIMARY KEY (ID)
);


-- SLGZT.WOC_EXTERNAL_ORDER definition

CREATE TABLE SLGZT.WOC_EXTERNAL_ORDER (
                                          ID VARCHAR2(32) NOT NULL,
                                          ORDER_TITLE VARCHAR2(255) NULL,
                                          ORDER_CODE VARCHAR2(64) NULL,
                                          ORDER_TYPE VARCHAR2(32) NULL,
                                          BILL_ID VARCHAR2(64) NULL,
                                          CUSTOM_NO VARCHAR2(64) NULL,
                                          CREATED_BY NUMBER NULL,
                                          BUSI_SYSTEM_ID NUMBER NULL,
                                          CREATE_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                          STATUS NUMBER DEFAULT 1 NULL,
                                          ORDER_DESC VARCHAR2(1024) NULL,
                                          DOMAIN_CODE VARCHAR2(32) NULL,
                                          CATALOGUE_DOMAIN_CODE VARCHAR2(32) NULL,
                                          MODULE_ID NUMBER NULL,
                                          MANUFACTURER VARCHAR2(300) NULL,
                                          MANUFACTURER_CONTACTS VARCHAR2(64) NULL,
                                          MANUFACTURER_MOBILE VARCHAR2(11) NULL,
                                          TENANT_ID NUMBER NULL,
                                          TENANT_NAME VARCHAR2(128) NULL,
                                          BUSI_SYSTEM_NAME VARCHAR2(64) NULL,
                                          CREATED_USER_NAME VARCHAR2(64) NULL,
                                          DOMAIN_NAME VARCHAR(50) NULL,
                                          BUSINESS_SYSTEM_NAME VARCHAR2(64) NULL,
                                          CATALOGUE_DOMAIN_NAME VARCHAR2(64) NULL,
                                          BUREAU_USER_NAME VARCHAR2(64) NULL,
                                          DEPARTMENT_NAME VARCHAR2(128) NULL,
                                          MODULE_NAME VARCHAR2(50) NULL,
                                          CONSTRAINT INDEX33565552 PRIMARY KEY (ID)
);


-- SLGZT.WOC_EXTERNAL_ORDER_PRODUCT definition

CREATE TABLE SLGZT.WOC_EXTERNAL_ORDER_PRODUCT (
                                                  ID NUMBER(19,0) NOT NULL,
                                                  WORK_ORDER_ID VARCHAR2(32) NOT NULL,
                                                  PRODUCT_TYPE VARCHAR2(50) NOT NULL,
                                                  PROPERTY_SNAPSHOT TEXT NULL,
                                                  PARENT_PRODUCT_ID NUMBER(19,0) DEFAULT 0 NOT NULL,
                                                  OPEN_STATUS VARCHAR2(50) NOT NULL,
                                                  MESSAGE VARCHAR2(500) NULL,
                                                  ENABLED NUMBER DEFAULT 1 NOT NULL,
                                                  CREATE_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                                  MODIFY_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                                  GID VARCHAR2(30) NULL,
                                                  EXT VARCHAR2(500) NULL,
                                                  SUB_ORDER_ID VARCHAR2(50) NULL,
                                                  CONSTRAINT INDEX33565554 PRIMARY KEY (ID)
);


-- SLGZT.WOC_EXTERNAL_RECOVERY_PRODUCT definition

CREATE TABLE SLGZT.WOC_EXTERNAL_RECOVERY_PRODUCT (
                                                     ID NUMBER(19,0) NOT NULL,
                                                     WORK_ORDER_ID VARCHAR2(32) NOT NULL,
                                                     PRODUCT_TYPE VARCHAR2(50) NOT NULL,
                                                     PROPERTY_SNAPSHOT TEXT NULL,
                                                     PARENT_PRODUCT_ID NUMBER(19,0) DEFAULT 0 NOT NULL,
                                                     RECOVERY_STATUS VARCHAR2(50) NOT NULL,
                                                     MESSAGE VARCHAR2(500) NULL,
                                                     CREATE_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                                     MODIFY_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                                     GID VARCHAR2(30) NULL,
                                                     SUB_ORDER_ID VARCHAR2(50) NULL,
                                                     RESOURCE_DETAIL_ID VARCHAR2(50) NULL,
                                                     CONSTRAINT INDEX33565560 PRIMARY KEY (ID)
);


-- SLGZT.WOC_FILE definition

CREATE TABLE SLGZT.WOC_FILE (
                                ID VARCHAR2(90) NOT NULL,
                                FILE_CODE VARCHAR2(90) NULL,
                                FILE_TYPE VARCHAR2(32) NULL,
                                FILE_NAME VARCHAR2(90) NULL,
                                FILE_PATH VARCHAR2(900) NULL,
                                SUFFIX VARCHAR2(90) NULL,
                                CREATED_BY NUMBER NULL,
                                CREATED_TIME TIMESTAMP DEFAULT SYSDATE NULL,
                                UPDATED_BY NUMBER NULL,
                                UPDATED_TIME TIMESTAMP NULL,
                                STATUS NUMBER DEFAULT 1 NULL,
                                TARGET_NAME VARCHAR2(90) NULL,
                                CONSTRAINT INDEX33565538 PRIMARY KEY (ID)
);


-- SLGZT.WOC_IMAGE_FILE definition

CREATE TABLE SLGZT.WOC_IMAGE_FILE (
                                      ID NUMBER(19,0) NOT NULL,
                                      IMAGE_NAME VARCHAR2(255) NULL,
                                      OS_NAME VARCHAR2(50) NULL,
                                      OS_VERSION VARCHAR2(50) NULL,
                                      "SIZE" VARCHAR2(20) NULL,
                                      FORMAT VARCHAR2(20) NULL,
                                      UPLOAD_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL,
                                      UPLOAD_COMPLETED NUMBER(1,0) DEFAULT 0 NULL,
                                      PROGRESS NUMBER(3,0) DEFAULT 0 NULL,
                                      MD5 VARCHAR2(32) NULL,
                                      DOWNLOAD_URL VARCHAR2(500) NULL,
                                      TOTAL_PARTS NUMBER(5,0) NULL,
                                      CONSTRAINT INDEX33565763 PRIMARY KEY (ID)
);


-- SLGZT.WOC_IP_ADDRESS definition

CREATE TABLE SLGZT.WOC_IP_ADDRESS (
                                      ID NUMBER NOT NULL,
                                      NETWORK_ID VARCHAR2(64) NOT NULL,
                                      SUBNET_ID VARCHAR2(64) NOT NULL,
                                      IP VARCHAR2(64) NOT NULL,
                                      CMDB_INSTANCE_ID VARCHAR2(64) NULL,
                                      "TYPE" VARCHAR2(16) NULL,
                                      DELETED NUMBER DEFAULT 1 NOT NULL,
                                      STATUS VARCHAR2(16) NULL,
                                      CONSTRAINT INDEX33565494 PRIMARY KEY (ID)
);


-- SLGZT.WOC_NAT_RULE definition

CREATE TABLE SLGZT.WOC_NAT_RULE (
                                    ID NUMBER NOT NULL,
                                    SOURCE_ADDRESS VARCHAR2(64) NOT NULL,
                                    TRANSFORM_ADDRESS VARCHAR2(64) NOT NULL,
                                    RULE_TYPE VARCHAR2(64) NOT NULL,
                                    RESOURCE_POOL_ID NUMBER NULL,
                                    GLOBAL_ID VARCHAR2(64) NULL,
                                    ORDER_CODE VARCHAR2(64) NULL,
                                    GOODS_ORDER_ID NUMBER NULL,
                                    DOMAIN_CODE VARCHAR2(64) NULL,
                                    CREATED_TIME TIMESTAMP NULL,
                                    UPDATED_TIME TIMESTAMP NULL,
                                    CREATED_BY NUMBER NOT NULL,
                                    UPDATED_BY NUMBER NULL,
                                    DELETED NUMBER DEFAULT 1 NOT NULL,
                                    STATUS VARCHAR2(10) NULL,
                                    DESCRIPTION VARCHAR2(512) NULL,
                                    CONSTRAINT INDEX33565508 PRIMARY KEY (ID)
);


-- SLGZT.WOC_NETWORK_GOODS definition

CREATE TABLE SLGZT.WOC_NETWORK_GOODS (
                                         ID NUMBER NOT NULL,
                                         NETWORK_ID VARCHAR2(64) NOT NULL,
                                         SUBNET_ID VARCHAR2(64) NOT NULL,
                                         DEVICE_ID VARCHAR2(64) NULL,
                                         ORDER_ID VARCHAR2(64) NOT NULL,
                                         ORDER_GOODS_ID VARCHAR2(64) NOT NULL,
                                         IP VARCHAR2(64) NULL,
                                         IP_INSTANCE_ID VARCHAR2(64) NULL,
                                         "TYPE" VARCHAR2(16) NULL,
                                         DELETED NUMBER DEFAULT 1 NOT NULL,
                                         STATUS VARCHAR2(16) NULL,
                                         CONSTRAINT INDEX33565540 PRIMARY KEY (ID)
);


-- SLGZT.WOC_NETWORK_ORDER definition

CREATE TABLE SLGZT.WOC_NETWORK_ORDER (
                                         ID VARCHAR2(50) NOT NULL,
                                         NAME VARCHAR2(64) NOT NULL,
                                         ORDER_ID VARCHAR2(60) NOT NULL,
                                         SUBNET_NUM NUMBER NOT NULL,
                                         TENANT_ID NUMBER NULL,
                                         ACCOUNT VARCHAR2(50) NULL,
                                         REGION_CODE VARCHAR2(90) NULL,
                                         AZ_CODE VARCHAR2(60) NULL,
                                         SYSTEM_SOURCE VARCHAR2(16) NULL,
                                         CATALOGUE_DOMAIN_CODE VARCHAR2(50) NULL,
                                         CATALOGUE_DOMAIN_NAME VARCHAR2(50) NULL,
                                         VLAN_ID VARCHAR2(50) NULL,
                                         VLAN VARCHAR2(50) NULL,
                                         PLANE VARCHAR2(16) NULL,
                                         DOMAIN_CODE VARCHAR2(50) NULL,
                                         DOMAIN_NAME VARCHAR2(50) NULL,
                                         FUNCTIONAL_MODULE VARCHAR2(50) NULL,
                                         NETWORK_TYPE VARCHAR2(16) NULL,
                                         RESOURCE_ID VARCHAR2(50) NULL,
                                         INSTANCE_ID VARCHAR2(60) NULL,
                                         CREATED_TIME TIMESTAMP NULL,
                                         UPDATED_TIME TIMESTAMP NULL,
                                         CREATED_BY NUMBER NOT NULL,
                                         UPDATED_BY NUMBER NULL,
                                         OVERALL_STATUS VARCHAR2(16) NULL,
                                         DELETED NUMBER DEFAULT 1 NOT NULL,
                                         GID VARCHAR2(50) NULL,
                                         STATUS VARCHAR2(16) NULL,
                                         MESSAGE VARCHAR2(2000) NULL,
                                         DETAIL VARCHAR2(5000) NULL,
                                         SUBNET_RECOVERY_STATUS NUMBER NULL,
                                         DESCRIPTION VARCHAR2(512) NULL,
                                         RECOVERY_STATUS NUMBER DEFAULT 0 NOT NULL,
                                         CONFIRM_OR_NOT NUMBER NULL,
                                         TENANT_NAME VARCHAR2(60) NULL,
                                         BUSINESS_SYS_ID NUMBER NULL,
                                         BUSINESS_SYS_NAME VARCHAR2(60) NULL,
                                         APPLY_USER_ID NUMBER NULL,
                                         APPLY_USER_NAME VARCHAR2(60) NULL,
                                         MODULE_ID NUMBER NULL,
                                         MODULE_NAME VARCHAR2(60) NULL,
                                         SOURCE_TYPE VARCHAR2(10) DEFAULT 'BZ' NOT NULL,
                                         ORDER_CODE VARCHAR2(64) NULL,
                                         CONSTRAINT INDEX33565490 PRIMARY KEY (ID)
);


-- SLGZT.WOC_NETWORK_ORDER_0506 definition

CREATE TABLE SLGZT.WOC_NETWORK_ORDER_0506 (
                                              ID VARCHAR2(50) NULL,
                                              NAME VARCHAR2(64) NOT NULL,
                                              ORDER_ID VARCHAR2(60) NOT NULL,
                                              SUBNET_NUM NUMBER NOT NULL,
                                              TENANT_ID NUMBER NULL,
                                              ACCOUNT VARCHAR2(50) NULL,
                                              REGION_CODE VARCHAR2(90) NULL,
                                              AZ_CODE VARCHAR2(60) NULL,
                                              SYSTEM_SOURCE VARCHAR2(16) NULL,
                                              CATALOGUE_DOMAIN_CODE VARCHAR2(50) NULL,
                                              CATALOGUE_DOMAIN_NAME VARCHAR2(50) NULL,
                                              VLAN_ID VARCHAR2(50) NULL,
                                              VLAN VARCHAR2(50) NULL,
                                              PLANE VARCHAR2(16) NULL,
                                              DOMAIN_CODE VARCHAR2(50) NULL,
                                              DOMAIN_NAME VARCHAR2(50) NULL,
                                              FUNCTIONAL_MODULE VARCHAR2(50) NULL,
                                              NETWORK_TYPE VARCHAR2(16) NULL,
                                              RESOURCE_ID VARCHAR2(50) NULL,
                                              INSTANCE_ID VARCHAR2(60) NULL,
                                              CREATED_TIME TIMESTAMP NULL,
                                              UPDATED_TIME TIMESTAMP NULL,
                                              CREATED_BY NUMBER NOT NULL,
                                              UPDATED_BY NUMBER NULL,
                                              OVERALL_STATUS VARCHAR2(16) NULL,
                                              DELETED NUMBER NOT NULL,
                                              GID VARCHAR2(50) NULL,
                                              STATUS VARCHAR2(16) NULL,
                                              MESSAGE VARCHAR2(2000) NULL,
                                              DETAIL VARCHAR2(5000) NULL,
                                              SUBNET_RECOVERY_STATUS NUMBER NULL,
                                              DESCRIPTION VARCHAR2(512) NULL,
                                              RECOVERY_STATUS NUMBER NOT NULL,
                                              CONFIRM_OR_NOT NUMBER NULL,
                                              TENANT_NAME VARCHAR2(60) NULL,
                                              BUSINESS_SYS_ID NUMBER NULL,
                                              BUSINESS_SYS_NAME VARCHAR2(60) NULL,
                                              APPLY_USER_ID NUMBER NULL,
                                              APPLY_USER_NAME VARCHAR2(60) NULL,
                                              MODULE_ID NUMBER NULL,
                                              MODULE_NAME VARCHAR2(60) NULL,
                                              SOURCE_TYPE VARCHAR2(10) NOT NULL,
                                              ORDER_CODE VARCHAR2(64) NULL
);


-- SLGZT.WOC_NETWORK_SUBNET_ORDER definition

CREATE TABLE SLGZT.WOC_NETWORK_SUBNET_ORDER (
                                                ID VARCHAR2(50) NOT NULL,
                                                SUBNET_NAME VARCHAR2(128) NOT NULL,
                                                NETWORK_ID VARCHAR2(50) NOT NULL,
                                                INSTANCE_ID VARCHAR2(60) NULL,
                                                CIDR VARCHAR2(64) NULL,
                                                GATEWAY VARCHAR2(64) NULL,
                                                NETMASK VARCHAR2(50) NULL,
                                                IP_VERSION VARCHAR2(8) NULL,
                                                RESOURCE_ID VARCHAR2(50) NULL,
                                                LEVEL2_INSTANCE_ID VARCHAR2(60) NULL,
                                                CREATED_TIME TIMESTAMP NULL,
                                                UPDATED_TIME TIMESTAMP NULL,
                                                DELETED NUMBER DEFAULT 1 NOT NULL,
                                                STATUS VARCHAR2(16) NULL,
                                                RECOVERY_STATUS NUMBER DEFAULT 0 NOT NULL,
                                                MESSAGE VARCHAR2(2000) NULL,
                                                DESCRIPTION VARCHAR2(512) NULL,
                                                UUID VARCHAR2(64) NULL,
                                                CONSTRAINT INDEX33565492 PRIMARY KEY (ID)
);


-- SLGZT.WOC_NON_STANDER_WORK_ORDER definition

CREATE TABLE SLGZT.WOC_NON_STANDER_WORK_ORDER (
                                                  ID VARCHAR2(32) NOT NULL,
                                                  ORDER_TITLE VARCHAR2(255) NULL,
                                                  ORDER_CODE VARCHAR2(64) NULL,
                                                  ORDER_TYPE VARCHAR2(32) NULL,
                                                  BILL_ID VARCHAR2(64) NULL,
                                                  CUSTOM_NO VARCHAR2(64) NULL,
                                                  ORDER_STATUS VARCHAR2(32) NULL,
                                                  CREATED_BY NUMBER NULL,
                                                  BUSINESS_SYSTEM_ID NUMBER NULL,
                                                  BUSINESS_SYSTEM_NAME VARCHAR2(64) NULL,
                                                  CREATE_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL,
                                                  MODIFY_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL,
                                                  UPDATED_BY NUMBER NULL,
                                                  CUSTOMER_ID VARCHAR2(64) NULL,
                                                  CUSTOMER_NAME VARCHAR2(64) NULL,
                                                  CUSTOMER_CODE VARCHAR2(64) NULL,
                                                  CONTACT_PERSON VARCHAR2(64) NULL,
                                                  CUSTOMER_MANAGER VARCHAR2(64) NULL,
                                                  CONTACT_PHONE VARCHAR2(11) NULL,
                                                  MANAGER_PHONE VARCHAR2(11) NULL,
                                                  STATUS NUMBER DEFAULT 1 NULL,
                                                  ACTIVITI_ID VARCHAR2(255) NULL,
                                                  ACTIVITE_KEY VARCHAR2(128) NULL,
                                                  ORDER_DESC VARCHAR2(1024) NULL,
                                                  DOMAIN_CODE VARCHAR2(32) NULL,
                                                  CATALOGUE_DOMAIN_CODE VARCHAR2(32) NULL,
                                                  START_ROLE_CODE VARCHAR2(32) NULL,
                                                  MODULE_ID NUMBER NULL,
                                                  TENANT_ID NUMBER NULL,
                                                  CURRENT_NODE_CODE VARCHAR2(50) NULL,
                                                  TENANT_NAME VARCHAR2(128) NULL,
                                                  CREATED_USER_NAME VARCHAR2(64) NULL,
                                                  CURRENT_NODE_NAME VARCHAR2(64) NULL,
                                                  WORK_ORDER_START_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL,
                                                  WORK_ORDER_END_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL,
                                                  CURRENT_NODE_START_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL,
                                                  DOMAIN_NAME VARCHAR2(50) NULL,
                                                  RESOURCE_APPLY_FILE VARCHAR2(6000) NULL,
                                                  PRODUCT_APPLY_FILE VARCHAR2(6000) NULL,
                                                  CATALOGUE_DOMAIN_NAME VARCHAR2(64) NULL,
                                                  BUREAU_USER_NAME VARCHAR2(64) NULL,
                                                  DEPARTMENT_NAME VARCHAR2(128) NULL,
                                                  MODULE_NAME VARCHAR2(50) NULL,
                                                  BUSINESS_DEPART_LEADER_ID NUMBER NULL,
                                                  BUSINESS_DEPART_LEADER_NAME VARCHAR2(64) NULL,
                                                  AUDIT_LOG_LIST TEXT NULL,
                                                  OFFLINE_OPEN BIT NULL,
                                                  CONTRACT_COST NUMBER(10,2) NULL,
                                                  PRODUCT_TOTAL_COST NUMBER(10,2) NULL,
                                                  REMARK VARCHAR2(512) NULL,
                                                  CONSTRAINT INDEX33565773 PRIMARY KEY (ID)
);


-- SLGZT.WOC_NON_STANDER_WORK_ORDER_PRODUCT definition

CREATE TABLE SLGZT.WOC_NON_STANDER_WORK_ORDER_PRODUCT (
                                                          ID NUMBER(19,0) NOT NULL,
                                                          WORK_ORDER_ID VARCHAR(32) NOT NULL,
                                                          PRODUCT_TYPE VARCHAR(50) NOT NULL,
                                                          PROPERTY_SNAPSHOT TEXT NULL,
                                                          PARENT_PRODUCT_ID NUMBER(19,0) DEFAULT 0 NOT NULL,
                                                          OPEN_STATUS VARCHAR2(50) NOT NULL,
                                                          MESSAGE VARCHAR2(500) NULL,
                                                          ENABLED BIT DEFAULT 1 NOT NULL,
                                                          CREATE_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                                          MODIFY_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                                          GID VARCHAR2(30) NULL,
                                                          EXT VARCHAR2(500) NULL,
                                                          SUB_ORDER_ID VARCHAR2(50) NULL,
                                                          CONSTRAINT INDEX33565775 PRIMARY KEY (ID)
);


-- SLGZT.WOC_OBS_OPEN_TASK definition

CREATE TABLE SLGZT.WOC_OBS_OPEN_TASK (
                                         PRODUCT_ORDER_ID NUMBER NOT NULL,
                                         WORK_ORDER_ID VARCHAR2(50) NULL,
                                         STATUS VARCHAR2(255) NULL,
                                         CREATE_TIME TIMESTAMP DEFAULT SYSDATE NULL,
                                         UPDATE_TIME TIMESTAMP DEFAULT SYSDATE NULL,
                                         MESSAGE VARCHAR2(255) NULL,
                                         CONSTRAINT INDEX33565550 PRIMARY KEY (PRODUCT_ORDER_ID)
);


-- SLGZT.WOC_OPERATION_LOG definition

CREATE TABLE SLGZT.WOC_OPERATION_LOG (
                                         ID NUMBER(20,0) NOT NULL,
                                         USER_ID NUMBER(20,0) NULL,
                                         USER_NAME VARCHAR2(100) NULL,
                                         DESCRIPTION VARCHAR2(500) NULL,
                                         OPERATION_TYPE VARCHAR2(100) NULL,
                                         "METHOD" VARCHAR2(100) NULL,
                                         REQUEST_URL VARCHAR2(500) NULL,
                                         REQUEST_IP VARCHAR2(100) NULL,
                                         REQUEST_PARAMS CLOB NULL,
                                         RESPONSE_RESULT CLOB NULL,
                                         EXECUTION_TIME NUMBER(10,0) NULL,
                                         STATUS VARCHAR2(20) NULL,
                                         ERROR_MESSAGE VARCHAR2(2000) NULL,
                                         TRACE_ID VARCHAR2(100) NULL,
                                         CREATE_TIME TIMESTAMP NULL,
                                         MODIFY_TIME TIMESTAMP NULL,
                                         USER_ACCOUNT VARCHAR(50) NULL,
                                         CONSTRAINT INDEX33565686 PRIMARY KEY (ID)
);
CREATE INDEX IDX_WOC_OPLOG_CREATE_TIME ON SLGZT.WOC_OPERATION_LOG (CREATE_TIME);
CREATE INDEX IDX_WOC_OPLOG_TRACE_ID ON SLGZT.WOC_OPERATION_LOG (TRACE_ID);
CREATE INDEX IDX_WOC_OPLOG_USER_ID ON SLGZT.WOC_OPERATION_LOG (USER_ID);


-- SLGZT.WOC_ORDER_SHOPPING_CART definition

CREATE TABLE SLGZT.WOC_ORDER_SHOPPING_CART (
                                               ID NUMBER NOT NULL,
                                               GOOD_TYPE VARCHAR2(128) NULL,
                                               CREATED_BY NUMBER DEFAULT 0 NULL,
                                               DATA_TYPE NUMBER DEFAULT 0 NULL,
                                               STATUS NUMBER DEFAULT 1 NULL,
                                               ORDER_JSON_BYTES BLOB NULL,
                                               CREATED_TIME TIMESTAMP DEFAULT SYSDATE NULL,
                                               UPDATED_TIME TIMESTAMP DEFAULT SYSDATE NULL,
                                               UPDATED_BY NUMBER NULL,
                                               CONSTRAINT INDEX33565449 PRIMARY KEY (ID)
);


-- SLGZT.WOC_PROBLEM_HANDLING_LOG definition

CREATE TABLE SLGZT.WOC_PROBLEM_HANDLING_LOG (
                                                ID VARCHAR2(32) NOT NULL,
                                                PROBLEM_CONTENT VARCHAR2(4000) NULL,
                                                PROBLEM_TYPE VARCHAR2(50) NULL,
                                                PROBLEM_STATUS VARCHAR2(50) NULL,
                                                PUBLISH_TIME DATETIME NULL,
                                                COMPLETE_TIME DATETIME NULL,
                                                PUBLISHER VARCHAR2(100) NULL,
                                                ENABLED NUMBER(1,0) DEFAULT 1 NULL,
                                                CREATE_TIME DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                                MODIFY_TIME DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                                PROBLEM_TYPE_DESC VARCHAR(100) NULL,
                                                CONSTRAINT INDEX33565721 PRIMARY KEY (ID)
);


-- SLGZT.WOC_PRODUCT_SPEC_SUPPORT definition

CREATE TABLE SLGZT.WOC_PRODUCT_SPEC_SUPPORT (
                                                ID NUMBER(19,0) NOT NULL,
                                                DOMAIN_CODE VARCHAR2(64) NULL,
                                                DOMAIN_NAME VARCHAR2(128) NULL,
                                                AZ_ID VARCHAR2(64) NULL,
                                                AZ_NAME VARCHAR2(128) NULL,
                                                REGION_CODE VARCHAR2(64) NULL,
                                                REGION_ID VARCHAR2(64) NULL,
                                                REGION_NAME VARCHAR2(128) NULL,
                                                PRODUCT_TYPE VARCHAR2(64) NULL,
                                                EXTERNAL_ID VARCHAR2(64) NULL,
                                                SPEC_VALUE VARCHAR2(64) NULL,
                                                CONSTRAINT INDEX33565755 PRIMARY KEY (ID)
);


-- SLGZT.WOC_RECOVERY_WORK_ORDER definition

CREATE TABLE SLGZT.WOC_RECOVERY_WORK_ORDER (
                                               ID VARCHAR2(32) NOT NULL,
                                               RECOVERY_TYPE VARCHAR2(32) NULL,
                                               ORDER_TITLE VARCHAR2(255) NULL,
                                               ORDER_CODE VARCHAR2(64) NULL,
                                               ORDER_STATUS VARCHAR2(32) NULL,
                                               CREATED_BY NUMBER NULL,
                                               CREATED_USER_NAME VARCHAR2(64) NULL,
                                               DEPARTMENT_NAME VARCHAR2(64) NULL,
                                               UPDATED_BY NUMBER NULL,
                                               LEVEL_THREE_LEADER_NAME VARCHAR2(64) NULL,
                                               LEVEL_THREE_LEADER_ID NUMBER NULL,
                                               MODULE_ID NUMBER NULL,
                                               MODULE_NAME VARCHAR2(128) NULL,
                                               BILL_ID VARCHAR2(64) NULL,
                                               CUSTOM_NO VARCHAR2(64) NULL,
                                               TENANT_ID NUMBER NULL,
                                               TENANT_NAME VARCHAR2(64) NULL,
                                               BUSINESS_SYSTEM_ID NUMBER NULL,
                                               BUSINESS_SYSTEM_NAME VARCHAR2(128) NULL,
                                               BUSINESS_SYSTEM_CODE VARCHAR2(128) NULL,
                                               CURRENT_NODE_CODE VARCHAR2(64) NULL,
                                               CURRENT_NODE_NAME VARCHAR2(64) NULL,
                                               CREATE_TIME DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                               MODIFY_TIME DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                               WORK_ORDER_START_TIME DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                               WORK_ORDER_END_TIME DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                               CURRENT_NODE_START_TIME DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                               STATUS NUMBER DEFAULT 1 NULL,
                                               ACTIVITI_ID VARCHAR2(255) NULL,
                                               ACTIVITE_KEY VARCHAR2(128) NULL,
                                               ORDER_DESC VARCHAR2(1024) NULL,
                                               MANUFACTURER VARCHAR2(300) NULL,
                                               MANUFACTURER_CONTACTS VARCHAR2(64) NULL,
                                               MANUFACTURER_MOBILE VARCHAR2(11) NULL,
                                               CONSTRAINT INDEX33565544 PRIMARY KEY (ID)
);


-- SLGZT.WOC_RECOVERY_WORK_ORDER_0506 definition

CREATE TABLE SLGZT.WOC_RECOVERY_WORK_ORDER_0506 (
                                                    ID VARCHAR2(32) NULL,
                                                    RECOVERY_TYPE VARCHAR2(32) NULL,
                                                    ORDER_TITLE VARCHAR2(255) NULL,
                                                    ORDER_CODE VARCHAR2(64) NULL,
                                                    ORDER_STATUS VARCHAR2(32) NULL,
                                                    CREATED_BY NUMBER NULL,
                                                    CREATED_USER_NAME VARCHAR2(64) NULL,
                                                    DEPARTMENT_NAME VARCHAR2(64) NULL,
                                                    UPDATED_BY NUMBER NULL,
                                                    LEVEL_THREE_LEADER_NAME VARCHAR2(64) NULL,
                                                    LEVEL_THREE_LEADER_ID NUMBER NULL,
                                                    MODULE_ID NUMBER NULL,
                                                    MODULE_NAME VARCHAR2(128) NULL,
                                                    BILL_ID VARCHAR2(64) NULL,
                                                    CUSTOM_NO VARCHAR2(64) NULL,
                                                    TENANT_ID NUMBER NULL,
                                                    TENANT_NAME VARCHAR2(64) NULL,
                                                    BUSINESS_SYSTEM_ID NUMBER NULL,
                                                    BUSINESS_SYSTEM_NAME VARCHAR2(128) NULL,
                                                    BUSINESS_SYSTEM_CODE VARCHAR2(128) NULL,
                                                    CURRENT_NODE_CODE VARCHAR2(64) NULL,
                                                    CURRENT_NODE_NAME VARCHAR2(64) NULL,
                                                    CREATE_TIME DATETIME NOT NULL,
                                                    MODIFY_TIME DATETIME NOT NULL,
                                                    WORK_ORDER_START_TIME DATETIME NOT NULL,
                                                    WORK_ORDER_END_TIME DATETIME NOT NULL,
                                                    CURRENT_NODE_START_TIME DATETIME NOT NULL,
                                                    STATUS NUMBER NULL,
                                                    ACTIVITI_ID VARCHAR2(255) NULL,
                                                    ACTIVITE_KEY VARCHAR2(128) NULL,
                                                    ORDER_DESC VARCHAR2(1024) NULL,
                                                    MANUFACTURER VARCHAR2(300) NULL,
                                                    MANUFACTURER_CONTACTS VARCHAR2(64) NULL,
                                                    MANUFACTURER_MOBILE VARCHAR2(11) NULL
);


-- SLGZT.WOC_RECOVERY_WORK_ORDER_PRODUCT definition

CREATE TABLE SLGZT.WOC_RECOVERY_WORK_ORDER_PRODUCT (
                                                       ID NUMBER(19,0) NOT NULL,
                                                       WORK_ORDER_ID VARCHAR(32) NOT NULL,
                                                       PRODUCT_TYPE VARCHAR(50) NOT NULL,
                                                       PROPERTY_SNAPSHOT TEXT NULL,
                                                       PARENT_PRODUCT_ID NUMBER(19,0) DEFAULT 0 NOT NULL,
                                                       RECOVERY_STATUS VARCHAR(50) NOT NULL,
                                                       MESSAGE VARCHAR(500) NULL,
                                                       ENABLED BIT DEFAULT 1 NOT NULL,
                                                       CREATE_TIME DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                                       MODIFY_TIME DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                                       GID VARCHAR2(30) NULL,
                                                       EXT VARCHAR(500) NULL,
                                                       SUB_ORDER_ID VARCHAR(50) NULL,
                                                       HCM_STATUS VARCHAR(50) NULL,
                                                       RESOURCE_DETAIL_ID VARCHAR(50) NULL,
                                                       CMDB_ID VARCHAR(50) NULL,
                                                       TENANT_CONFIRM BIT DEFAULT 0 NULL,
                                                       CONSTRAINT INDEX33565542 PRIMARY KEY (ID)
);


-- SLGZT.WOC_REGION_CAPACITY_MAPPER definition

CREATE TABLE SLGZT.WOC_REGION_CAPACITY_MAPPER (
                                                  ID VARCHAR(36) NOT NULL,
                                                  DOMAIN_CODE VARCHAR2(64) NULL,
                                                  EXTERNAL_AZ_NAME VARCHAR2(128) NULL,
                                                  EXTERNAL_REGION_NAME VARCHAR2(128) NULL,
                                                  AZ_NAME VARCHAR2(128) NULL,
                                                  AZ_ID VARCHAR2(64) NULL,
                                                  REGION_ID VARCHAR2(64) NULL,
                                                  REGION_NAME VARCHAR2(128) NULL,
                                                  REGION_CODE VARCHAR2(64) NULL,
                                                  REGION_GROUP_CODE VARCHAR2(64) NULL,
                                                  AZ_CODE VARCHAR(36) NULL,
                                                  CONSTRAINT INDEX33565751 PRIMARY KEY (ID)
);


-- SLGZT.WOC_RESOURCE_DETAIL definition

CREATE TABLE SLGZT.WOC_RESOURCE_DETAIL (
                                           GOODS_ORDER_ID NUMBER(19,0) NULL,
                                           ORDER_ID VARCHAR(50) NULL,
                                           DEVICE_ID VARCHAR2(64) NULL,
                                           DEVICE_NAME VARCHAR2(64) NULL,
                                           RESOURCE_ID VARCHAR2(64) NULL,
                                           OS_VERSION VARCHAR2(32) NULL,
                                           SPEC VARCHAR2(255) NULL,
                                           SYS_DISK VARCHAR2(32) NULL,
                                           DATA_DISK VARCHAR2(32) NULL,
                                           IP VARCHAR2(100) NULL,
                                           EIP VARCHAR2(32) NULL,
                                           BAND_WIDTH VARCHAR2(16) NULL,
                                           APPLY_TIME VARCHAR2(16) NULL,
                                           TENANT_ID NUMBER NULL,
                                           TENANT_NAME VARCHAR2(32) NULL,
                                           BUSINESS_SYS_ID NUMBER NULL,
                                           BUSINESS_SYS_NAME VARCHAR2(64) NULL,
                                           CLOUD_PLATFORM VARCHAR2(100) NULL,
                                           RESOURCE_POOL_ID NUMBER NULL,
                                           RESOURCE_POOL_NAME VARCHAR2(64) NULL,
                                           ORDER_CODE VARCHAR2(64) NULL,
                                           RESOURCE_APPLY_TIME TIMESTAMP NULL,
                                           EXPIRE_TIME TIMESTAMP NULL,
                                           DEVICE_STATUS VARCHAR2(32) NULL,
                                           APPLY_USER_ID NUMBER NULL,
                                           APPLY_USER_NAME VARCHAR2(32) NULL,
                                           STATUS NUMBER DEFAULT 1 NULL,
                                           "TYPE" VARCHAR2(32) NULL,
                                           CREATE_TIME TIMESTAMP NULL,
                                           EFFECTIVE_TIME TIMESTAMP NULL,
                                           VPC_NAME VARCHAR2(255) DEFAULT '' NULL,
                                           SUBNET_NAME VARCHAR2(255) DEFAULT '' NULL,
                                           VM_ID VARCHAR2(255) DEFAULT '' NULL,
                                           MOUNT_OR_NOT VARCHAR2(10) NULL,
                                           ECS_NAME VARCHAR2(255) DEFAULT '' NULL,
                                           INSTANCE_UUID VARCHAR2(64) NULL,
                                           NET_ID VARCHAR2(64) NULL,
                                           NET_NAME VARCHAR2(500) NULL,
                                           MAC VARCHAR2(64) NULL,
                                           CONFIG_ID VARCHAR2(64) NULL,
                                           RECOVERY_STATUS NUMBER DEFAULT 0 NULL,
                                           VPC_ID VARCHAR2(36) NULL,
                                           SUBNET_ID VARCHAR2(32) NULL,
                                           DIS_DIMENSION_STATUS VARCHAR2(8) NULL,
                                           ACCESS_KEY VARCHAR2(200) NULL,
                                           SECRET_KEY VARCHAR2(200) NULL,
                                           PUBLIC_ADDRESS VARCHAR2(200) NULL,
                                           INTERNAL_ADDRESS VARCHAR2(200) NULL,
                                           VOLUME_ID VARCHAR2(100) NULL,
                                           EIP_ID VARCHAR2(100) NULL,
                                           MANAGE_IP VARCHAR2(32) NULL,
                                           SECURITY_GROUP_IDS VARCHAR2(100) NULL,
                                           ID NUMBER(19,0) NOT NULL,
                                           RECOVERY_STATUS_CN VARCHAR(50) NULL,
                                           CAPACITY VARCHAR(50) NULL,
                                           STORE_TYPE VARCHAR(50) NULL,
                                           DOMAIN_CODE VARCHAR(50) NULL,
                                           DOMAIN_NAME VARCHAR(50) NULL,
                                           BILL_ID VARCHAR(50) NULL,
                                           RESET_PWD VARCHAR(50) NULL,
                                           DIS_DIMENSION_STATUS_CN VARCHAR(50) NULL,
                                           RESOURCE_POOL_CODE VARCHAR(50) NULL,
                                           RECOVERY_INFO VARCHAR(50) NULL,
                                           RESOURCE_NAME VARCHAR(50) NULL,
                                           RESOURCE_CODE VARCHAR(50) NULL,
                                           AZ_CODE VARCHAR(50) NULL,
                                           AZ_NAME VARCHAR(50) NULL,
                                           AZ_ID VARCHAR(50) NULL,
                                           PROJECT_NAME VARCHAR(50) NULL,
                                           NETWORK_MODEL_SNAPSHOT VARCHAR(4000) NULL,
                                           CHANGE_STATUS VARCHAR2(32) DEFAULT 'un_change' NULL,
                                           SOURCE_TYPE VARCHAR2(10) DEFAULT 'BZ' NOT NULL,
                                           MODULE_ID NUMBER NULL,
                                           MODULE_NAME VARCHAR2(60) NULL,
                                           HANDOVER_STATUS VARCHAR2(32) DEFAULT '未交维' NULL,
                                           RELATED_DEVICE_ID VARCHAR2(64) NULL,
                                           RELATED_DEVICE_NAME VARCHAR2(255) NULL,
                                           RELATED_DEVICE_TYPE VARCHAR2(32) NULL,
                                           HIS_CHANGE_ORDER_IDS VARCHAR(2000) NULL,
                                           HIS_CHANGE_ORDER_CODES VARCHAR(4000) NULL,
                                           CONSTRAINT INDEX33565505 PRIMARY KEY (ID)
);
CREATE UNIQUE INDEX IDX_WOC_RESOURCE_DETAIL_GOODS_ORDER_ID ON SLGZT.WOC_RESOURCE_DETAIL (GOODS_ORDER_ID);


-- SLGZT.WOC_RESOURCE_DETAIL_0506 definition

CREATE TABLE SLGZT.WOC_RESOURCE_DETAIL_0506 (
                                                GOODS_ORDER_ID NUMBER(19,0) NULL,
                                                ORDER_ID VARCHAR(50) NULL,
                                                DEVICE_ID VARCHAR2(64) NULL,
                                                DEVICE_NAME VARCHAR2(64) NULL,
                                                RESOURCE_ID VARCHAR2(64) NULL,
                                                OS_VERSION VARCHAR2(32) NULL,
                                                SPEC VARCHAR2(255) NULL,
                                                SYS_DISK VARCHAR2(32) NULL,
                                                DATA_DISK VARCHAR2(32) NULL,
                                                IP VARCHAR2(100) NULL,
                                                EIP VARCHAR2(32) NULL,
                                                BAND_WIDTH VARCHAR2(16) NULL,
                                                APPLY_TIME VARCHAR2(16) NULL,
                                                TENANT_ID NUMBER NULL,
                                                TENANT_NAME VARCHAR2(32) NULL,
                                                BUSINESS_SYS_ID NUMBER NULL,
                                                BUSINESS_SYS_NAME VARCHAR2(64) NULL,
                                                CLOUD_PLATFORM VARCHAR2(100) NULL,
                                                RESOURCE_POOL_ID NUMBER NULL,
                                                RESOURCE_POOL_NAME VARCHAR2(64) NULL,
                                                ORDER_CODE VARCHAR2(64) NULL,
                                                RESOURCE_APPLY_TIME TIMESTAMP NULL,
                                                EXPIRE_TIME TIMESTAMP NULL,
                                                DEVICE_STATUS VARCHAR2(32) NULL,
                                                APPLY_USER_ID NUMBER NULL,
                                                APPLY_USER_NAME VARCHAR2(32) NULL,
                                                STATUS NUMBER NULL,
                                                "TYPE" VARCHAR2(32) NULL,
                                                CREATE_TIME TIMESTAMP NULL,
                                                EFFECTIVE_TIME TIMESTAMP NULL,
                                                VPC_NAME VARCHAR2(255) NULL,
                                                SUBNET_NAME VARCHAR2(255) NULL,
                                                VM_ID VARCHAR2(255) NULL,
                                                MOUNT_OR_NOT VARCHAR2(10) NULL,
                                                ECS_NAME VARCHAR2(255) NULL,
                                                INSTANCE_UUID VARCHAR2(64) NULL,
                                                NET_ID VARCHAR2(64) NULL,
                                                NET_NAME VARCHAR2(500) NULL,
                                                MAC VARCHAR2(64) NULL,
                                                CONFIG_ID VARCHAR2(64) NULL,
                                                RECOVERY_STATUS NUMBER NULL,
                                                VPC_ID VARCHAR2(36) NULL,
                                                SUBNET_ID VARCHAR2(32) NULL,
                                                DIS_DIMENSION_STATUS VARCHAR2(8) NULL,
                                                ACCESS_KEY VARCHAR2(200) NULL,
                                                SECRET_KEY VARCHAR2(200) NULL,
                                                PUBLIC_ADDRESS VARCHAR2(200) NULL,
                                                INTERNAL_ADDRESS VARCHAR2(200) NULL,
                                                VOLUME_ID VARCHAR2(100) NULL,
                                                EIP_ID VARCHAR2(100) NULL,
                                                MANAGE_IP VARCHAR2(32) NULL,
                                                SECURITY_GROUP_IDS VARCHAR2(100) NULL,
                                                ID NUMBER(19,0) NULL,
                                                RECOVERY_STATUS_CN VARCHAR(50) NULL,
                                                CAPACITY VARCHAR(50) NULL,
                                                STORE_TYPE VARCHAR(50) NULL,
                                                DOMAIN_CODE VARCHAR(50) NULL,
                                                DOMAIN_NAME VARCHAR(50) NULL,
                                                BILL_ID VARCHAR(50) NULL,
                                                RESET_PWD VARCHAR(50) NULL,
                                                DIS_DIMENSION_STATUS_CN VARCHAR(50) NULL,
                                                RESOURCE_POOL_CODE VARCHAR(50) NULL,
                                                RECOVERY_INFO VARCHAR(50) NULL,
                                                RESOURCE_NAME VARCHAR(50) NULL,
                                                RESOURCE_CODE VARCHAR(50) NULL,
                                                AZ_CODE VARCHAR(50) NULL,
                                                AZ_NAME VARCHAR(50) NULL,
                                                AZ_ID VARCHAR(50) NULL,
                                                PROJECT_NAME VARCHAR(50) NULL,
                                                NETWORK_MODEL_SNAPSHOT VARCHAR(4000) NULL,
                                                CHANGE_STATUS VARCHAR2(32) NULL,
                                                SOURCE_TYPE VARCHAR2(10) NOT NULL,
                                                MODULE_ID NUMBER NULL,
                                                MODULE_NAME VARCHAR2(60) NULL,
                                                HANDOVER_STATUS VARCHAR2(32) NULL,
                                                RELATED_DEVICE_ID VARCHAR2(64) NULL,
                                                RELATED_DEVICE_NAME VARCHAR2(255) NULL,
                                                RELATED_DEVICE_TYPE VARCHAR2(32) NULL,
                                                HIS_CHANGE_ORDER_IDS VARCHAR(2000) NULL,
                                                HIS_CHANGE_ORDER_CODES VARCHAR(4000) NULL
);


-- SLGZT.WOC_RESOURCE_ORDER_PRODUCT_REL definition

CREATE TABLE SLGZT.WOC_RESOURCE_ORDER_PRODUCT_REL (
                                                      ID NUMBER NOT NULL,
                                                      ORDER_ID NUMBER NULL,
                                                      ECS_RESOURCE_JSON CLOB NULL,
                                                      GCS_RESOURCE_JSON CLOB NULL,
                                                      EVS_RESOURCE_JSON CLOB NULL,
                                                      SLB_RESOURCE_JSON CLOB NULL,
                                                      NAT_RESOURCE_JSON CLOB NULL,
                                                      OBS_RESOURCE_JSON CLOB NULL,
                                                      CONSTRAINT INDEX33565484 PRIMARY KEY (ID)
);


-- SLGZT.WOC_SECURITY_GROUP definition

CREATE TABLE SLGZT.WOC_SECURITY_GROUP (
                                          ID NUMBER(19,0) NOT NULL,
                                          NAME VARCHAR(255) NULL,
                                          CATALOGUE_DOMAIN_CODE VARCHAR(255) NULL,
                                          DOMAIN_CODE VARCHAR(255) NULL,
                                          REGION_CODE VARCHAR(255) NULL,
                                          REGION_NAME VARCHAR(255) NULL,
                                          AZ_CODE VARCHAR(255) NULL,
                                          VPC_ID VARCHAR(255) NULL,
                                          BUSINESS_SYSTEM_ID VARCHAR(255) NULL,
                                          OPEN_STATUS VARCHAR(255) NULL,
                                          DESCRIPTION VARCHAR(255) NULL,
                                          RESOURCE_ID VARCHAR(255) NULL,
                                          CREATED_TIME DATETIME DEFAULT CURRENT_TIMESTAMP NULL,
                                          UPDATED_TIME TIMESTAMP NULL,
                                          STATUS NUMBER(10,0) DEFAULT 1 NULL,
                                          RULE_SNAPSHOT VARCHAR(4000) NULL,
                                          REGION_ID NUMBER NULL,
                                          CONSTRAINT INDEX33565749 PRIMARY KEY (ID)
);


-- SLGZT.WOC_STANDARD_WORK_ORDER definition

CREATE TABLE SLGZT.WOC_STANDARD_WORK_ORDER (
                                               ID VARCHAR2(32) NOT NULL,
                                               ORDER_TITLE VARCHAR2(255) NULL,
                                               ORDER_CODE VARCHAR2(64) NULL,
                                               ORDER_TYPE VARCHAR2(32) NULL,
                                               BILL_ID VARCHAR2(64) NULL,
                                               CUSTOM_NO VARCHAR2(64) NULL,
                                               ORDER_STATUS VARCHAR2(32) NULL,
                                               CREATED_BY NUMBER NULL,
                                               BUSI_SYSTEM_ID NUMBER NULL,
                                               CREATE_TIME DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                               MODIFY_TIME DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                               UPDATED_BY NUMBER NULL,
                                               STATUS NUMBER DEFAULT 1 NULL,
                                               CLOUD_LEADER_ID NUMBER NULL,
                                               BUSI_DEPART_LEADER_ID NUMBER NULL,
                                               LEVEL_THREE_LEADER_ID NUMBER NULL,
                                               SECOND_LEVEL_LEADER_ID NUMBER NULL,
                                               ACTIVITI_ID VARCHAR2(255) NULL,
                                               ACTIVITE_KEY VARCHAR2(128) NULL,
                                               ORDER_DESC VARCHAR2(1024) NULL,
                                               DOMAIN_CODE VARCHAR2(32) NULL,
                                               CATALOGUE_DOMAIN_CODE VARCHAR2(32) NULL,
                                               START_ROLE_CODE VARCHAR2(32) NULL,
                                               MODULE_ID NUMBER NULL,
                                               MANUFACTURER VARCHAR2(300) NULL,
                                               MANUFACTURER_CONTACTS VARCHAR2(64) NULL,
                                               MANUFACTURER_MOBILE VARCHAR2(11) NULL,
                                               RECOVERY_GOODS_IDS VARCHAR2(1024) NULL,
                                               RECOVERY_TYPE VARCHAR2(32) NULL,
                                               ECS_RESOURCE_LIST TEXT NULL,
                                               GCS_RESOURCE_LIST TEXT NULL,
                                               EVS_RESOURCE_LIST TEXT NULL,
                                               SLB_RESOURCE_LIST TEXT NULL,
                                               NAT_RESOURCE_LIST TEXT NULL,
                                               OBS_RESOURCE_LIST TEXT NULL,
                                               TENANT_ID NUMBER NULL,
                                               CURRENT_NODE_CODE VARCHAR2(50) NULL,
                                               TENANT_NAME VARCHAR2(128) NULL,
                                               BUSI_SYSTEM_NAME VARCHAR2(64) NULL,
                                               CREATED_USER_NAME VARCHAR2(64) NULL,
                                               CURRENT_NODE_NAME VARCHAR2(64) NULL,
                                               WORK_ORDER_START_TIME DATETIME DEFAULT CURRENT_TIMESTAMP NULL,
                                               WORK_ORDER_END_TIME DATETIME DEFAULT CURRENT_TIMESTAMP NULL,
                                               CURRENT_NODE_START_TIME DATETIME DEFAULT CURRENT_TIMESTAMP NULL,
                                               DOMAIN_NAME VARCHAR(50) NULL,
                                               RESOURCE_APPLY_FILE VARCHAR2(6000) NULL,
                                               BUSINESS_SYSTEM_NAME VARCHAR2(64) NULL,
                                               CATALOGUE_DOMAIN_NAME VARCHAR2(64) NULL,
                                               BUREAU_USER_NAME VARCHAR2(64) NULL,
                                               DEPARTMENT_NAME VARCHAR2(128) NULL,
                                               MODULE_NAME VARCHAR2(50) NULL,
                                               BUSINESS_DEPART_LEADER_NAME VARCHAR2(64) NULL,
                                               LEVEL_THREE_LEADER_NAME VARCHAR2(64) NULL,
                                               AUDIT_LOG_LIST TEXT NULL,
                                               EIP_RESOURCE_LIST TEXT NULL,
                                               IS_OFFLINE BIT NULL,
                                               CONSTRAINT INDEX33565534 PRIMARY KEY (ID)
);


-- SLGZT.WOC_STANDARD_WORK_ORDER_0506 definition

CREATE TABLE SLGZT.WOC_STANDARD_WORK_ORDER_0506 (
                                                    ID VARCHAR2(32) NULL,
                                                    ORDER_TITLE VARCHAR2(255) NULL,
                                                    ORDER_CODE VARCHAR2(64) NULL,
                                                    ORDER_TYPE VARCHAR2(32) NULL,
                                                    BILL_ID VARCHAR2(64) NULL,
                                                    CUSTOM_NO VARCHAR2(64) NULL,
                                                    ORDER_STATUS VARCHAR2(32) NULL,
                                                    CREATED_BY NUMBER NULL,
                                                    BUSI_SYSTEM_ID NUMBER NULL,
                                                    CREATE_TIME DATETIME NOT NULL,
                                                    MODIFY_TIME DATETIME NOT NULL,
                                                    UPDATED_BY NUMBER NULL,
                                                    STATUS NUMBER NULL,
                                                    CLOUD_LEADER_ID NUMBER NULL,
                                                    BUSI_DEPART_LEADER_ID NUMBER NULL,
                                                    LEVEL_THREE_LEADER_ID NUMBER NULL,
                                                    SECOND_LEVEL_LEADER_ID NUMBER NULL,
                                                    ACTIVITI_ID VARCHAR2(255) NULL,
                                                    ACTIVITE_KEY VARCHAR2(128) NULL,
                                                    ORDER_DESC VARCHAR2(1024) NULL,
                                                    DOMAIN_CODE VARCHAR2(32) NULL,
                                                    CATALOGUE_DOMAIN_CODE VARCHAR2(32) NULL,
                                                    START_ROLE_CODE VARCHAR2(32) NULL,
                                                    MODULE_ID NUMBER NULL,
                                                    MANUFACTURER VARCHAR2(300) NULL,
                                                    MANUFACTURER_CONTACTS VARCHAR2(64) NULL,
                                                    MANUFACTURER_MOBILE VARCHAR2(11) NULL,
                                                    RECOVERY_GOODS_IDS VARCHAR2(1024) NULL,
                                                    RECOVERY_TYPE VARCHAR2(32) NULL,
                                                    ECS_RESOURCE_LIST TEXT NULL,
                                                    GCS_RESOURCE_LIST TEXT NULL,
                                                    EVS_RESOURCE_LIST TEXT NULL,
                                                    SLB_RESOURCE_LIST TEXT NULL,
                                                    NAT_RESOURCE_LIST TEXT NULL,
                                                    OBS_RESOURCE_LIST TEXT NULL,
                                                    TENANT_ID NUMBER NULL,
                                                    CURRENT_NODE_CODE VARCHAR2(50) NULL,
                                                    TENANT_NAME VARCHAR2(128) NULL,
                                                    BUSI_SYSTEM_NAME VARCHAR2(64) NULL,
                                                    CREATED_USER_NAME VARCHAR2(64) NULL,
                                                    CURRENT_NODE_NAME VARCHAR2(64) NULL,
                                                    WORK_ORDER_START_TIME DATETIME NULL,
                                                    WORK_ORDER_END_TIME DATETIME NULL,
                                                    CURRENT_NODE_START_TIME DATETIME NULL,
                                                    DOMAIN_NAME VARCHAR(50) NULL,
                                                    RESOURCE_APPLY_FILE VARCHAR2(6000) NULL,
                                                    BUSINESS_SYSTEM_NAME VARCHAR2(64) NULL,
                                                    CATALOGUE_DOMAIN_NAME VARCHAR2(64) NULL,
                                                    BUREAU_USER_NAME VARCHAR2(64) NULL,
                                                    DEPARTMENT_NAME VARCHAR2(128) NULL,
                                                    MODULE_NAME VARCHAR2(50) NULL,
                                                    BUSINESS_DEPART_LEADER_NAME VARCHAR2(64) NULL,
                                                    LEVEL_THREE_LEADER_NAME VARCHAR2(64) NULL,
                                                    AUDIT_LOG_LIST TEXT NULL,
                                                    EIP_RESOURCE_LIST TEXT NULL,
                                                    IS_OFFLINE BIT NULL
);


-- SLGZT.WOC_STANDARD_WORK_ORDER_BAK definition

CREATE TABLE SLGZT.WOC_STANDARD_WORK_ORDER_BAK (
                                                   ID VARCHAR2(32) NOT NULL,
                                                   ORDER_TITLE VARCHAR2(255) NULL,
                                                   ORDER_CODE VARCHAR2(64) NULL,
                                                   ORDER_TYPE VARCHAR2(32) NULL,
                                                   BILL_ID VARCHAR2(64) NULL,
                                                   CUSTOM_NO VARCHAR2(64) NULL,
                                                   ORDER_STATUS VARCHAR2(32) NULL,
                                                   CREATED_BY NUMBER NULL,
                                                   BUSI_SYSTEM_ID NUMBER NULL,
                                                   CREATE_TIME DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                                   MODIFY_TIME DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                                   UPDATED_BY NUMBER NULL,
                                                   STATUS NUMBER DEFAULT 1 NULL,
                                                   CLOUD_LEADER_ID NUMBER NULL,
                                                   BUSI_DEPART_LEADER_ID NUMBER NULL,
                                                   LEVEL_THREE_LEADER_ID NUMBER NULL,
                                                   SECOND_LEVEL_LEADER_ID NUMBER NULL,
                                                   ACTIVITI_ID VARCHAR2(255) NULL,
                                                   ACTIVITE_KEY VARCHAR2(128) NULL,
                                                   ORDER_DESC VARCHAR2(1024) NULL,
                                                   DOMAIN_CODE VARCHAR2(32) NULL,
                                                   CATALOGUE_DOMAIN_CODE VARCHAR2(32) NULL,
                                                   START_ROLE_CODE VARCHAR2(32) NULL,
                                                   MODULE_ID NUMBER NULL,
                                                   MANUFACTURER VARCHAR2(300) NULL,
                                                   MANUFACTURER_CONTACTS VARCHAR2(64) NULL,
                                                   MANUFACTURER_MOBILE VARCHAR2(11) NULL,
                                                   RECOVERY_GOODS_IDS VARCHAR2(1024) NULL,
                                                   RECOVERY_TYPE VARCHAR2(32) NULL,
                                                   ECS_RESOURCE_LIST VARCHAR2(15000) NULL,
                                                   GCS_RESOURCE_LIST VARCHAR2(15000) NULL,
                                                   EVS_RESOURCE_LIST VARCHAR2(15000) NULL,
                                                   SLB_RESOURCE_LIST VARCHAR2(15000) NULL,
                                                   NAT_RESOURCE_LIST VARCHAR2(15000) NULL,
                                                   OBS_RESOURCE_LIST VARCHAR2(15000) NULL,
                                                   TENANT_ID NUMBER NULL,
                                                   CURRENT_NODE_CODE VARCHAR2(50) NULL,
                                                   TENANT_NAME VARCHAR2(128) NULL,
                                                   BUSI_SYSTEM_NAME VARCHAR2(64) NULL,
                                                   CREATED_USER_NAME VARCHAR2(64) NULL,
                                                   CURRENT_NODE_NAME VARCHAR2(64) NULL,
                                                   WORK_ORDER_START_TIME DATETIME DEFAULT CURRENT_TIMESTAMP NULL,
                                                   WORK_ORDER_END_TIME DATETIME DEFAULT CURRENT_TIMESTAMP NULL,
                                                   CURRENT_NODE_START_TIME DATETIME DEFAULT CURRENT_TIMESTAMP NULL,
                                                   DOMAIN_NAME VARCHAR(50) NULL,
                                                   RESOURCE_APPLY_FILE VARCHAR2(6000) NULL,
                                                   BUSINESS_SYSTEM_NAME VARCHAR2(64) NULL,
                                                   CATALOGUE_DOMAIN_NAME VARCHAR2(64) NULL,
                                                   BUREAU_USER_NAME VARCHAR2(64) NULL,
                                                   DEPARTMENT_NAME VARCHAR2(128) NULL,
                                                   MODULE_NAME VARCHAR2(50) NULL,
                                                   BUSINESS_DEPART_LEADER_NAME VARCHAR2(64) NULL,
                                                   LEVEL_THREE_LEADER_NAME VARCHAR2(64) NULL,
                                                   CONSTRAINT INDEX33565530 PRIMARY KEY (ID)
);


-- SLGZT.WOC_STANDARD_WORK_ORDER_PRODUCT definition

CREATE TABLE SLGZT.WOC_STANDARD_WORK_ORDER_PRODUCT (
                                                       ID NUMBER(19,0) NOT NULL,
                                                       WORK_ORDER_ID VARCHAR(32) NOT NULL,
                                                       PRODUCT_TYPE VARCHAR(50) NOT NULL,
                                                       PROPERTY_SNAPSHOT TEXT NULL,
                                                       PARENT_PRODUCT_ID NUMBER(19,0) DEFAULT 0 NOT NULL,
                                                       OPEN_STATUS VARCHAR(50) NOT NULL,
                                                       MESSAGE VARCHAR(500) NULL,
                                                       ENABLED BIT DEFAULT 1 NOT NULL,
                                                       CREATE_TIME DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                                       MODIFY_TIME DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                                       GID VARCHAR2(30) NULL,
                                                       EXT VARCHAR(500) NULL,
                                                       SUB_ORDER_ID VARCHAR(50) NULL,
                                                       CONSTRAINT INDEX33565500 PRIMARY KEY (ID)
);


-- SLGZT.WOC_STATIC_PRODUCT_STOCK definition

CREATE TABLE SLGZT.WOC_STATIC_PRODUCT_STOCK (
                                                ID NUMBER(19,0) NOT NULL,
                                                PRODUCT_TYPE VARCHAR2(64) NULL,
                                                REMAINING_AMOUNT NUMBER(10,0) NULL,
                                                REGION_CODE VARCHAR2(64) NULL,
                                                REGION_NAME VARCHAR2(128) NULL,
                                                REGION_ID VARCHAR2(64) NULL,
                                                AZ_ID VARCHAR2(64) NULL,
                                                AZ_NAME VARCHAR2(128) NULL,
                                                AZ_CODE VARCHAR2(64) NULL,
                                                CONSTRAINT INDEX33565753 PRIMARY KEY (ID)
);


-- SLGZT.WOC_SYSTEM_MAINTENANCE_LOG definition

CREATE TABLE SLGZT.WOC_SYSTEM_MAINTENANCE_LOG (
                                                  ID VARCHAR2(36) NOT NULL,
                                                  MAINTENANCE_CONTENT VARCHAR2(2000) NOT NULL,
                                                  MAINTENANCE_TYPE VARCHAR2(20) NOT NULL,
                                                  VERSION VARCHAR2(50) NULL,
                                                  ENABLED NUMBER(1,0) DEFAULT 1 NULL,
                                                  CREATE_TIME TIMESTAMP NULL,
                                                  MODIFY_TIME TIMESTAMP NULL,
                                                  PUBLISHER VARCHAR2(100) NULL,
                                                  CONSTRAINT INDEX33565719 PRIMARY KEY (ID)
);


-- SLGZT.WOC_VNIC definition

CREATE TABLE SLGZT.WOC_VNIC (
                                ID VARCHAR2(64) NOT NULL,
                                VNIC_ID VARCHAR2(64) NULL,
                                VNIC_NAME VARCHAR2(255) NULL,
                                BUSINESS_SYSTEM_NAME VARCHAR2(255) NULL,
                                BUSINESS_SYSTEM_ID VARCHAR2(64) NULL,
                                CATALOGUE_DOMAIN_NAME VARCHAR2(255) NULL,
                                CATALOGUE_DOMAIN_CODE VARCHAR2(64) NULL,
                                DOMAIN_NAME VARCHAR2(255) NULL,
                                DOMAIN_CODE VARCHAR2(64) NULL,
                                REGION_NAME VARCHAR2(255) NULL,
                                REGION_ID VARCHAR2(64) NULL,
                                AZ_NAME VARCHAR2(255) NULL,
                                AZ_ID VARCHAR2(64) NULL,
                                VPC_NAME VARCHAR2(255) NULL,
                                VPC_ID VARCHAR2(64) NULL,
                                SUBNET_NAME VARCHAR2(255) NULL,
                                SUBNET_ID VARCHAR2(64) NULL,
                                IP_ADDRESS VARCHAR2(64) NULL,
                                VM_NAME VARCHAR2(255) NULL,
                                VM_ID VARCHAR2(64) NULL,
                                ENABLED NUMBER(1,0) DEFAULT 1 NULL,
                                CREATE_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL,
                                MODIFY_TIME TIMESTAMP NULL,
                                REGION_CODE VARCHAR2(64) NULL,
                                AZ_CODE VARCHAR2(64) NULL,
                                CONSTRAINT INDEX33565777 PRIMARY KEY (ID)
);


-- SLGZT.WOC_VPC_ORDER definition

CREATE TABLE SLGZT.WOC_VPC_ORDER (
                                     ID VARCHAR2(32) NOT NULL,
                                     VPC_NAME VARCHAR2(64) NOT NULL,
                                     ORDER_ID VARCHAR2(60) NOT NULL,
                                     SUBNET_NUM NUMBER NOT NULL,
                                     TENANT_ID NUMBER NULL,
                                     ACCOUNT VARCHAR2(50) NULL,
                                     REGION_CODE VARCHAR2(90) NULL,
                                     IPV4_CIDR VARCHAR2(32) NULL,
                                     IPV6_CIDR VARCHAR2(32) NULL,
                                     PLANE VARCHAR2(16) NULL,
                                     CREATED_TIME TIMESTAMP NULL,
                                     UPDATED_TIME TIMESTAMP NULL,
                                     CREATED_BY NUMBER NOT NULL,
                                     UPDATED_BY NUMBER NULL,
                                     DELETED NUMBER DEFAULT 1 NOT NULL,
                                     GID VARCHAR2(50) NULL,
                                     STATUS VARCHAR2(60) NULL,
                                     SUBNET_RECOVERY_STATUS NUMBER NULL,
                                     MESSAGE VARCHAR2(2000) NULL,
                                     RECOVERY_TIME TIMESTAMP NULL,
                                     DESCRIPTION VARCHAR2(512) NULL,
                                     DETAIL VARCHAR2(5000) NULL,
                                     RECOVERY_STATUS NUMBER DEFAULT 0 NOT NULL,
                                     CONFIRM_OR_NOT NUMBER NULL,
                                     AZ_CODE VARCHAR2(60) NULL,
                                     FUNCTIONAL_MODULE VARCHAR2(50) NULL,
                                     TENANT_NAME VARCHAR2(60) NULL,
                                     BUSINESS_SYS_ID NUMBER NULL,
                                     BUSINESS_SYS_NAME VARCHAR2(60) NULL,
                                     APPLY_USER_ID NUMBER NULL,
                                     APPLY_USER_NAME VARCHAR2(60) NULL,
                                     SOURCE_TYPE VARCHAR2(10) DEFAULT 'BZ' NOT NULL,
                                     MODULE_ID NUMBER NULL,
                                     MODULE_NAME VARCHAR2(60) NULL,
                                     ORDER_CODE VARCHAR2(64) NULL,
                                     DOMAIN_CODE VARCHAR2(64) NULL,
                                     DOMAIN_NAME VARCHAR2(64) NULL,
                                     CATALOGUE_DOMAIN_CODE VARCHAR2(64) NULL,
                                     CATALOGUE_DOMAIN_NAME VARCHAR2(64) NULL,
                                     VPC_TYPE NUMBER NULL,
                                     CONSTRAINT INDEX33565486 PRIMARY KEY (ID)
);


-- SLGZT.WOC_VPC_SUBNET_ORDER definition

CREATE TABLE SLGZT.WOC_VPC_SUBNET_ORDER (
                                            ID VARCHAR2(36) NOT NULL,
                                            SUBNET_NAME VARCHAR2(250) NOT NULL,
                                            VPC_ID VARCHAR2(36) NOT NULL,
                                            AZ_CODE VARCHAR2(60) NULL,
                                            DNS_NAMES VARCHAR2(512) NULL,
                                            CIDR VARCHAR2(50) NULL,
                                            START_IP VARCHAR2(64) NULL,
                                            END_IP VARCHAR2(64) NULL,
                                            NETMASK VARCHAR2(50) NULL,
                                            CREATED_TIME TIMESTAMP NULL,
                                            UPDATED_TIME TIMESTAMP NULL,
                                            DELETED NUMBER DEFAULT 1 NOT NULL,
                                            MESSAGE VARCHAR2(2000) NULL,
                                            DESCRIPTION VARCHAR2(512) NULL,
                                            RECOVERY_STATUS NUMBER DEFAULT 0 NOT NULL,
                                            LEVEL2_INSTANCE_ID VARCHAR2(60) NULL,
                                            INSTANCE_ID VARCHAR2(60) NULL,
                                            STATUS VARCHAR2(20) NULL,
                                            UUID VARCHAR2(64) NULL,
                                            CONSTRAINT INDEX33565488 PRIMARY KEY (ID)
);


-- SLGZT.WOC_VPC_SUBNET_ORDER_0506 definition

CREATE TABLE SLGZT.WOC_VPC_SUBNET_ORDER_0506 (
                                                 ID VARCHAR2(36) NULL,
                                                 SUBNET_NAME VARCHAR2(250) NOT NULL,
                                                 VPC_ID VARCHAR2(36) NOT NULL,
                                                 AZ_CODE VARCHAR2(60) NULL,
                                                 DNS_NAMES VARCHAR2(512) NULL,
                                                 CIDR VARCHAR2(50) NULL,
                                                 START_IP VARCHAR2(64) NULL,
                                                 END_IP VARCHAR2(64) NULL,
                                                 NETMASK VARCHAR2(50) NULL,
                                                 CREATED_TIME TIMESTAMP NULL,
                                                 UPDATED_TIME TIMESTAMP NULL,
                                                 DELETED NUMBER NOT NULL,
                                                 MESSAGE VARCHAR2(2000) NULL,
                                                 DESCRIPTION VARCHAR2(512) NULL,
                                                 RECOVERY_STATUS NUMBER NOT NULL,
                                                 LEVEL2_INSTANCE_ID VARCHAR2(60) NULL,
                                                 INSTANCE_ID VARCHAR2(60) NULL,
                                                 STATUS VARCHAR2(20) NULL,
                                                 UUID VARCHAR2(64) NULL
);


-- SLGZT.WOC_WORK_ORDER_AUTH_LOG definition

CREATE TABLE SLGZT.WOC_WORK_ORDER_AUTH_LOG (
                                               ID NUMBER(19,0) NOT NULL,
                                               WORK_ORDER_ID VARCHAR2(50) NOT NULL,
                                               PROCESS_INSTANCE_ID VARCHAR(50) NULL,
                                               AUDIT_NODE_CODE VARCHAR(50) NULL,
                                               AUDIT_NODE_NAME VARCHAR(50) NULL,
                                               AUDIT_RESULT VARCHAR(50) NULL,
                                               ADVICE VARCHAR(1024) NULL,
                                               FILE_IDS VARCHAR(500) NULL,
                                               CREATE_TIME DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                               MODIFY_TIME DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                               ENABLED BIT DEFAULT 1 NOT NULL,
                                               USER_ID VARCHAR(50) NOT NULL,
                                               WORK_ORDER_TYPE VARCHAR(50) NULL,
                                               USER_NAME VARCHAR(50) NULL,
                                               USER_PHONE VARCHAR(50) NULL,
                                               USER_EMAIL VARCHAR(50) NULL,
                                               AUDIT_RESULT_DESC VARCHAR(50) NULL,
                                               CONSTRAINT INDEX33565347 PRIMARY KEY (ID)
);


-- SLGZT.XIE_YUN_CLUSTER definition

CREATE TABLE SLGZT.XIE_YUN_CLUSTER (
                                       ID VARCHAR2(90) NOT NULL,
                                       CLUSTER_NAME VARCHAR2(128) NULL,
                                       CREATED_TIME TIMESTAMP DEFAULT SYSDATE NULL,
                                       UPDATED_TIME TIMESTAMP DEFAULT SYSDATE NULL,
                                       DELETED NUMBER(1,0) DEFAULT 0 NULL,
                                       CONSTRAINT INDEX33565642 PRIMARY KEY (ID)
);


-- SLGZT.XIE_YUN_NAMESPACE definition

CREATE TABLE SLGZT.XIE_YUN_NAMESPACE (
                                         ID VARCHAR2(90) NOT NULL,
                                         XIE_YUN_NAMESPACE_ID VARCHAR2(128) NULL,
                                         XIE_YUN_PROJECT_ID VARCHAR2(128) NULL,
                                         XIE_YUN_ORG_ID VARCHAR2(128) NULL,
                                         NAMESPACE_NAME VARCHAR2(128) NULL,
                                         CLUSTER_NAME VARCHAR2(128) NULL,
                                         NODE_POOL_NAME VARCHAR2(128) NULL,
                                         CPU_VALUE VARCHAR2(128) NULL,
                                         MEMORY_VALUE VARCHAR2(128) NULL,
                                         NAMESPACE_DESC VARCHAR2(1024) NULL,
                                         XIE_YUN_NAMESPACE_CREATE_OPM_LIST TEXT NULL,
                                         CREATED_TIME TIMESTAMP DEFAULT SYSDATE NULL,
                                         UPDATED_TIME TIMESTAMP DEFAULT SYSDATE NULL,
                                         DELETED NUMBER(1,0) DEFAULT 0 NULL,
                                         CONSTRAINT INDEX33565646 PRIMARY KEY (ID)
);


-- SLGZT.XIE_YUN_ORG definition

CREATE TABLE SLGZT.XIE_YUN_ORG (
                                   ID VARCHAR2(90) NOT NULL,
                                   XIE_YUN_ORG_ID VARCHAR2(128) NULL,
                                   CODE VARCHAR2(128) NULL,
                                   NAME VARCHAR2(128) NULL,
                                   XIE_YUN_USER_ID VARCHAR2(90) NULL,
                                   XIE_YUN_REPO_ID VARCHAR2(90) NULL,
                                   XIE_YUN_REGISTRY_ID VARCHAR2(90) NULL,
                                   NODE_POOL_NAME VARCHAR2(128) NULL,
                                   CLUSTER_NAME VARCHAR2(128) NULL,
                                   CPU_VALUE VARCHAR2(128) NULL,
                                   MEMORY_VALUE VARCHAR2(128) NULL,
                                   DESCRIPTION VARCHAR2(512) NULL,
                                   XIE_YUN_ORG_QUOTA_OPM_LIST TEXT NULL,
                                   CREATED_TIME TIMESTAMP DEFAULT SYSDATE NULL,
                                   UPDATED_TIME TIMESTAMP DEFAULT SYSDATE NULL,
                                   DELETED NUMBER(1,0) DEFAULT 0 NULL,
                                   CONSTRAINT INDEX33565638 PRIMARY KEY (ID)
);


-- SLGZT.XIE_YUN_PROJECT definition

CREATE TABLE SLGZT.XIE_YUN_PROJECT (
                                       ID VARCHAR2(90) NOT NULL,
                                       XIE_YUN_PROJECT_ID VARCHAR2(128) NULL,
                                       XIE_YUN_ORG_ID VARCHAR2(128) NULL,
                                       CLUSTER_NAME VARCHAR2(128) NULL,
                                       NODE_POOL_NAME VARCHAR2(128) NULL,
                                       CPU_VALUE VARCHAR2(128) NULL,
                                       MEMORY_VALUE VARCHAR2(128) NULL,
                                       XIE_YUN_PROJECT_QUOTA_OPM_LIST TEXT NULL,
                                       CREATED_TIME TIMESTAMP DEFAULT SYSDATE NULL,
                                       UPDATED_TIME TIMESTAMP DEFAULT SYSDATE NULL,
                                       DELETED NUMBER(1,0) DEFAULT 0 NULL,
                                       CONSTRAINT INDEX33565640 PRIMARY KEY (ID)
);


-- SLGZT.XIE_YUN_REPOSITORY definition

CREATE TABLE SLGZT.XIE_YUN_REPOSITORY (
                                          ID VARCHAR2(90) NOT NULL,
                                          XIE_YUN_REPO_ID VARCHAR2(128) NULL,
                                          XIE_YUN_REGISTRY_ID VARCHAR2(128) NULL,
                                          REPO_NAME VARCHAR2(128) NULL,
                                          CREATED_TIME TIMESTAMP DEFAULT SYSDATE NULL,
                                          UPDATED_TIME TIMESTAMP DEFAULT SYSDATE NULL,
                                          DELETED NUMBER(1,0) DEFAULT 0 NULL,
                                          CONSTRAINT INDEX33565644 PRIMARY KEY (ID)
);


-- SLGZT.XIE_YUN_USER definition

CREATE TABLE SLGZT.XIE_YUN_USER (
                                    ID VARCHAR2(90) NOT NULL,
                                    XIE_YUN_USER_ID VARCHAR2(128) NULL,
                                    USERNAME VARCHAR2(128) NULL,
                                    NAME VARCHAR2(128) NULL,
                                    MOBILE VARCHAR2(16) NULL,
                                    EMAIL VARCHAR2(64) NULL,
                                    CREATED_TIME TIMESTAMP DEFAULT SYSDATE NULL,
                                    UPDATED_TIME TIMESTAMP DEFAULT SYSDATE NULL,
                                    DELETED NUMBER(1,0) DEFAULT 0 NULL,
                                    CONSTRAINT INDEX33565636 PRIMARY KEY (ID)
);


-- SLGZT.XXL_JOB_GROUP definition

CREATE TABLE SLGZT.XXL_JOB_GROUP (
                                     ID NUMBER(11,0) NOT NULL,
                                     APP_NAME VARCHAR2(64) NULL,
                                     TITLE VARCHAR2(255) NULL,
                                     ADDRESS_TYPE NUMBER(4,0) NULL,
                                     ADDRESS_LIST VARCHAR2(4000) NULL,
                                     CONSTRAINT INDEX33564143 PRIMARY KEY (ID)
);


-- SLGZT.XXL_JOB_INFO definition

CREATE TABLE SLGZT.XXL_JOB_INFO (
                                    ID NUMBER(11,0) NOT NULL,
                                    JOB_GROUP NUMBER(11,0) NULL,
                                    JOB_CRON VARCHAR2(128) NULL,
                                    JOB_DESC VARCHAR2(255) NULL,
                                    ADD_TIME TIMESTAMP NULL,
                                    UPDATE_TIME TIMESTAMP NULL,
                                    AUTHOR VARCHAR2(64) NULL,
                                    ALARM_EMAIL VARCHAR2(255) NULL,
                                    EXECUTOR_ROUTE_STRATEGY VARCHAR2(50) NULL,
                                    EXECUTOR_HANDLER VARCHAR2(255) NULL,
                                    EXECUTOR_PARAM VARCHAR2(512) NULL,
                                    EXECUTOR_BLOCK_STRATEGY VARCHAR2(50) NULL,
                                    EXECUTOR_TIMEOUT NUMBER(11,0) NULL,
                                    EXECUTOR_FAIL_RETRY_COUNT NUMBER(11,0) NULL,
                                    GLUE_TYPE VARCHAR2(50) NULL,
                                    GLUE_SOURCE CLOB NULL,
                                    GLUE_REMARK VARCHAR2(128) NULL,
                                    GLUE_UPDATETIME TIMESTAMP NULL,
                                    CHILD_JOBID VARCHAR2(255) NULL,
                                    TRIGGER_STATUS NUMBER(4,0) NULL,
                                    TRIGGER_LAST_TIME NUMBER(20,0) NULL,
                                    TRIGGER_NEXT_TIME NUMBER(20,0) NULL,
                                    CONSTRAINT INDEX33564144 PRIMARY KEY (ID)
);


-- SLGZT.XXL_JOB_LOCK definition

CREATE TABLE SLGZT.XXL_JOB_LOCK (
                                    LOCK_NAME VARCHAR2(50) NOT NULL,
                                    CONSTRAINT INDEX33564145 PRIMARY KEY (LOCK_NAME)
);


-- SLGZT.XXL_JOB_LOG definition

CREATE TABLE SLGZT.XXL_JOB_LOG (
                                   ID NUMBER(20,0) NOT NULL,
                                   JOB_GROUP NUMBER(11,0) NULL,
                                   JOB_ID NUMBER(11,0) NULL,
                                   EXECUTOR_ADDRESS VARCHAR2(255) NULL,
                                   EXECUTOR_HANDLER VARCHAR2(255) NULL,
                                   EXECUTOR_PARAM VARCHAR2(512) NULL,
                                   EXECUTOR_SHARDING_PARAM VARCHAR2(20) NULL,
                                   EXECUTOR_FAIL_RETRY_COUNT NUMBER(11,0) DEFAULT 0 NULL,
                                   TRIGGER_TIME TIMESTAMP NULL,
                                   TRIGGER_CODE NUMBER(11,0) NULL,
                                   TRIGGER_MSG CLOB NULL,
                                   HANDLE_TIME TIMESTAMP NULL,
                                   HANDLE_CODE NUMBER(11,0) NULL,
                                   HANDLE_MSG CLOB NULL,
                                   ALARM_STATUS NUMBER(4,0) DEFAULT 0 NULL,
                                   CONSTRAINT INDEX33564146 PRIMARY KEY (ID)
);
CREATE INDEX D_IDX_ID_ALARM_HANDLE_TRIGGER ON SLGZT.XXL_JOB_LOG (ID,ALARM_STATUS,HANDLE_CODE,TRIGGER_CODE);
CREATE INDEX D_INDX_TR_HA_TR ON SLGZT.XXL_JOB_LOG (TRIGGER_TIME,HANDLE_CODE,TRIGGER_CODE,EXECUTOR_ADDRESS,ID);
CREATE INDEX D_INDX_TTIME_TC_HC ON SLGZT.XXL_JOB_LOG (TRIGGER_TIME,TRIGGER_CODE,HANDLE_CODE);
CREATE INDEX D_I_HANDLE_CODE ON SLGZT.XXL_JOB_LOG (HANDLE_CODE);
CREATE INDEX D_I_TRIGGER_TIME ON SLGZT.XXL_JOB_LOG (TRIGGER_TIME);


-- SLGZT.XXL_JOB_LOG_REPORT definition

CREATE TABLE SLGZT.XXL_JOB_LOG_REPORT (
                                          ID NUMBER(11,0) NOT NULL,
                                          TRIGGER_DAY TIMESTAMP NULL,
                                          RUNNING_COUNT NUMBER(11,0) NULL,
                                          SUC_COUNT NUMBER(11,0) NULL,
                                          FAIL_COUNT NUMBER(11,0) NULL,
                                          CONSTRAINT INDEX33564148 PRIMARY KEY (ID)
);
CREATE INDEX D_I_TRIGGER_DAY ON SLGZT.XXL_JOB_LOG_REPORT (TRIGGER_DAY);


-- SLGZT.XXL_JOB_LOGGLUE definition

CREATE TABLE SLGZT.XXL_JOB_LOGGLUE (
                                       ID NUMBER(11,0) NOT NULL,
                                       JOB_ID NUMBER(11,0) NULL,
                                       GLUE_TYPE VARCHAR2(50) NULL,
                                       GLUE_SOURCE CLOB NULL,
                                       GLUE_REMARK VARCHAR2(128) NULL,
                                       ADD_TIME TIMESTAMP NULL,
                                       UPDATE_TIME TIMESTAMP NULL,
                                       CONSTRAINT INDEX33564147 PRIMARY KEY (ID)
);


-- SLGZT.XXL_JOB_REGISTRY definition

CREATE TABLE SLGZT.XXL_JOB_REGISTRY (
                                        ID NUMBER(11,0) NOT NULL,
                                        REGISTRY_GROUP VARCHAR2(50) NULL,
                                        REGISTRY_KEY VARCHAR2(255) NULL,
                                        REGISTRY_VALUE VARCHAR2(255) NULL,
                                        UPDATE_TIME TIMESTAMP NULL,
                                        CONSTRAINT INDEX33564149 PRIMARY KEY (ID)
);


-- SLGZT.XXL_JOB_USER definition

CREATE TABLE SLGZT.XXL_JOB_USER (
                                    ID NUMBER(11,0) NOT NULL,
                                    USERNAME VARCHAR2(50) NULL,
                                    PASSWORD VARCHAR2(50) NULL,
                                    "ROLE" NUMBER(4,0) NULL,
                                    PERMISSION VARCHAR2(255) NULL,
                                    CONSTRAINT INDEX33564150 PRIMARY KEY (ID)
);
CREATE INDEX D_I_USERNAME ON SLGZT.XXL_JOB_USER (USERNAME);


-- SLGZT.WOC_CONTAINER_QUOTA definition

CREATE TABLE SLGZT.WOC_CONTAINER_QUOTA (
                                           ID VARCHAR2(64) NOT NULL,
                                           WORK_ORDER_ID VARCHAR2(32) NULL,
                                           SUB_ORDER_ID VARCHAR2(50) NULL,
                                           BUSINESS_SYSTEM_ID NUMBER NULL,
                                           BUSINESS_SYSTEM_NAME VARCHAR2(128) NULL,
                                           CQ_NAME VARCHAR2(255) NULL,
                                           V_CPUS NUMBER(10,0) NULL,
                                           RAM NUMBER(10,0) NULL,
                                           GPU_RATIO NUMBER(10,0) NULL,
                                           GPU_VIRTUAL_MEMORY NUMBER(10,0) NULL,
                                           GPU_CORE NUMBER(10,0) NULL,
                                           GPU_VIRTUAL_CORE NUMBER(10,0) NULL,
                                           A4_ACCOUNT VARCHAR2(128) NULL,
                                           A4_PHONE VARCHAR2(32) NULL,
                                           APPLY_TIME VARCHAR2(64) NULL,
                                           OPEN_NUM NUMBER(10,0) NULL,
                                           CATALOGUE_DOMAIN_CODE VARCHAR2(64) NULL,
                                           CATALOGUE_DOMAIN_NAME VARCHAR2(128) NULL,
                                           DOMAIN_CODE VARCHAR2(64) NULL,
                                           DOMAIN_NAME VARCHAR2(128) NULL,
                                           REGION_ID NUMBER(19,0) NULL,
                                           REGION_CODE VARCHAR2(64) NULL,
                                           REGION_NAME VARCHAR2(128) NULL,
                                           AZ_ID NUMBER(19,0) NULL,
                                           AZ_CODE VARCHAR2(64) NULL,
                                           AZ_NAME VARCHAR2(128) NULL,
                                           PRODUCT_TYPE VARCHAR2(64) NULL,
                                           FUNCTIONAL_MODULE VARCHAR2(64) NULL,
                                           STATUS VARCHAR2(32) NULL,
                                           MESSAGE VARCHAR2(1000) NULL,
                                           ORIGIN_NAME VARCHAR2(255) NULL,
                                           ARCHIVED_IP VARCHAR2(64) NULL,
                                           ENABLED NUMBER(1,0) DEFAULT 1 NULL,
                                           CREATE_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL,
                                           MODIFY_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL,
                                           CONSTRAINT INDEX33565800 PRIMARY KEY (ID)
);
CREATE INDEX IDX_WOC_CQ_WORK_ORDER_ID ON SLGZT.WOC_CONTAINER_QUOTA (WORK_ORDER_ID);
CREATE INDEX IDX_WOC_CQ_BUSINESS_SYSTEM_ID ON SLGZT.WOC_CONTAINER_QUOTA (BUSINESS_SYSTEM_ID);
CREATE INDEX IDX_WOC_CQ_CQ_NAME ON SLGZT.WOC_CONTAINER_QUOTA (CQ_NAME);


-- SLGZT.ACT_GE_BYTEARRAY definition

CREATE TABLE SLGZT.ACT_GE_BYTEARRAY (
                                        ID_ NVARCHAR2(256) NOT NULL,
                                        REV_ NUMBER(10,0) NULL,
                                        NAME_ NVARCHAR2(1020) NULL,
                                        DEPLOYMENT_ID_ NVARCHAR2(256) NULL,
                                        BYTES_ BLOB NULL,
                                        GENERATED_ NUMBER(1,0) NULL,
                                        CONSTRAINT INDEX33564623 PRIMARY KEY (ID_),
                                        CONSTRAINT ACT_FK_BYTEARR_DEPL FOREIGN KEY (DEPLOYMENT_ID_) REFERENCES SLGZT.ACT_RE_DEPLOYMENT(ID_)
);
CREATE INDEX ACT_IDX_BYTEAR_DEPL ON SLGZT.ACT_GE_BYTEARRAY (DEPLOYMENT_ID_);
CREATE INDEX INDEX33564660 ON SLGZT.ACT_GE_BYTEARRAY (DEPLOYMENT_ID_);


-- SLGZT.ACT_ID_MEMBERSHIP definition

CREATE TABLE SLGZT.ACT_ID_MEMBERSHIP (
                                         USER_ID_ NVARCHAR2(256) NOT NULL,
                                         GROUP_ID_ NVARCHAR2(256) NOT NULL,
                                         CONSTRAINT INDEX33564789 PRIMARY KEY (GROUP_ID_,USER_ID_),
                                         CONSTRAINT ACT_FK_MEMB_GROUP FOREIGN KEY (GROUP_ID_) REFERENCES SLGZT.ACT_ID_GROUP(ID_),
                                         CONSTRAINT ACT_FK_MEMB_USER FOREIGN KEY (USER_ID_) REFERENCES SLGZT.ACT_ID_USER(ID_)
);
CREATE INDEX ACT_IDX_MEMB_GROUP ON SLGZT.ACT_ID_MEMBERSHIP (GROUP_ID_);
CREATE INDEX ACT_IDX_MEMB_USER ON SLGZT.ACT_ID_MEMBERSHIP (USER_ID_);
CREATE INDEX INDEX33564795 ON SLGZT.ACT_ID_MEMBERSHIP (GROUP_ID_);
CREATE INDEX INDEX33564797 ON SLGZT.ACT_ID_MEMBERSHIP (USER_ID_);


-- SLGZT.ACT_PROCDEF_INFO definition

CREATE TABLE SLGZT.ACT_PROCDEF_INFO (
                                        ID_ NVARCHAR2(256) NOT NULL,
                                        PROC_DEF_ID_ NVARCHAR2(256) NOT NULL,
                                        REV_ NUMBER(10,0) NULL,
                                        INFO_JSON_ID_ NVARCHAR2(256) NULL,
                                        CONSTRAINT INDEX33564651 PRIMARY KEY (ID_),
                                        CONSTRAINT ACT_FK_INFO_JSON_BA FOREIGN KEY (INFO_JSON_ID_) REFERENCES SLGZT.ACT_GE_BYTEARRAY(ID_),
                                        CONSTRAINT ACT_FK_INFO_PROCDEF FOREIGN KEY (PROC_DEF_ID_) REFERENCES SLGZT.ACT_RE_PROCDEF(ID_)
);
CREATE INDEX ACT_IDX_PROCDEF_INFO_JSON ON SLGZT.ACT_PROCDEF_INFO (INFO_JSON_ID_);
CREATE INDEX ACT_IDX_PROCDEF_INFO_PROC ON SLGZT.ACT_PROCDEF_INFO (PROC_DEF_ID_);
CREATE INDEX INDEX33564747 ON SLGZT.ACT_PROCDEF_INFO (INFO_JSON_ID_);
CREATE INDEX INDEX33564749 ON SLGZT.ACT_PROCDEF_INFO (PROC_DEF_ID_);
CREATE UNIQUE INDEX INDEX33564750 ON SLGZT.ACT_PROCDEF_INFO (PROC_DEF_ID_);


-- SLGZT.ACT_RE_MODEL definition

CREATE TABLE SLGZT.ACT_RE_MODEL (
                                    ID_ NVARCHAR2(256) NOT NULL,
                                    REV_ NUMBER(10,0) NULL,
                                    NAME_ NVARCHAR2(1020) NULL,
                                    KEY_ NVARCHAR2(1020) NULL,
                                    CATEGORY_ NVARCHAR2(1020) NULL,
                                    CREATE_TIME_ TIMESTAMP NULL,
                                    LAST_UPDATE_TIME_ TIMESTAMP NULL,
                                    VERSION_ NUMBER(10,0) NULL,
                                    META_INFO_ NVARCHAR2(8000) NULL,
                                    DEPLOYMENT_ID_ NVARCHAR2(256) NULL,
                                    EDITOR_SOURCE_VALUE_ID_ NVARCHAR2(256) NULL,
                                    EDITOR_SOURCE_EXTRA_VALUE_ID_ NVARCHAR2(256) NULL,
                                    TENANT_ID_ NVARCHAR2(1020) DEFAULT '' NULL,
                                    CONSTRAINT INDEX33564627 PRIMARY KEY (ID_),
                                    CONSTRAINT ACT_FK_MODEL_DEPLOYMENT FOREIGN KEY (DEPLOYMENT_ID_) REFERENCES SLGZT.ACT_RE_DEPLOYMENT(ID_),
                                    CONSTRAINT ACT_FK_MODEL_SOURCE FOREIGN KEY (EDITOR_SOURCE_VALUE_ID_) REFERENCES SLGZT.ACT_GE_BYTEARRAY(ID_),
                                    CONSTRAINT ACT_FK_MODEL_SOURCE_EXTRA FOREIGN KEY (EDITOR_SOURCE_EXTRA_VALUE_ID_) REFERENCES SLGZT.ACT_GE_BYTEARRAY(ID_)
);
CREATE INDEX ACT_IDX_MODEL_DEPLOYMENT ON SLGZT.ACT_RE_MODEL (DEPLOYMENT_ID_);
CREATE INDEX ACT_IDX_MODEL_SOURCE ON SLGZT.ACT_RE_MODEL (EDITOR_SOURCE_VALUE_ID_);
CREATE INDEX ACT_IDX_MODEL_SOURCE_EXTRA ON SLGZT.ACT_RE_MODEL (EDITOR_SOURCE_EXTRA_VALUE_ID_);
CREATE INDEX INDEX33564741 ON SLGZT.ACT_RE_MODEL (EDITOR_SOURCE_VALUE_ID_);
CREATE INDEX INDEX33564743 ON SLGZT.ACT_RE_MODEL (EDITOR_SOURCE_EXTRA_VALUE_ID_);
CREATE INDEX INDEX33564745 ON SLGZT.ACT_RE_MODEL (DEPLOYMENT_ID_);


-- SLGZT.ACT_RU_EXECUTION definition

CREATE TABLE SLGZT.ACT_RU_EXECUTION (
                                        ID_ NVARCHAR2(256) NOT NULL,
                                        REV_ NUMBER(10,0) NULL,
                                        PROC_INST_ID_ NVARCHAR2(256) NULL,
                                        BUSINESS_KEY_ NVARCHAR2(1020) NULL,
                                        PARENT_ID_ NVARCHAR2(256) NULL,
                                        PROC_DEF_ID_ NVARCHAR2(256) NULL,
                                        SUPER_EXEC_ NVARCHAR2(256) NULL,
                                        ROOT_PROC_INST_ID_ NVARCHAR2(256) NULL,
                                        ACT_ID_ NVARCHAR2(1020) NULL,
                                        IS_ACTIVE_ NUMBER(1,0) NULL,
                                        IS_CONCURRENT_ NUMBER(1,0) NULL,
                                        IS_SCOPE_ NUMBER(1,0) NULL,
                                        IS_EVENT_SCOPE_ NUMBER(1,0) NULL,
                                        IS_MI_ROOT_ NUMBER(1,0) NULL,
                                        SUSPENSION_STATE_ NUMBER(10,0) NULL,
                                        CACHED_ENT_STATE_ NUMBER(10,0) NULL,
                                        TENANT_ID_ NVARCHAR2(1020) DEFAULT '' NULL,
                                        NAME_ NVARCHAR2(1020) NULL,
                                        START_TIME_ TIMESTAMP NULL,
                                        START_USER_ID_ NVARCHAR2(1020) NULL,
                                        LOCK_TIME_ TIMESTAMP NULL,
                                        IS_COUNT_ENABLED_ NUMBER(1,0) NULL,
                                        EVT_SUBSCR_COUNT_ NUMBER(10,0) NULL,
                                        TASK_COUNT_ NUMBER(10,0) NULL,
                                        JOB_COUNT_ NUMBER(10,0) NULL,
                                        TIMER_JOB_COUNT_ NUMBER(10,0) NULL,
                                        SUSP_JOB_COUNT_ NUMBER(10,0) NULL,
                                        DEADLETTER_JOB_COUNT_ NUMBER(10,0) NULL,
                                        VAR_COUNT_ NUMBER(10,0) NULL,
                                        ID_LINK_COUNT_ NUMBER(10,0) NULL,
                                        CONSTRAINT INDEX33564629 PRIMARY KEY (ID_),
                                        CONSTRAINT ACT_FK_EXE_PARENT FOREIGN KEY (PARENT_ID_) REFERENCES SLGZT.ACT_RU_EXECUTION(ID_),
                                        CONSTRAINT ACT_FK_EXE_PROCDEF FOREIGN KEY (PROC_DEF_ID_) REFERENCES SLGZT.ACT_RE_PROCDEF(ID_),
                                        CONSTRAINT ACT_FK_EXE_PROCINST FOREIGN KEY (PROC_INST_ID_) REFERENCES SLGZT.ACT_RU_EXECUTION(ID_),
                                        CONSTRAINT ACT_FK_EXE_SUPER FOREIGN KEY (SUPER_EXEC_) REFERENCES SLGZT.ACT_RU_EXECUTION(ID_)
);
CREATE INDEX ACT_IDX_EXEC_BUSKEY ON SLGZT.ACT_RU_EXECUTION (BUSINESS_KEY_);
CREATE INDEX ACT_IDX_EXEC_ROOT ON SLGZT.ACT_RU_EXECUTION (ROOT_PROC_INST_ID_);
CREATE INDEX ACT_IDX_EXE_PARENT ON SLGZT.ACT_RU_EXECUTION (PARENT_ID_);
CREATE INDEX ACT_IDX_EXE_PROCDEF ON SLGZT.ACT_RU_EXECUTION (PROC_DEF_ID_);
CREATE INDEX ACT_IDX_EXE_PROCINST ON SLGZT.ACT_RU_EXECUTION (PROC_INST_ID_);
CREATE INDEX ACT_IDX_EXE_SUPER ON SLGZT.ACT_RU_EXECUTION (SUPER_EXEC_);
CREATE INDEX INDEX33564663 ON SLGZT.ACT_RU_EXECUTION (PROC_INST_ID_);
CREATE INDEX INDEX33564671 ON SLGZT.ACT_RU_EXECUTION (PARENT_ID_);
CREATE INDEX INDEX33564679 ON SLGZT.ACT_RU_EXECUTION (SUPER_EXEC_);
CREATE INDEX INDEX33564687 ON SLGZT.ACT_RU_EXECUTION (PROC_DEF_ID_);


-- SLGZT.ACT_RU_JOB definition

CREATE TABLE SLGZT.ACT_RU_JOB (
                                  ID_ NVARCHAR2(256) NOT NULL,
                                  REV_ NUMBER(10,0) NULL,
                                  TYPE_ NVARCHAR2(1020) NOT NULL,
                                  LOCK_EXP_TIME_ TIMESTAMP NULL,
                                  LOCK_OWNER_ NVARCHAR2(1020) NULL,
                                  EXCLUSIVE_ NUMBER(1,0) NULL,
                                  EXECUTION_ID_ NVARCHAR2(256) NULL,
                                  PROCESS_INSTANCE_ID_ NVARCHAR2(256) NULL,
                                  PROC_DEF_ID_ NVARCHAR2(256) NULL,
                                  RETRIES_ NUMBER(10,0) NULL,
                                  EXCEPTION_STACK_ID_ NVARCHAR2(256) NULL,
                                  EXCEPTION_MSG_ NVARCHAR2(8000) NULL,
                                  DUEDATE_ TIMESTAMP NULL,
                                  REPEAT_ NVARCHAR2(1020) NULL,
                                  HANDLER_TYPE_ NVARCHAR2(1020) NULL,
                                  HANDLER_CFG_ NVARCHAR2(8000) NULL,
                                  TENANT_ID_ NVARCHAR2(1020) DEFAULT '' NULL,
                                  CONSTRAINT INDEX33564631 PRIMARY KEY (ID_),
                                  CONSTRAINT ACT_FK_JOB_EXCEPTION FOREIGN KEY (EXCEPTION_STACK_ID_) REFERENCES SLGZT.ACT_GE_BYTEARRAY(ID_),
                                  CONSTRAINT ACT_FK_JOB_EXECUTION FOREIGN KEY (EXECUTION_ID_) REFERENCES SLGZT.ACT_RU_EXECUTION(ID_),
                                  CONSTRAINT ACT_FK_JOB_PROCESS_INSTANCE FOREIGN KEY (PROCESS_INSTANCE_ID_) REFERENCES SLGZT.ACT_RU_EXECUTION(ID_),
                                  CONSTRAINT ACT_FK_JOB_PROC_DEF FOREIGN KEY (PROC_DEF_ID_) REFERENCES SLGZT.ACT_RE_PROCDEF(ID_)
);
CREATE INDEX ACT_IDX_JOB_EXCEPTION ON SLGZT.ACT_RU_JOB (EXCEPTION_STACK_ID_);
CREATE INDEX ACT_IDX_JOB_EXECUTION_ID ON SLGZT.ACT_RU_JOB (EXECUTION_ID_);
CREATE INDEX ACT_IDX_JOB_PROC_DEF_ID ON SLGZT.ACT_RU_JOB (PROC_DEF_ID_);
CREATE INDEX ACT_IDX_JOB_PROC_INST_ID ON SLGZT.ACT_RU_JOB (PROCESS_INSTANCE_ID_);
CREATE INDEX INDEX33564707 ON SLGZT.ACT_RU_JOB (EXECUTION_ID_);
CREATE INDEX INDEX33564709 ON SLGZT.ACT_RU_JOB (PROCESS_INSTANCE_ID_);
CREATE INDEX INDEX33564711 ON SLGZT.ACT_RU_JOB (PROC_DEF_ID_);
CREATE INDEX INDEX33564713 ON SLGZT.ACT_RU_JOB (EXCEPTION_STACK_ID_);


-- SLGZT.ACT_RU_SUSPENDED_JOB definition

CREATE TABLE SLGZT.ACT_RU_SUSPENDED_JOB (
                                            ID_ NVARCHAR2(256) NOT NULL,
                                            REV_ NUMBER(10,0) NULL,
                                            TYPE_ NVARCHAR2(1020) NOT NULL,
                                            EXCLUSIVE_ NUMBER(1,0) NULL,
                                            EXECUTION_ID_ NVARCHAR2(256) NULL,
                                            PROCESS_INSTANCE_ID_ NVARCHAR2(256) NULL,
                                            PROC_DEF_ID_ NVARCHAR2(256) NULL,
                                            RETRIES_ NUMBER(10,0) NULL,
                                            EXCEPTION_STACK_ID_ NVARCHAR2(256) NULL,
                                            EXCEPTION_MSG_ NVARCHAR2(8000) NULL,
                                            DUEDATE_ TIMESTAMP NULL,
                                            REPEAT_ NVARCHAR2(1020) NULL,
                                            HANDLER_TYPE_ NVARCHAR2(1020) NULL,
                                            HANDLER_CFG_ NVARCHAR2(8000) NULL,
                                            TENANT_ID_ NVARCHAR2(1020) DEFAULT '' NULL,
                                            CONSTRAINT INDEX33564635 PRIMARY KEY (ID_),
                                            CONSTRAINT ACT_FK_SJOB_EXCEPTION FOREIGN KEY (EXCEPTION_STACK_ID_) REFERENCES SLGZT.ACT_GE_BYTEARRAY(ID_),
                                            CONSTRAINT ACT_FK_SJOB_EXECUTION FOREIGN KEY (EXECUTION_ID_) REFERENCES SLGZT.ACT_RU_EXECUTION(ID_),
                                            CONSTRAINT ACT_FK_SJOB_PROCESS_INSTANCE FOREIGN KEY (PROCESS_INSTANCE_ID_) REFERENCES SLGZT.ACT_RU_EXECUTION(ID_),
                                            CONSTRAINT ACT_FK_SJOB_PROC_DEF FOREIGN KEY (PROC_DEF_ID_) REFERENCES SLGZT.ACT_RE_PROCDEF(ID_)
);
CREATE INDEX ACT_IDX_SJOB_EXCEPTION ON SLGZT.ACT_RU_SUSPENDED_JOB (EXCEPTION_STACK_ID_);
CREATE INDEX ACT_IDX_SJOB_EXECUTION_ID ON SLGZT.ACT_RU_SUSPENDED_JOB (EXECUTION_ID_);
CREATE INDEX ACT_IDX_SJOB_PROC_DEF_ID ON SLGZT.ACT_RU_SUSPENDED_JOB (PROC_DEF_ID_);
CREATE INDEX ACT_IDX_SJOB_PROC_INST_ID ON SLGZT.ACT_RU_SUSPENDED_JOB (PROCESS_INSTANCE_ID_);
CREATE INDEX INDEX33564723 ON SLGZT.ACT_RU_SUSPENDED_JOB (EXECUTION_ID_);
CREATE INDEX INDEX33564725 ON SLGZT.ACT_RU_SUSPENDED_JOB (PROCESS_INSTANCE_ID_);
CREATE INDEX INDEX33564727 ON SLGZT.ACT_RU_SUSPENDED_JOB (PROC_DEF_ID_);
CREATE INDEX INDEX33564729 ON SLGZT.ACT_RU_SUSPENDED_JOB (EXCEPTION_STACK_ID_);


-- SLGZT.ACT_RU_TASK definition

CREATE TABLE SLGZT.ACT_RU_TASK (
                                   ID_ NVARCHAR2(256) NOT NULL,
                                   REV_ NUMBER(10,0) NULL,
                                   EXECUTION_ID_ NVARCHAR2(256) NULL,
                                   PROC_INST_ID_ NVARCHAR2(256) NULL,
                                   PROC_DEF_ID_ NVARCHAR2(256) NULL,
                                   NAME_ NVARCHAR2(1020) NULL,
                                   PARENT_TASK_ID_ NVARCHAR2(256) NULL,
                                   DESCRIPTION_ NVARCHAR2(8000) NULL,
                                   TASK_DEF_KEY_ NVARCHAR2(1020) NULL,
                                   OWNER_ NVARCHAR2(1020) NULL,
                                   ASSIGNEE_ NVARCHAR2(1020) NULL,
                                   DELEGATION_ NVARCHAR2(256) NULL,
                                   PRIORITY_ NUMBER(10,0) NULL,
                                   CREATE_TIME_ TIMESTAMP NULL,
                                   DUE_DATE_ TIMESTAMP NULL,
                                   CATEGORY_ NVARCHAR2(1020) NULL,
                                   SUSPENSION_STATE_ NUMBER(10,0) NULL,
                                   TENANT_ID_ NVARCHAR2(1020) DEFAULT '' NULL,
                                   FORM_KEY_ NVARCHAR2(1020) NULL,
                                   CLAIM_TIME_ TIMESTAMP NULL,
                                   CONSTRAINT INDEX33564641 PRIMARY KEY (ID_),
                                   CONSTRAINT ACT_FK_TASK_EXE FOREIGN KEY (EXECUTION_ID_) REFERENCES SLGZT.ACT_RU_EXECUTION(ID_),
                                   CONSTRAINT ACT_FK_TASK_PROCDEF FOREIGN KEY (PROC_DEF_ID_) REFERENCES SLGZT.ACT_RE_PROCDEF(ID_),
                                   CONSTRAINT ACT_FK_TASK_PROCINST FOREIGN KEY (PROC_INST_ID_) REFERENCES SLGZT.ACT_RU_EXECUTION(ID_)
);
CREATE INDEX ACT_IDX_TASK_CREATE ON SLGZT.ACT_RU_TASK (CREATE_TIME_);
CREATE INDEX ACT_IDX_TASK_EXEC ON SLGZT.ACT_RU_TASK (EXECUTION_ID_);
CREATE INDEX ACT_IDX_TASK_PROCDEF ON SLGZT.ACT_RU_TASK (PROC_DEF_ID_);
CREATE INDEX ACT_IDX_TASK_PROCINST ON SLGZT.ACT_RU_TASK (PROC_INST_ID_);
CREATE INDEX INDEX33564695 ON SLGZT.ACT_RU_TASK (EXECUTION_ID_);
CREATE INDEX INDEX33564697 ON SLGZT.ACT_RU_TASK (PROC_INST_ID_);
CREATE INDEX INDEX33564699 ON SLGZT.ACT_RU_TASK (PROC_DEF_ID_);


-- SLGZT.ACT_RU_TIMER_JOB definition

CREATE TABLE SLGZT.ACT_RU_TIMER_JOB (
                                        ID_ NVARCHAR2(256) NOT NULL,
                                        REV_ NUMBER(10,0) NULL,
                                        TYPE_ NVARCHAR2(1020) NOT NULL,
                                        LOCK_EXP_TIME_ TIMESTAMP NULL,
                                        LOCK_OWNER_ NVARCHAR2(1020) NULL,
                                        EXCLUSIVE_ NUMBER(1,0) NULL,
                                        EXECUTION_ID_ NVARCHAR2(256) NULL,
                                        PROCESS_INSTANCE_ID_ NVARCHAR2(256) NULL,
                                        PROC_DEF_ID_ NVARCHAR2(256) NULL,
                                        RETRIES_ NUMBER(10,0) NULL,
                                        EXCEPTION_STACK_ID_ NVARCHAR2(256) NULL,
                                        EXCEPTION_MSG_ NVARCHAR2(8000) NULL,
                                        DUEDATE_ TIMESTAMP NULL,
                                        REPEAT_ NVARCHAR2(1020) NULL,
                                        HANDLER_TYPE_ NVARCHAR2(1020) NULL,
                                        HANDLER_CFG_ NVARCHAR2(8000) NULL,
                                        TENANT_ID_ NVARCHAR2(1020) DEFAULT '' NULL,
                                        CONSTRAINT INDEX33564633 PRIMARY KEY (ID_),
                                        CONSTRAINT ACT_FK_TJOB_EXCEPTION FOREIGN KEY (EXCEPTION_STACK_ID_) REFERENCES SLGZT.ACT_GE_BYTEARRAY(ID_),
                                        CONSTRAINT ACT_FK_TJOB_EXECUTION FOREIGN KEY (EXECUTION_ID_) REFERENCES SLGZT.ACT_RU_EXECUTION(ID_),
                                        CONSTRAINT ACT_FK_TJOB_PROCESS_INSTANCE FOREIGN KEY (PROCESS_INSTANCE_ID_) REFERENCES SLGZT.ACT_RU_EXECUTION(ID_),
                                        CONSTRAINT ACT_FK_TJOB_PROC_DEF FOREIGN KEY (PROC_DEF_ID_) REFERENCES SLGZT.ACT_RE_PROCDEF(ID_)
);
CREATE INDEX ACT_IDX_TJOB_EXCEPTION ON SLGZT.ACT_RU_TIMER_JOB (EXCEPTION_STACK_ID_);
CREATE INDEX ACT_IDX_TJOB_EXECUTION_ID ON SLGZT.ACT_RU_TIMER_JOB (EXECUTION_ID_);
CREATE INDEX ACT_IDX_TJOB_PROC_DEF_ID ON SLGZT.ACT_RU_TIMER_JOB (PROC_DEF_ID_);
CREATE INDEX ACT_IDX_TJOB_PROC_INST_ID ON SLGZT.ACT_RU_TIMER_JOB (PROCESS_INSTANCE_ID_);
CREATE INDEX INDEX33564715 ON SLGZT.ACT_RU_TIMER_JOB (EXECUTION_ID_);
CREATE INDEX INDEX33564717 ON SLGZT.ACT_RU_TIMER_JOB (PROCESS_INSTANCE_ID_);
CREATE INDEX INDEX33564719 ON SLGZT.ACT_RU_TIMER_JOB (PROC_DEF_ID_);
CREATE INDEX INDEX33564721 ON SLGZT.ACT_RU_TIMER_JOB (EXCEPTION_STACK_ID_);


-- SLGZT.ACT_RU_VARIABLE definition

CREATE TABLE SLGZT.ACT_RU_VARIABLE (
                                       ID_ NVARCHAR2(256) NOT NULL,
                                       REV_ NUMBER(10,0) NULL,
                                       TYPE_ NVARCHAR2(1020) NOT NULL,
                                       NAME_ NVARCHAR2(1020) NOT NULL,
                                       EXECUTION_ID_ NVARCHAR2(256) NULL,
                                       PROC_INST_ID_ NVARCHAR2(256) NULL,
                                       TASK_ID_ NVARCHAR2(256) NULL,
                                       BYTEARRAY_ID_ NVARCHAR2(256) NULL,
                                       DOUBLE_ NUMBER(38,10) NULL,
                                       LONG_ NUMBER(19,0) NULL,
                                       TEXT_ NVARCHAR2(8000) NULL,
                                       TEXT2_ NVARCHAR2(8000) NULL,
                                       CONSTRAINT INDEX33564645 PRIMARY KEY (ID_),
                                       CONSTRAINT ACT_FK_VAR_BYTEARRAY FOREIGN KEY (BYTEARRAY_ID_) REFERENCES SLGZT.ACT_GE_BYTEARRAY(ID_),
                                       CONSTRAINT ACT_FK_VAR_EXE FOREIGN KEY (EXECUTION_ID_) REFERENCES SLGZT.ACT_RU_EXECUTION(ID_),
                                       CONSTRAINT ACT_FK_VAR_PROCINST FOREIGN KEY (PROC_INST_ID_) REFERENCES SLGZT.ACT_RU_EXECUTION(ID_)
);
CREATE INDEX ACT_IDX_VARIABLE_TASK_ID ON SLGZT.ACT_RU_VARIABLE (TASK_ID_);
CREATE INDEX ACT_IDX_VAR_BYTEARRAY ON SLGZT.ACT_RU_VARIABLE (BYTEARRAY_ID_);
CREATE INDEX ACT_IDX_VAR_EXE ON SLGZT.ACT_RU_VARIABLE (EXECUTION_ID_);
CREATE INDEX ACT_IDX_VAR_PROCINST ON SLGZT.ACT_RU_VARIABLE (PROC_INST_ID_);
CREATE INDEX INDEX33564701 ON SLGZT.ACT_RU_VARIABLE (EXECUTION_ID_);
CREATE INDEX INDEX33564703 ON SLGZT.ACT_RU_VARIABLE (PROC_INST_ID_);
CREATE INDEX INDEX33564705 ON SLGZT.ACT_RU_VARIABLE (BYTEARRAY_ID_);


-- SLGZT.BATCH_JOB_EXECUTION definition

CREATE TABLE SLGZT.BATCH_JOB_EXECUTION (
                                           JOB_EXECUTION_ID NUMBER(19,0) NOT NULL,
                                           VERSION NUMBER(19,0) NULL,
                                           JOB_INSTANCE_ID NUMBER(19,0) NOT NULL,
                                           CREATE_TIME TIMESTAMP NOT NULL,
                                           START_TIME TIMESTAMP DEFAULT NULL NULL,
                                           END_TIME TIMESTAMP DEFAULT NULL NULL,
                                           STATUS VARCHAR2(40) NULL,
                                           EXIT_CODE VARCHAR2(10000) NULL,
                                           EXIT_MESSAGE VARCHAR2(10000) NULL,
                                           LAST_UPDATED TIMESTAMP NULL,
                                           JOB_CONFIGURATION_LOCATION VARCHAR(10000) NULL,
                                           CONSTRAINT INDEX33565569 PRIMARY KEY (JOB_EXECUTION_ID),
                                           CONSTRAINT JOB_INST_EXEC_FK FOREIGN KEY (JOB_INSTANCE_ID) REFERENCES SLGZT.BATCH_JOB_INSTANCE(JOB_INSTANCE_ID)
);
CREATE INDEX INDEX33565570 ON SLGZT.BATCH_JOB_EXECUTION (JOB_INSTANCE_ID);


-- SLGZT.BATCH_JOB_EXECUTION_CONTEXT definition

CREATE TABLE SLGZT.BATCH_JOB_EXECUTION_CONTEXT (
                                                   JOB_EXECUTION_ID NUMBER(19,0) NOT NULL,
                                                   SHORT_CONTEXT VARCHAR2(10000) NOT NULL,
                                                   SERIALIZED_CONTEXT CLOB NULL,
                                                   CONSTRAINT INDEX33565580 PRIMARY KEY (JOB_EXECUTION_ID),
                                                   CONSTRAINT JOB_EXEC_CTX_FK FOREIGN KEY (JOB_EXECUTION_ID) REFERENCES SLGZT.BATCH_JOB_EXECUTION(JOB_EXECUTION_ID)
);
CREATE INDEX INDEX33565581 ON SLGZT.BATCH_JOB_EXECUTION_CONTEXT (JOB_EXECUTION_ID);


-- SLGZT.BATCH_JOB_EXECUTION_PARAMS definition

CREATE TABLE SLGZT.BATCH_JOB_EXECUTION_PARAMS (
                                                  JOB_EXECUTION_ID NUMBER(19,0) NOT NULL,
                                                  TYPE_CD VARCHAR2(24) NOT NULL,
                                                  KEY_NAME VARCHAR2(400) NOT NULL,
                                                  STRING_VAL VARCHAR2(1000) NULL,
                                                  DATE_VAL TIMESTAMP DEFAULT NULL NULL,
                                                  LONG_VAL NUMBER(19,0) NULL,
                                                  DOUBLE_VAL NUMBER NULL,
                                                  IDENTIFYING CHAR(1) NOT NULL,
                                                  CONSTRAINT JOB_EXEC_PARAMS_FK FOREIGN KEY (JOB_EXECUTION_ID) REFERENCES SLGZT.BATCH_JOB_EXECUTION(JOB_EXECUTION_ID)
);
CREATE INDEX INDEX33565572 ON SLGZT.BATCH_JOB_EXECUTION_PARAMS (JOB_EXECUTION_ID);


-- SLGZT.BATCH_STEP_EXECUTION definition

CREATE TABLE SLGZT.BATCH_STEP_EXECUTION (
                                            STEP_EXECUTION_ID NUMBER(19,0) NOT NULL,
                                            VERSION NUMBER(19,0) NOT NULL,
                                            STEP_NAME VARCHAR2(400) NOT NULL,
                                            JOB_EXECUTION_ID NUMBER(19,0) NOT NULL,
                                            START_TIME TIMESTAMP NOT NULL,
                                            END_TIME TIMESTAMP DEFAULT NULL NULL,
                                            STATUS VARCHAR2(40) NULL,
                                            COMMIT_COUNT NUMBER(19,0) NULL,
                                            READ_COUNT NUMBER(19,0) NULL,
                                            FILTER_COUNT NUMBER(19,0) NULL,
                                            WRITE_COUNT NUMBER(19,0) NULL,
                                            READ_SKIP_COUNT NUMBER(19,0) NULL,
                                            WRITE_SKIP_COUNT NUMBER(19,0) NULL,
                                            PROCESS_SKIP_COUNT NUMBER(19,0) NULL,
                                            ROLLBACK_COUNT NUMBER(19,0) NULL,
                                            EXIT_CODE VARCHAR2(10000) NULL,
                                            EXIT_MESSAGE VARCHAR2(10000) NULL,
                                            LAST_UPDATED TIMESTAMP NULL,
                                            CONSTRAINT INDEX33565574 PRIMARY KEY (STEP_EXECUTION_ID),
                                            CONSTRAINT JOB_EXEC_STEP_FK FOREIGN KEY (JOB_EXECUTION_ID) REFERENCES SLGZT.BATCH_JOB_EXECUTION(JOB_EXECUTION_ID)
);
CREATE INDEX INDEX33565575 ON SLGZT.BATCH_STEP_EXECUTION (JOB_EXECUTION_ID);


-- SLGZT.BATCH_STEP_EXECUTION_CONTEXT definition

CREATE TABLE SLGZT.BATCH_STEP_EXECUTION_CONTEXT (
                                                    STEP_EXECUTION_ID NUMBER(19,0) NOT NULL,
                                                    SHORT_CONTEXT VARCHAR2(10000) NOT NULL,
                                                    SERIALIZED_CONTEXT CLOB NULL,
                                                    CONSTRAINT INDEX33565577 PRIMARY KEY (STEP_EXECUTION_ID),
                                                    CONSTRAINT STEP_EXEC_CTX_FK FOREIGN KEY (STEP_EXECUTION_ID) REFERENCES SLGZT.BATCH_STEP_EXECUTION(STEP_EXECUTION_ID)
);
CREATE INDEX INDEX33565578 ON SLGZT.BATCH_STEP_EXECUTION_CONTEXT (STEP_EXECUTION_ID);


-- SLGZT.ACT_RU_DEADLETTER_JOB definition

CREATE TABLE SLGZT.ACT_RU_DEADLETTER_JOB (
                                             ID_ NVARCHAR2(256) NOT NULL,
                                             REV_ NUMBER(10,0) NULL,
                                             TYPE_ NVARCHAR2(1020) NOT NULL,
                                             EXCLUSIVE_ NUMBER(1,0) NULL,
                                             EXECUTION_ID_ NVARCHAR2(256) NULL,
                                             PROCESS_INSTANCE_ID_ NVARCHAR2(256) NULL,
                                             PROC_DEF_ID_ NVARCHAR2(256) NULL,
                                             EXCEPTION_STACK_ID_ NVARCHAR2(256) NULL,
                                             EXCEPTION_MSG_ NVARCHAR2(8000) NULL,
                                             DUEDATE_ TIMESTAMP NULL,
                                             REPEAT_ NVARCHAR2(1020) NULL,
                                             HANDLER_TYPE_ NVARCHAR2(1020) NULL,
                                             HANDLER_CFG_ NVARCHAR2(8000) NULL,
                                             TENANT_ID_ NVARCHAR2(1020) DEFAULT '' NULL,
                                             CONSTRAINT INDEX33564637 PRIMARY KEY (ID_),
                                             CONSTRAINT ACT_FK_DJOB_EXCEPTION FOREIGN KEY (EXCEPTION_STACK_ID_) REFERENCES SLGZT.ACT_GE_BYTEARRAY(ID_),
                                             CONSTRAINT ACT_FK_DJOB_EXECUTION FOREIGN KEY (EXECUTION_ID_) REFERENCES SLGZT.ACT_RU_EXECUTION(ID_),
                                             CONSTRAINT ACT_FK_DJOB_PROCESS_INSTANCE FOREIGN KEY (PROCESS_INSTANCE_ID_) REFERENCES SLGZT.ACT_RU_EXECUTION(ID_),
                                             CONSTRAINT ACT_FK_DJOB_PROC_DEF FOREIGN KEY (PROC_DEF_ID_) REFERENCES SLGZT.ACT_RE_PROCDEF(ID_)
);
CREATE INDEX ACT_IDX_DJOB_EXCEPTION ON SLGZT.ACT_RU_DEADLETTER_JOB (EXCEPTION_STACK_ID_);
CREATE INDEX ACT_IDX_DJOB_EXECUTION_ID ON SLGZT.ACT_RU_DEADLETTER_JOB (EXECUTION_ID_);
CREATE INDEX ACT_IDX_DJOB_PROC_DEF_ID ON SLGZT.ACT_RU_DEADLETTER_JOB (PROC_DEF_ID_);
CREATE INDEX ACT_IDX_DJOB_PROC_INST_ID ON SLGZT.ACT_RU_DEADLETTER_JOB (PROCESS_INSTANCE_ID_);
CREATE INDEX INDEX33564731 ON SLGZT.ACT_RU_DEADLETTER_JOB (EXECUTION_ID_);
CREATE INDEX INDEX33564733 ON SLGZT.ACT_RU_DEADLETTER_JOB (PROCESS_INSTANCE_ID_);
CREATE INDEX INDEX33564735 ON SLGZT.ACT_RU_DEADLETTER_JOB (PROC_DEF_ID_);
CREATE INDEX INDEX33564737 ON SLGZT.ACT_RU_DEADLETTER_JOB (EXCEPTION_STACK_ID_);


-- SLGZT.ACT_RU_EVENT_SUBSCR definition

CREATE TABLE SLGZT.ACT_RU_EVENT_SUBSCR (
                                           ID_ NVARCHAR2(256) NOT NULL,
                                           REV_ NUMBER(10,0) NULL,
                                           EVENT_TYPE_ NVARCHAR2(1020) NOT NULL,
                                           EVENT_NAME_ NVARCHAR2(1020) NULL,
                                           EXECUTION_ID_ NVARCHAR2(256) NULL,
                                           PROC_INST_ID_ NVARCHAR2(256) NULL,
                                           ACTIVITY_ID_ NVARCHAR2(256) NULL,
                                           CONFIGURATION_ NVARCHAR2(1020) NULL,
                                           CREATED_ TIMESTAMP NOT NULL,
                                           PROC_DEF_ID_ NVARCHAR2(256) NULL,
                                           TENANT_ID_ NVARCHAR2(1020) DEFAULT '' NULL,
                                           CONSTRAINT INDEX33564647 PRIMARY KEY (ID_),
                                           CONSTRAINT ACT_FK_EVENT_EXEC FOREIGN KEY (EXECUTION_ID_) REFERENCES SLGZT.ACT_RU_EXECUTION(ID_)
);
CREATE INDEX ACT_IDX_EVENT_SUBSCR ON SLGZT.ACT_RU_EVENT_SUBSCR (EXECUTION_ID_);
CREATE INDEX ACT_IDX_EVENT_SUBSCR_CONFIG_ ON SLGZT.ACT_RU_EVENT_SUBSCR (CONFIGURATION_);
CREATE INDEX INDEX33564739 ON SLGZT.ACT_RU_EVENT_SUBSCR (EXECUTION_ID_);


-- SLGZT.ACT_RU_IDENTITYLINK definition

CREATE TABLE SLGZT.ACT_RU_IDENTITYLINK (
                                           ID_ NVARCHAR2(256) NOT NULL,
                                           REV_ NUMBER(10,0) NULL,
                                           GROUP_ID_ NVARCHAR2(1020) NULL,
                                           TYPE_ NVARCHAR2(1020) NULL,
                                           USER_ID_ NVARCHAR2(1020) NULL,
                                           TASK_ID_ NVARCHAR2(256) NULL,
                                           PROC_INST_ID_ NVARCHAR2(256) NULL,
                                           PROC_DEF_ID_ NVARCHAR2(256) NULL,
                                           CONSTRAINT INDEX33564643 PRIMARY KEY (ID_),
                                           CONSTRAINT ACT_FK_ATHRZ_PROCEDEF FOREIGN KEY (PROC_DEF_ID_) REFERENCES SLGZT.ACT_RE_PROCDEF(ID_),
                                           CONSTRAINT ACT_FK_IDL_PROCINST FOREIGN KEY (PROC_INST_ID_) REFERENCES SLGZT.ACT_RU_EXECUTION(ID_),
                                           CONSTRAINT ACT_FK_TSKASS_TASK FOREIGN KEY (TASK_ID_) REFERENCES SLGZT.ACT_RU_TASK(ID_)
);
CREATE INDEX ACT_IDX_ATHRZ_PROCEDEF ON SLGZT.ACT_RU_IDENTITYLINK (PROC_DEF_ID_);
CREATE INDEX ACT_IDX_IDENT_LNK_GROUP ON SLGZT.ACT_RU_IDENTITYLINK (GROUP_ID_);
CREATE INDEX ACT_IDX_IDENT_LNK_USER ON SLGZT.ACT_RU_IDENTITYLINK (USER_ID_);
CREATE INDEX ACT_IDX_IDL_PROCINST ON SLGZT.ACT_RU_IDENTITYLINK (PROC_INST_ID_);
CREATE INDEX ACT_IDX_TSKASS_TASK ON SLGZT.ACT_RU_IDENTITYLINK (TASK_ID_);
CREATE INDEX INDEX33564689 ON SLGZT.ACT_RU_IDENTITYLINK (TASK_ID_);
CREATE INDEX INDEX33564691 ON SLGZT.ACT_RU_IDENTITYLINK (PROC_DEF_ID_);
CREATE INDEX INDEX33564693 ON SLGZT.ACT_RU_IDENTITYLINK (PROC_INST_ID_);

--虚拟网卡表
CREATE TABLE "WOC_VNIC" (
   "ID" VARCHAR2(64) NOT NULL,
   "VNIC_ID" VARCHAR2(64),
   "VNIC_NAME" VARCHAR2(255),
   "BUSINESS_SYSTEM_NAME" VARCHAR2(255),
   "BUSINESS_SYSTEM_ID" VARCHAR2(64),
   "CATALOGUE_DOMAIN_NAME" VARCHAR2(255),
   "CATALOGUE_DOMAIN_CODE" VARCHAR2(64),
   "DOMAIN_NAME" VARCHAR2(255),
   "DOMAIN_CODE" VARCHAR2(64),
   "REGION_NAME" VARCHAR2(255),
   "REGION_ID" VARCHAR2(64),
   "AZ_NAME" VARCHAR2(255),
   "AZ_ID" VARCHAR2(64),
   "VPC_NAME" VARCHAR2(255),
   "VPC_ID" VARCHAR2(64),
   "SUBNET_NAME" VARCHAR2(255),
   "SUBNET_ID" VARCHAR2(64),
   "IP_ADDRESS" VARCHAR2(64),
   "VM_NAME" VARCHAR2(255),
   "VM_ID" VARCHAR2(64),
   "ENABLED" NUMBER(1) DEFAULT 1,
   "CREATE_TIME" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
   "MODIFY_TIME" TIMESTAMP,
   CONSTRAINT "PK_WOC_VNIC" PRIMARY KEY ("ID")
);

COMMENT ON TABLE "WOC_VNIC" IS '虚拟网卡表';
COMMENT ON COLUMN "WOC_VNIC"."ID" IS '主键ID';
COMMENT ON COLUMN "WOC_VNIC"."VNIC_ID" IS '虚拟网卡ID';
COMMENT ON COLUMN "WOC_VNIC"."VNIC_NAME" IS '虚拟网卡名称';
COMMENT ON COLUMN "WOC_VNIC"."BUSINESS_SYSTEM_NAME" IS '业务系统名称';
COMMENT ON COLUMN "WOC_VNIC"."BUSINESS_SYSTEM_ID" IS '业务系统Id';
COMMENT ON COLUMN "WOC_VNIC"."CATALOGUE_DOMAIN_NAME" IS '云类型名称';
COMMENT ON COLUMN "WOC_VNIC"."CATALOGUE_DOMAIN_CODE" IS '云类型编码';
COMMENT ON COLUMN "WOC_VNIC"."DOMAIN_NAME" IS '云平台名称';
COMMENT ON COLUMN "WOC_VNIC"."DOMAIN_CODE" IS '云平台编码';
COMMENT ON COLUMN "WOC_VNIC"."REGION_NAME" IS '资源池名称';
COMMENT ON COLUMN "WOC_VNIC"."REGION_ID" IS '资源池ID';
COMMENT ON COLUMN "WOC_VNIC"."AZ_NAME" IS '可用区名称';
COMMENT ON COLUMN "WOC_VNIC"."AZ_ID" IS '可用区ID';
COMMENT ON COLUMN "WOC_VNIC"."VPC_NAME" IS 'VPC名称';
COMMENT ON COLUMN "WOC_VNIC"."VPC_ID" IS 'VPC ID';
COMMENT ON COLUMN "WOC_VNIC"."SUBNET_NAME" IS '子网名称';
COMMENT ON COLUMN "WOC_VNIC"."SUBNET_ID" IS '子网ID';
COMMENT ON COLUMN "WOC_VNIC"."IP_ADDRESS" IS 'IP地址';
COMMENT ON COLUMN "WOC_VNIC"."VM_NAME" IS '云主机名称';
COMMENT ON COLUMN "WOC_VNIC"."VM_ID" IS '云主机ID';
COMMENT ON COLUMN "WOC_VNIC"."ENABLED" IS '逻辑删除标识(1:有效 0:删除)';
COMMENT ON COLUMN "WOC_VNIC"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "WOC_VNIC"."MODIFY_TIME" IS '修改时间';
-- 添加REGION_CODE字段
ALTER TABLE "WOC_VNIC" ADD "REGION_CODE" VARCHAR2(64);
COMMENT ON COLUMN "WOC_VNIC"."REGION_CODE" IS '资源池code';
-- 添加AZ_CODE字段
ALTER TABLE "WOC_VNIC" ADD "AZ_CODE" VARCHAR2(64);
COMMENT ON COLUMN "WOC_VNIC"."AZ_CODE" IS '可用区code';
-- 添加DESCRIPTION字段
ALTER TABLE "WOC_VNIC" ADD "DESCRIPTION" VARCHAR2(64);
COMMENT ON COLUMN "WOC_VNIC"."DESCRIPTION" IS '描述';
package com.datatech.slgzt.convert;

import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.model.BaseProductModel;
import com.datatech.slgzt.model.dto.StandardWorkOrderDTO;
import com.datatech.slgzt.model.dto.StandardWorkOrderProductDTO;
import com.datatech.slgzt.model.dto.order.GoodsProductDTO;
import com.datatech.slgzt.model.nostander.*;
import com.datatech.slgzt.model.opm.ProductGeneralCheckOpm;
import com.datatech.slgzt.model.query.StandardWorkOrderQuery;
import com.datatech.slgzt.model.req.standard.StandardWorkOrderCreateReq;
import com.datatech.slgzt.model.req.standard.StandardWorkOrderPageReq;
import com.datatech.slgzt.model.vo.standard.StandardWorkOrderDetailVO;
import com.datatech.slgzt.model.vo.standard.StandardWorkOrderVO;
import com.datatech.slgzt.utils.HashUtils;
import com.datatech.slgzt.utils.StreamUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring")
public interface StandardWorkOrderWebConvert {


    @Mapping(target = "cloudEcsResourceList", source = "ecsModelList")
    @Mapping(target = "cpuEcsResourceList", source = "gcsModelList")
    @Mapping(target = "natGatwayModelList", source = "natModelList")
    @Mapping(target = "resourceApplyFile", source = "resourceApplyFiles")
    StandardWorkOrderDTO convert(StandardWorkOrderCreateReq req);
    StandardWorkOrderQuery convert(StandardWorkOrderPageReq req);

    @Mapping(target = "createUserId", source = "createdBy")
    StandardWorkOrderVO convert(StandardWorkOrderDTO req);


    @Mapping(target = "ecsModelList", ignore = true)
    @Mapping(target = "gcsModelList", ignore = true)
    @Mapping(target = "evsModelList", ignore = true)
    @Mapping(target = "eipModelList", ignore = true)
    @Mapping(target = "natModelList", ignore = true)
    @Mapping(target = "slbModelList", ignore = true)
    @Mapping(target = "obsModelList", ignore = true)
    @Mapping(target = "cqModelList", ignore = true)
    @Mapping(target = "resourcePoolModelList", ignore = true)
    @Mapping(target = "resourceApplyFiles", source = "resourceApplyFile")
    @Mapping(target = "orderType", defaultValue = "orderType" ,qualifiedByName = "orderType")
    StandardWorkOrderDetailVO convertDetail(StandardWorkOrderDTO req);


    @Mapping(target = "ecsModelList", source = "cloudEcsResourceList")
    @Mapping(target = "gcsModelList", source = "cpuEcsResourceList")
    @Mapping(target = "natModelList", source = "natGatwayModelList")
    @Mapping(target = "resourceApplyFiles", source = "resourceApplyFile")
    StandardWorkOrderDetailVO convertDetailDraft(StandardWorkOrderDTO req);


    @Named("orderType")
    default String orderType(String orderType){
        return "资源开通";
    }



    default void fillDetail(StandardWorkOrderDetailVO vo, List<StandardWorkOrderProductDTO> productDTOS,Boolean aggregation) {
        //找出所有主产品--->主产品就是父类id=0的
        List<StandardWorkOrderProductDTO> mainProductDTOS = productDTOS.stream().filter(item -> item.getParentProductId() == 0)
                .collect(Collectors.toList());
        List<CloudEcsResourceModel> ecsList = Lists.newArrayList();
        List<CpuEcsResourceModel> gcsList =Lists.newArrayList();
        List<EcsModel> mysqlList =Lists.newArrayList();
        List<EcsModel> redisList =Lists.newArrayList();
        List<MountDataDiskModel> evsList =Lists.newArrayList();
        List<EipModel> eipList =Lists.newArrayList();
        List<SlbModel> slbModelList =Lists.newArrayList();
        List<ObsModel> obsModels =Lists.newArrayList();
        List<NatGatwayModel> natGatwayModels =Lists.newArrayList();
        List<CQModel> cqModels =Lists.newArrayList();
       // 处理ESC类型
        processProductType(mainProductDTOS, ProductTypeEnum.ECS.getCode(), CloudEcsResourceModel.class, ecsList);
        //如果aggregation为true则需要聚合 把idhash相同取一个即可
        if(aggregation){
            ecsList = StreamUtils.distinctByKey(ecsList, CloudEcsResourceModel::getIdHash);
        }
        vo.setEcsModelList(ecsList);
        // 处理GCS类型
        processProductType(mainProductDTOS, ProductTypeEnum.GCS.getCode(), CpuEcsResourceModel.class, gcsList);
        if(aggregation){
            gcsList = StreamUtils.distinctByKey(gcsList, CpuEcsResourceModel::getIdHash);
        }
        vo.setGcsModelList(gcsList);
        // 处理mysql类型
        processProductType(mainProductDTOS, ProductTypeEnum.MYSQL.getCode(), EcsModel.class, mysqlList);
        if(aggregation){
            mysqlList = StreamUtils.distinctByKey(mysqlList, EcsModel::getIdHash);
        }
        vo.setMysqlModelList(mysqlList);
        // 处理redis类型
        processProductType(mainProductDTOS, ProductTypeEnum.REDIS.getCode(), EcsModel.class, redisList);
        if(aggregation){
            redisList = StreamUtils.distinctByKey(redisList, EcsModel::getIdHash);
        }
        vo.setRedisModelList(redisList);
        // 处理EVS类型
        processProductType(mainProductDTOS, ProductTypeEnum.EVS.getCode(), MountDataDiskModel.class, evsList);
        if (aggregation) {
            evsList = StreamUtils.distinctByKey(evsList, MountDataDiskModel::getIdHash);
        }
        vo.setEvsModelList(evsList);
        // 处理EIP类型
        processProductType(mainProductDTOS, ProductTypeEnum.EIP.getCode(), EipModel.class, eipList);
        if (aggregation) {
            eipList = StreamUtils.distinctByKey(eipList, EipModel::getIdHash);
        }
        vo.setEipModelList(eipList);
        // 处理SLB类型
        processProductType(mainProductDTOS, ProductTypeEnum.SLB.getCode(), SlbModel.class, slbModelList);
        if (aggregation) {
            slbModelList = StreamUtils.distinctByKey(slbModelList, SlbModel::getIdHash);
        }
        vo.setSlbModelList(slbModelList);
        // 处理OBS类型
        processProductType(mainProductDTOS, ProductTypeEnum.OBS.getCode(), ObsModel.class, obsModels);
        if (aggregation) {
            obsModels = StreamUtils.distinctByKey(obsModels, ObsModel::getIdHash);
        }
        vo.setObsModelList(obsModels);
        // 处理NAT类型
        processProductType(mainProductDTOS, ProductTypeEnum.NAT.getCode(), NatGatwayModel.class, natGatwayModels);
        if (aggregation) {
            natGatwayModels = StreamUtils.distinctByKey(natGatwayModels, NatGatwayModel::getIdHash);
        }
        vo.setNatModelList(natGatwayModels);
        // 处理CONTAINER类型
        processProductType(mainProductDTOS, ProductTypeEnum.CQ.getCode(), CQModel.class, cqModels);
        if (aggregation) {
            cqModels = StreamUtils.distinctByKey(cqModels, CQModel::getIdHash);
        }
        vo.setCqModelList(cqModels);
        //设置资源池 -->全部转成BaseProductModel 拿到里面的名称
        List<BaseProductModel> baseProductModels = new ArrayList<>();
        Set<Long> flgSet= Sets.newHashSet();
        productDTOS.forEach(item -> {
            BaseProductModel resourceModel = JSON.parseObject(item.getPropertySnapshot(), BaseProductModel.class);
            if(resourceModel.getRegionId()==null){
                return;
            }
            if (flgSet.contains(resourceModel.getRegionId())) {
                return;
            }
            flgSet.add(resourceModel.getRegionId());
            baseProductModels.add(resourceModel);
        });
        vo.setResourcePoolModelList(baseProductModels);
    }



    default String convert(ProductTypeEnum productTypeEnum) {
        if (productTypeEnum == null) {
            return null;
        }
        return productTypeEnum.getCode();
    }


    // 泛型方法处理公共逻辑
    default  <T> void processProductType(List<StandardWorkOrderProductDTO> mainProducts,
            String productType,
            Class<T> targetClass,
            List<? super T> resultList) {
        mainProducts.stream()
                .filter(item -> productType.equals(item.getProductType()))
                .forEach(item -> {
                    T resourceModel = JSON.parseObject(item.getPropertySnapshot(), targetClass);
                    String idHash = HashUtils.calculateUniqueId(
                            ((BaseProductModel) resourceModel).getMainIds()
                    );
                    ((BaseProductModel) resourceModel).setIdHash(idHash);
                    ((BaseProductModel) resourceModel).setMessage(item.getMessage());
                    ((BaseProductModel) resourceModel).setStatus(item.getOpenStatus());
                    resultList.add(resourceModel);
                });
    }


    default ProductGeneralCheckOpm convertCheckOpm(List<StandardWorkOrderProductDTO> productDTOS,List<GoodsProductDTO> goodsModelList) {
        ProductGeneralCheckOpm productGeneralCheckOpm = new ProductGeneralCheckOpm();
        Map<String, GoodsProductDTO> goodsProductDTOMap = StreamUtils.toMap(goodsModelList, GoodsProductDTO::getId);
        List<CloudEcsResourceModel> ecsModelList = Lists.newArrayList();
        List<CpuEcsResourceModel> gcsModelList = Lists.newArrayList();
        List<MountDataDiskModel> evsModelList = Lists.newArrayList();
        List<EipModel> eipModelList = Lists.newArrayList();
        List<NatGatwayModel> natModelList = Lists.newArrayList();
        List<SlbModel> slbModelList = Lists.newArrayList();
        List<ObsModel> obsModelList = Lists.newArrayList();
        List<CQModel> cqModelList = Lists.newArrayList();
        //循环productDTOS
        for (StandardWorkOrderProductDTO productDTO : productDTOS) {
            if (ProductTypeEnum.ECS.getCode().equals(productDTO.getProductType())) {
                GoodsProductDTO goodsProductDTO = goodsProductDTOMap.get(productDTO.getId().toString());
                CloudEcsResourceModel cloudEcsResourceModel = JSON.parseObject(productDTO.getPropertySnapshot(), CloudEcsResourceModel.class);
                fillRegion(cloudEcsResourceModel,goodsProductDTO);
                ecsModelList.add(cloudEcsResourceModel);
            }
            if (ProductTypeEnum.GCS.getCode().equals(productDTO.getProductType())) {
                GoodsProductDTO goodsProductDTO = goodsProductDTOMap.get(productDTO.getId().toString());
                CpuEcsResourceModel cpuEcsResourceModel = JSON.parseObject(productDTO.getPropertySnapshot(), CpuEcsResourceModel.class);
                fillRegion(cpuEcsResourceModel,goodsProductDTO);
                gcsModelList.add(cpuEcsResourceModel);
            }
            if (ProductTypeEnum.EVS.getCode().equals(productDTO.getProductType())) {
                GoodsProductDTO goodsProductDTO = goodsProductDTOMap.get(productDTO.getId().toString());
                MountDataDiskModel mountDataDiskModel = JSON.parseObject(productDTO.getPropertySnapshot(), MountDataDiskModel.class);
                fillRegion(mountDataDiskModel,goodsProductDTO);
                evsModelList.add(mountDataDiskModel);
            }
            if (ProductTypeEnum.EIP.getCode().equals(productDTO.getProductType())) {
                GoodsProductDTO goodsProductDTO = goodsProductDTOMap.get(productDTO.getId().toString());
                EipModel eipModel = JSON.parseObject(productDTO.getPropertySnapshot(), EipModel.class);
                fillRegion(eipModel,goodsProductDTO);
                eipModelList.add(eipModel);
            }
            if (ProductTypeEnum.SLB.getCode().equals(productDTO.getProductType())) {
                GoodsProductDTO goodsProductDTO = goodsProductDTOMap.get(productDTO.getId().toString());
                SlbModel slbModel = JSON.parseObject(productDTO.getPropertySnapshot(), SlbModel.class);
                fillRegion(slbModel,goodsProductDTO);
                slbModelList.add(slbModel);
            }
            if (ProductTypeEnum.OBS.getCode().equals(productDTO.getProductType())) {
                GoodsProductDTO goodsProductDTO = goodsProductDTOMap.get(productDTO.getId().toString());
                ObsModel obsModel = JSON.parseObject(productDTO.getPropertySnapshot(), ObsModel.class);
                fillRegion(obsModel,goodsProductDTO);
                obsModelList.add(obsModel);
            }
            if (ProductTypeEnum.NAT.getCode().equals(productDTO.getProductType())) {
                GoodsProductDTO goodsProductDTO = goodsProductDTOMap.get(productDTO.getId().toString());
                NatGatwayModel natGatwayModel = JSON.parseObject(productDTO.getPropertySnapshot(), NatGatwayModel.class);
                fillRegion(natGatwayModel,goodsProductDTO);
                natModelList.add(natGatwayModel);
            }
            if (ProductTypeEnum.CQ.getCode().equals(productDTO.getProductType())) {
                GoodsProductDTO goodsProductDTO = goodsProductDTOMap.get(productDTO.getId().toString());
                CQModel containerModel = JSON.parseObject(productDTO.getPropertySnapshot(), CQModel.class);
                fillRegion(containerModel,goodsProductDTO);
                cqModelList.add(containerModel);
            }
        }
        productGeneralCheckOpm.setEcsModelList(ecsModelList);
        productGeneralCheckOpm.setGcsModelList(gcsModelList);
        productGeneralCheckOpm.setEvsModelList(evsModelList);
        productGeneralCheckOpm.setEipModelList(eipModelList);
        productGeneralCheckOpm.setNatModelList(natModelList);
        productGeneralCheckOpm.setSlbModelList(slbModelList);
        productGeneralCheckOpm.setObsModelList(obsModelList);
        productGeneralCheckOpm.setCqModelList(cqModelList);
        productGeneralCheckOpm.setCheckSlbNum(Boolean.TRUE);
        return productGeneralCheckOpm;
    }

    default void fillRegion(BaseProductModel baseProductModel,GoodsProductDTO goodsProductDTO) {
        baseProductModel.setRegionCode(goodsProductDTO.getRegionCode());
        baseProductModel.setRegionId(Long.valueOf(goodsProductDTO.getRegionId()));
        baseProductModel.setRegionName(goodsProductDTO.getRegionName());
    }
}

package com.datatech.slgzt.manager.xieyun;

import com.datatech.slgzt.model.dto.xieyun.QuotaResultDTO;
import com.datatech.slgzt.model.xieyun.XieyunOrgCreateOpm;
import com.datatech.slgzt.model.xieyun.XieyunOrgQuotaOpm;

public interface XieyunOrgManager {

    /**
     * 创建组织
     */
    String ensureOrg(XieyunOrgCreateOpm opm);

    String orgQuota(String clusterName, String nodePoolName, String orgId, XieyunOrgQuotaOpm opm);

    /**
     * 查询组织额度
     * @param clusterName 集群名称
     * @param nodePoolName 资源池名称
     * @param orgId 组织id
     * @return
     */
    QuotaResultDTO selectOrgQuota(String clusterName, String nodePoolName, String orgId);
}

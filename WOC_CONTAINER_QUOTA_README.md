# WOC_CONTAINER_QUOTA 容器配额管理功能

## 功能概述

基于CQModel创建了WOC_CONTAINER_QUOTA表和相关的完整代码层，用于管理容器配额信息。该功能支持容器配额的创建、查询，并提供了丰富的搜索功能。

## 数据库设计

### 表结构：WOC_CONTAINER_QUOTA

```sql
CREATE TABLE SLGZT.WOC_CONTAINER_QUOTA (
    ID VARCHAR2(64) NOT NULL,                    -- 主键ID
    WORK_ORDER_ID VARCHAR2(32) NULL,             -- 工单ID
    SUB_ORDER_ID VARCHAR2(50) NULL,              -- 子订单ID
    BUSINESS_SYSTEM_ID NUMBER NULL,              -- 业务系统ID
    BUSINESS_SYSTEM_NAME VARCHAR2(128) NULL,     -- 业务系统名称
    CQ_NAME VARCHAR2(255) NULL,                  -- 配额名称
    V_CPUS NUMBER(10,0) NULL,                    -- 容器配额-核心数
    RAM NUMBER(10,0) NULL,                       -- 容器配额-内存大小，单位G
    GPU_RATIO NUMBER(10,0) NULL,                 -- GPU算力
    GPU_VIRTUAL_MEMORY NUMBER(10,0) NULL,        -- GPU显存大小，单位GB
    GPU_CORE NUMBER(10,0) NULL,                  -- 物理GPU卡(个)
    GPU_VIRTUAL_CORE NUMBER(10,0) NULL,          -- 虚拟GPU卡(个)
    A4_ACCOUNT VARCHAR2(128) NULL,               -- 4A账号
    A4_PHONE VARCHAR2(32) NULL,                  -- 4A账号绑定的手机
    APPLY_TIME VARCHAR2(64) NULL,                -- 申请时长
    OPEN_NUM NUMBER(10,0) NULL,                  -- 开通数量
    CATALOGUE_DOMAIN_CODE VARCHAR2(64) NULL,     -- 云类型编码
    CATALOGUE_DOMAIN_NAME VARCHAR2(128) NULL,    -- 云类型名称
    DOMAIN_CODE VARCHAR2(64) NULL,               -- 云平台编码
    DOMAIN_NAME VARCHAR2(128) NULL,              -- 云平台名称
    REGION_ID NUMBER(19,0) NULL,                 -- 资源池ID
    REGION_CODE VARCHAR2(64) NULL,               -- 资源池编码
    REGION_NAME VARCHAR2(128) NULL,              -- 资源池名称
    STATUS VARCHAR2(32) NULL,                    -- 状态
    ORIGIN_NAME VARCHAR2(255) NULL,              -- 原始名称
    ENABLED NUMBER(1,0) DEFAULT 1 NULL,          -- 是否启用
    CREATE_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL,  -- 创建时间
    MODIFY_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NULL,  -- 修改时间
    CONSTRAINT INDEX33565800 PRIMARY KEY (ID)
);

-- 索引
CREATE INDEX IDX_WOC_CQ_WORK_ORDER_ID ON SLGZT.WOC_CONTAINER_QUOTA (WORK_ORDER_ID);
CREATE INDEX IDX_WOC_CQ_BUSINESS_SYSTEM_ID ON SLGZT.WOC_CONTAINER_QUOTA (BUSINESS_SYSTEM_ID);
CREATE INDEX IDX_WOC_CQ_CQ_NAME ON SLGZT.WOC_CONTAINER_QUOTA (CQ_NAME);
```

## 代码结构

### 1. 数据访问层 (DAO)

- **ContainerQuotaDO**: 数据对象，映射数据库表
- **ContainerQuotaMapper**: MyBatis Plus Mapper接口
- **ContainerQuotaDAO**: 数据访问对象，封装复杂查询逻辑

### 2. 服务层 (Service)

- **ContainerQuotaService**: 服务接口
- **ContainerQuotaServiceImpl**: 服务实现类

### 3. 控制器层 (Controller)

- **ContainerQuotaController**: REST API控制器

### 4. 数据传输对象

- **ContainerQuotaDTO**: 数据传输对象
- **ContainerQuotaQuery**: 查询条件对象

### 5. 工具类

- **IdUtils**: ID生成工具类
- **BeanCopyUtils**: Bean拷贝工具类

## API接口

### 分页查询接口

1. **分页查询容器配额**
   - `POST /container/quota/page`
   - 支持的查询条件：
     - cqName: 配额名称（模糊查询）
     - businessSystemId: 业务系统ID
     - businessSystemName: 业务系统名称（模糊查询）
     - workOrderId: 工单ID
     - subOrderId: 子订单ID
     - status: 状态
     - domainCode: 云平台编码
     - domainName: 云平台名称
     - regionCode: 资源池编码
     - regionName: 资源池名称
     - a4Account: 4A账号
     - createTimeStart: 创建时间开始
     - createTimeEnd: 创建时间结束
     - pageNum: 页码
     - pageSize: 页大小

## 主要功能特性

### 1. 数据创建
- 支持从CQModel创建容器配额记录
- 自动生成唯一ID
- 设置创建和修改时间

### 2. 查询功能
- 支持单条记录查询
- 支持批量查询
- 支持分页查询
- 支持多条件组合查询

### 3. 搜索功能
- 配额名称模糊搜索
- 业务系统搜索（ID和名称）
- 支持所有字段的精确匹配查询

### 4. 数据安全
- 使用逻辑删除（ENABLED字段）
- 只提供查询和创建功能，无删除和修改功能

## 使用示例

### 创建容器配额

```java
// 在Service层调用
CQModel cqModel = new CQModel();
// 设置CQModel属性...

ContainerQuotaDTO result = containerQuotaService.createContainerQuota(
    cqModel,
    "WO123456789",      // 工单ID
    "SO123456789",      // 子订单ID
    1001L,              // 业务系统ID
    "测试业务系统"       // 业务系统名称
);
```

### 查询容器配额

```java
// 分页查询
ContainerQuotaQuery query = new ContainerQuotaQuery();
query.setCqName("测试");
query.setBusinessSystemId(1001L);
query.setPageNum(1);
query.setPageSize(10);

PageResult<ContainerQuotaDTO> page = containerQuotaService.queryPage(query);
```

## 单元测试

提供了完整的单元测试类 `ContainerQuotaServiceTest`，覆盖了以下测试场景：

- 创建容器配额记录
- 根据ID查询
- 根据工单ID查询列表
- 根据子订单ID查询
- 分页查询
- 边界条件测试（null值处理）

## 注意事项

1. **只读设计**: Controller层只提供分页查询接口，不提供增删改接口
2. **Service层限制**: Service层只提供增加和查询功能，无删除和修改功能
3. **数据完整性**: 创建时会自动设置必要的系统字段
4. **性能优化**: 在常用查询字段上建立了索引
5. **简化设计**: 移除了不必要的字段（azId、azCode、azName、productType、functionalModule、message、archivedIp）

## 后续扩展

该设计为后续的回调功能预留了接口，当CQModel任务执行成功后，可以通过Service层的创建方法来记录容器配额信息。

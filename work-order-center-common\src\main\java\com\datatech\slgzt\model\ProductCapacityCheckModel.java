package com.datatech.slgzt.model;

import lombok.Data;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 05月06日 13:54:20
 */
@Data
public class ProductCapacityCheckModel {

    //cup核数
    private Integer cpuNum = 0;

    //内存大小GB
    private Integer memorySize = 0;

    /**
     * GPU算力(算力)
     */
    private Integer gpuRatioSize = 0;

    /**
     * GPU显存大小，单位GB
     */
    private Integer gpuVirtualMemorySize = 0;

    /**
     * 物理GPU卡(个)
     */
    private Integer gpuCoreSize = 0;

    /**
     * 虚拟GPU卡(个)
     */
    private Integer gpuVirtualCoreSize = 0;

    //ssd大小GB
    private Integer ssdSize = 0;

    //hdd大小GB
    private Integer hddOrSasSize = 0;

    //eip带宽大小Mb
    private Integer eipBandwidth = 0;

    //eip数量
    private Integer eipNum = 0;

    //nat数量
    private Integer natNum = 0;

    //slb数量
    private Integer slbNum = 0;


}

package com.datatech.slgzt.model.query;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 导出任务查询对象
 */
@Data
public class ExportTaskQuery {
    
    private Integer pageNum;
    
    private Integer pageSize;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 业务类型
     */
    private String businessType;
    
    /**
     * 状态
     */
    private Integer status;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
} 
package com.datatech.slgzt.manager.xieyun;


import com.datatech.slgzt.model.dto.xieyun.QuotaResultDTO;
import com.datatech.slgzt.model.xieyun.XieyunProjectCreateOpm;
import com.datatech.slgzt.model.xieyun.XieyunProjectQuotaOpm;

public interface XieyunProjectManager {

    //创建项目
    String ensureProject(String orgId, XieyunProjectCreateOpm opm);

    String queryProject(String projectName);

    String projectQuota(String orgId, String projectId, String cluster, String nodepool, XieyunProjectQuotaOpm opm);

    QuotaResultDTO selectProjectQuota(String clusterName, String nodePoolName, String orgId, String projectId);
}

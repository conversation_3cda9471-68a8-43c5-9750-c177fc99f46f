package com.datatech.slgzt.controller;

import cn.hutool.core.collection.ListUtil;
import com.datatech.slgzt.convert.VnicWebConvert;
import com.datatech.slgzt.enums.domain.CatalogueDomain;
import com.datatech.slgzt.manager.*;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.business.CmpAppDTO;
import com.datatech.slgzt.model.dto.*;
import com.datatech.slgzt.model.dto.business.BusinessService;
import com.datatech.slgzt.model.query.VnicQuery;
import com.datatech.slgzt.model.req.vnic.VnicOperateReq;
import com.datatech.slgzt.model.req.vnic.VnicPageReq;
import com.datatech.slgzt.model.req.vnic.VnicSaveReq;
import com.datatech.slgzt.model.vo.network.NetworkOrderResult;
import com.datatech.slgzt.model.vo.vnic.VnicVO;
import com.datatech.slgzt.model.vo.vpc.VpcOrderResult;
import com.datatech.slgzt.service.PlatformService;
import com.datatech.slgzt.service.VnicService;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.utils.UuidUtil;
import com.datatech.slgzt.warpper.PageWarppers;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 虚拟网卡控制器
 */
@RestController
@RequestMapping("/vnic")
public class VnicController {

    @Resource
    private VnicManager vnicManager;
    
    @Resource
    private VnicWebConvert vnicWebConvert;

    @Resource
    private ResourceDetailManager resourceDetailManager;

    @Resource
    private VnicService netcardService;

    @Resource
    private BusinessService businessService;

    @Resource
    private TenantManager tenantManager;

    @Resource
    private PlatformService platformService;

    @Resource
    private VpcOrderManager vpcOrderManager;

    @Resource
    private NetworkOrderManager networkOrderManager;
    
    /**
     * 分页查询
     */
    @PostMapping("/page")
    public CommonResult<PageResult<VnicVO>> page(@RequestBody VnicPageReq req) {
        VnicQuery query = vnicWebConvert.convert(req);
        PageResult<VnicDTO> page = vnicManager.page(query);
        return CommonResult.success(PageWarppers.box(page, vnicWebConvert::convert));
    }
    
    /**
     * 保存虚拟网卡
     */
    @PostMapping("/save")
    public CommonResult<Void> save(@RequestBody VnicSaveReq req) {
        Precondition.checkArgument(req.getCatalogueDomainCode(),"云类型不能为空");
        Precondition.checkArgument(req.getDomainCode(),"云平台不能为空");
        Precondition.checkArgument(req.getRegionCode(),"资源池不能为空");
        Precondition.checkArgument(req.getAzCode(),"可用区不能为空");
        Precondition.checkArgument(req.getVpcId(),"vpc/网络不能为空");
        Precondition.checkArgument(req.getSubnetId(),"子网id不能为空");
        Long businessSysId = null;
        String businessSysName = null;
        if (req.getVpcId().startsWith("vpc")) {
            List<VpcOrderResult> vpcOrderResults = vpcOrderManager.listByIdList(Arrays.asList(req.getVpcId()));
            Precondition.checkArgument(vpcOrderResults, "找不到对应的vpc");
            VpcOrderResult vpcOrderResult = vpcOrderResults.get(0);
            businessSysId = vpcOrderResult.getBusinessSysId();
            businessSysName = vpcOrderResult.getBusinessSysName();
        } else if (req.getVpcId().startsWith("net")) {
            List<NetworkOrderResult> networkOrderResults = networkOrderManager.selectNetworkRecoveryList(Arrays.asList(req.getVpcId()));
            Precondition.checkArgument(networkOrderResults, "找不到对应的网络");
            NetworkOrderResult networkOrderResult = networkOrderResults.get(0);
            businessSysId = networkOrderResult.getBusinessSysId();
            businessSysName = networkOrderResult.getBusinessSysName();
        }
        Precondition.checkArgument(businessSysId,"业务系统id不能为空");
        Precondition.checkArgument(businessSysName,"业务系统名称不能为空");
        req.setBusinessSystemId(String.valueOf(businessSysId));
        req.setBusinessSystemName(businessSysName);
        // 1. 拿底层租户id
        CmpAppDTO cmpAppDTO = businessService.getById(Long.valueOf(req.getBusinessSystemId()));
        TenantDTO tenantDTO = tenantManager.getById(cmpAppDTO.getTenantId());
        Long tenantId = platformService.getOrCreateTenantId(tenantDTO.getBillId(), req.getRegionCode());
        // 2. 组装NetcardCreateOpm
        NetcardCreateOpm opm = new NetcardCreateOpm();
        opm.setTenantId(tenantId);
        opm.setRegionCode(req.getRegionCode());
        opm.setOrderId(UuidUtil.generateId());
        opm.setName(req.getVnicName());
        opm.setDescription(req.getDescription());
        opm.setSubnetId(req.getSubnetId());
        opm.setAzCode(req.getAzCode());
        opm.setType(req.getType());
        opm.setNetName(req.getNetName());
        // 3. 调用底层接口
        String vnicId = netcardService.createNetcard(opm);
        // 4. 正常保存
        req.setVnicId(vnicId);
        VnicDTO dto = vnicWebConvert.convert(req);
        vnicManager.save(dto);
        return CommonResult.success(null);
    }
    
    /**
     * 删除虚拟网卡
     */
    @GetMapping("/delete")
    public CommonResult<Void> delete(@RequestParam String id) {
        VnicDTO vnicDTO = vnicManager.getById(id);
        Precondition.checkArgument(Objects.isNull(vnicDTO.getVmId()),"虚拟网卡存在未解绑的云主机：" + vnicDTO.getVmName());
        // 1. 拿底层租户id
        CmpAppDTO cmpAppDTO = businessService.getById(Long.valueOf(vnicDTO.getBusinessSystemId()));
        TenantDTO tenantDTO = tenantManager.getById(cmpAppDTO.getTenantId());
        Long tenantId = platformService.getOrCreateTenantId(tenantDTO.getBillId(), vnicDTO.getRegionCode());
        // 2. 组装NetcardDeleteOpm
        NetcardDeleteOpm opm = new NetcardDeleteOpm();
        opm.setTenantId(tenantId);
        opm.setRegionCode(vnicDTO.getRegionCode());
        opm.setOrderId(id);
        opm.setAzCode(vnicDTO.getAzCode());
        opm.setInstanceId(vnicDTO.getVnicId());
        // 3. 调用底层接口
        netcardService.deleteNetcard(opm);
        // 4. 删除
        vnicManager.delete(id);
        return CommonResult.success(null);
    }
    
    /**
     * 获取虚拟网卡详情
     */
    @GetMapping("/detail")
    public CommonResult<VnicVO> detail(@RequestParam String id) {
        VnicDTO dto = vnicManager.getById(id);
        return CommonResult.success(vnicWebConvert.convert(dto));
    }

    /**
     * 虚拟网卡绑定、解绑云主机
     */
    @PostMapping("/operate")
    public CommonResult<String> operate(@RequestBody VnicOperateReq req) {
        // 云主机id
        String deviceId = req.getDeviceId();
        VnicDTO vnicDTO = vnicManager.getById(req.getVnicOrderId());
        ResourceDetailDTO resourceDetailDTO = resourceDetailManager.getByDeviceId(deviceId);
        Precondition.checkArgument(resourceDetailDTO.getVpcId(), "主机vpc/网络不能为空");
        Precondition.checkArgument(ListUtil.toList(resourceDetailDTO.getVpcId().split(",")).contains(vnicDTO.getVpcId()),"虚拟网卡与云主机不在同一vpc/网络下，无法操作");
        if (CatalogueDomain.HUAWEI.getCode().equals(vnicDTO.getDomainCode())) {
            Precondition.checkArgument(!Objects.equals(vnicDTO.getSubnetId(), resourceDetailDTO.getSubnetId()), "云主机和虚拟网卡的子网id相同，无法操作");
        } else {
            Precondition.checkArgument(vnicDTO.getIpAddress(), "该虚拟网卡未完成创建，暂无法进行操作");
        }
        // 1. 拿底层租户id
        CmpAppDTO cmpAppDTO = businessService.getById(Long.valueOf(vnicDTO.getBusinessSystemId()));
        TenantDTO tenantDTO = tenantManager.getById(cmpAppDTO.getTenantId());
        Long tenantId = platformService.getOrCreateTenantId(tenantDTO.getBillId(), vnicDTO.getRegionCode());
        // 2. 组装NetcardOperateOpm
        NetcardOperateOpm opm = new NetcardOperateOpm();
        opm.setOperateType(req.getOperateType());
        opm.setTenantId(tenantId);
        opm.setRegionCode(vnicDTO.getRegionCode());
        opm.setOrderId(req.getVnicOrderId());
        opm.setAzCode(vnicDTO.getAzCode());
        opm.setInstanceId(vnicDTO.getVnicId());
        opm.setServerId(deviceId);
        // 3. 调用底层接口
        Boolean flag = netcardService.operateNetcard(opm);
        // 4. 更新网卡与云主机的绑定信息（如果返回不是SUCCESS，则放在kafka消费消息里处理，否则直接处理）
        if (flag) {
            if ("BIND".equals(opm.getOperateType())) {
                //更新网卡信息
                vnicDTO.setVmId(deviceId);
                vnicDTO.setVmName(resourceDetailDTO.getDeviceName());
                vnicManager.update(vnicDTO);
                //更新云主机信息
                String ip = resourceDetailDTO.getIp();
                if (StringUtils.isNotBlank(ip)) {
                    List<String> list = ListUtil.toList(ip.split(","));
                    list.add(vnicDTO.getIpAddress());
                    list = list.stream().distinct().collect(Collectors.toList());
                    ip = String.join(",", list);
                }
                resourceDetailDTO.setIp(ip);
                resourceDetailManager.updateById(resourceDetailDTO);
            } else if ("UNBIND".equals(opm.getOperateType())) {
                //更新网卡信息
                vnicDTO.setVmId(null);
                vnicDTO.setVmName(null);
                vnicManager.updateVmById(vnicDTO);
                //更新云主机信息
                String ip = resourceDetailDTO.getIp();
                ArrayList<String> list = new ArrayList<>(ListUtil.toList(ip.split(",")));
                list.remove(vnicDTO.getIpAddress());
                resourceDetailDTO.setIp(String.join(",", list));
                resourceDetailManager.updateById(resourceDetailDTO);
            }
        }
        return CommonResult.success(flag? "虚拟网卡操作成功" : "虚拟网卡操作中");
    }

    /**
     * 变更虚拟网卡
     */
    @PostMapping("/update")
    public CommonResult<Void> update(@RequestBody VnicSaveReq req) {
        Precondition.checkArgument(req.getId(),"虚拟网卡主键id不能为空");
        Precondition.checkArgument(req.getCatalogueDomainCode(),"云类型不能为空");
        Precondition.checkArgument(req.getDomainCode(),"云平台不能为空");
        Precondition.checkArgument(req.getRegionCode(),"资源池不能为空");
        Precondition.checkArgument(req.getAzCode(),"可用区不能为空");
        Precondition.checkArgument(req.getVpcId(),"vpc/网络不能为空");
        Precondition.checkArgument(req.getSubnetId(),"子网id不能为空");
        Long businessSysId = null;
        String businessSysName = null;
        if (req.getVpcId().startsWith("vpc")) {
            List<VpcOrderResult> vpcOrderResults = vpcOrderManager.listByIdList(Arrays.asList(req.getVpcId()));
            Precondition.checkArgument(vpcOrderResults, "找不到对应的vpc");
            VpcOrderResult vpcOrderResult = vpcOrderResults.get(0);
            businessSysId = vpcOrderResult.getBusinessSysId();
            businessSysName = vpcOrderResult.getBusinessSysName();
        } else if (req.getVpcId().startsWith("net")) {
            List<NetworkOrderResult> networkOrderResults = networkOrderManager.selectNetworkRecoveryList(Arrays.asList(req.getVpcId()));
            Precondition.checkArgument(networkOrderResults, "找不到对应的网络");
            NetworkOrderResult networkOrderResult = networkOrderResults.get(0);
            businessSysId = networkOrderResult.getBusinessSysId();
            businessSysName = networkOrderResult.getBusinessSysName();
        }
        Precondition.checkArgument(businessSysId,"业务系统id不能为空");
        Precondition.checkArgument(businessSysName,"业务系统名称不能为空");
        req.setBusinessSystemId(String.valueOf(businessSysId));
        req.setBusinessSystemName(businessSysName);
        // 1. 拿底层租户id
        // 先删除资源和工单中心网卡信息
        VnicDTO vnicDTO = vnicManager.getById(req.getId());
        CmpAppDTO cmpAppDTO = businessService.getById(Long.valueOf(vnicDTO.getBusinessSystemId()));
        TenantDTO tenantDTO = tenantManager.getById(cmpAppDTO.getTenantId());
        Long tenantId = platformService.getOrCreateTenantId(tenantDTO.getBillId(), vnicDTO.getRegionCode());
        // 2. 组装NetcardDeleteOpm
        NetcardDeleteOpm deleteOpm = new NetcardDeleteOpm();
        deleteOpm.setTenantId(tenantId);
        deleteOpm.setRegionCode(vnicDTO.getRegionCode());
        deleteOpm.setOrderId(req.getId());
        deleteOpm.setAzCode(vnicDTO.getAzCode());
        deleteOpm.setInstanceId(vnicDTO.getVnicId());
        // 3. 调用底层接口删除
        netcardService.deleteNetcard(deleteOpm);
        // 4. 组装NetcardCreateOpm
        CmpAppDTO cmpAppDTO1 = businessService.getById(Long.valueOf(req.getBusinessSystemId()));
        TenantDTO tenantDTO1 = tenantManager.getById(cmpAppDTO1.getTenantId());
        Long tenantId1 = platformService.getOrCreateTenantId(tenantDTO1.getBillId(), req.getRegionCode());
        NetcardCreateOpm createOpm = new NetcardCreateOpm();
        createOpm.setTenantId(tenantId1);
        createOpm.setRegionCode(req.getRegionCode());
        createOpm.setOrderId(UuidUtil.generateId());
        createOpm.setName(req.getVnicName());
        createOpm.setDescription(req.getDescription());
        createOpm.setSubnetId(req.getSubnetId());
        createOpm.setAzCode(req.getAzCode());
        createOpm.setType(req.getType());
        createOpm.setNetName(req.getNetName());
        // 5. 调用底层接口
        String vnicId = netcardService.createNetcard(createOpm);
        // 6. 更新
        req.setVnicId(vnicId);
        VnicDTO dto = vnicWebConvert.convert(req);
        vnicManager.update(dto);
        return CommonResult.success(null);
    }
} 
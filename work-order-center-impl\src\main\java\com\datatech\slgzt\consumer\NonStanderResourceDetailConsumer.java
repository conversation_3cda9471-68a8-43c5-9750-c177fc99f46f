package com.datatech.slgzt.consumer;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.convert.StandardResourceDetailConsumerConvert;
import com.datatech.slgzt.dao.mapper.ObsOpenTaskMapper;
import com.datatech.slgzt.dao.mapper.VmMapper;
import com.datatech.slgzt.dao.mapper.network.NetworkIpAddressMapper;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.ResOpenEnum;
import com.datatech.slgzt.enums.SourceTypeEnum;
import com.datatech.slgzt.enums.domain.CatalogueDomain;
import com.datatech.slgzt.manager.*;
import com.datatech.slgzt.model.dto.*;
import com.datatech.slgzt.model.nostander.*;
import com.datatech.slgzt.model.query.NonStanderWorkOrderProductQuery;
import com.datatech.slgzt.model.query.StandardWorkOrderProductQuery;
import com.datatech.slgzt.service.UserService;
import com.datatech.slgzt.service.external.ExternalResourceDetailService;
import com.datatech.slgzt.utils.DateUtils;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.StreamUtils;
import com.google.common.base.Joiner;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月12日 15:20:59
 */
@Slf4j
@Service
public class NonStanderResourceDetailConsumer {


    @Resource
    private NonStanderWorkOrderManager nonStanderWorkOrderManager;

    @Resource
    private ResourceDetailManager resourceDetailManager;

    @Resource
    private VmMapper vmMapper;

    @Resource
    private CpcProjectManager cpcProjectManager;

    @Resource
    private NonStanderWorkOrderProductManager productManager;

    @Resource
    private NetworkIpAddressMapper networkIpAddressMapper;

    @Resource
    private NonStanderResourceDetailConsumer converter;

    public void consumeResourceMessage(List<ResourceDetailDTO> list) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        //处理集合封装资源详情
        Map<Long, ResourceDetailDTO> gooodId2IdentityMap = StreamUtils.toMap(list, ResourceDetailDTO::getGoodsOrderId);
        List<ResourceDetailDTO> needInsertList = new ArrayList<>();
        log.info("监听任务消息: 标准工单对象列表={}", JSONObject.toJSONString(gooodId2IdentityMap));
        List<NonStanderWorkOrderProductDTO> productDTOS = productManager.list(new NonStanderWorkOrderProductQuery()
                .setSubOrderIds(gooodId2IdentityMap.keySet()));
        if (CollectionUtil.isNotEmpty(productDTOS)) {
            productDTOS.forEach(productDTO -> {
                //去detail表中找到对应的数据 如果已经存在则不处理
                if (resourceDetailManager.getByGoodsOrderId(productDTO.getSubOrderId()) != null) {
                    log.info("kafka资源开通回调，待更新detail表数据已存在：产品表的 subOrderId={}", productDTO.getSubOrderId());
                    return;
                }
                ResourceDetailDTO detail = gooodId2IdentityMap.get(productDTO.getSubOrderId());
                NonStanderWorkOrderDTO orderDTO = nonStanderWorkOrderManager.getById(productDTO.getWorkOrderId());
                if (Objects.nonNull(orderDTO)) {
                    detail.setSourceType(SourceTypeEnum.NON_STANDARD.getPrefix());
                    detail.setType(productDTO.getProductType());
                    detail.setOrderId(orderDTO.getId());
                    detail.setOrderCode(orderDTO.getOrderCode());
                    detail.setTenantId(orderDTO.getTenantId());
                    detail.setTenantName(orderDTO.getTenantName());
                    detail.setBusinessSysName(orderDTO.getBusinessSystemName());
                    detail.setApplyUserId(orderDTO.getCreatedBy());
                    detail.setApplyUserName(orderDTO.getCreatedUserName());
                    detail.setCreateTime(LocalDateTime.now());
                    detail.setStatus(1);
                    detail.setBillId(orderDTO.getBillId());
                    // detail.setCloudPlatform(orderDTO.getDomainName());
                    detail.setBusinessSysId(orderDTO.getBusinessSystemId());
                    // detail.setDomainCode(orderDTO.getDomainCode());
                    // detail.setDomainName(orderDTO.getDomainName());
                    detail.setModuleId(orderDTO.getModuleId());
                    detail.setModuleName(orderDTO.getModuleName());
                }
                switch (ProductTypeEnum.getByCode(detail.getType())) {
                    case ECS:
                    case GCS:
                        coverEcs(detail, productDTO, needInsertList);
                        break;
                    default:
                }
                needInsertList.add(detail);

            });
        }

        log.info("kafka资源开通回调，待更新detail表数据：{}", list);
        needInsertList.forEach(o -> {
            try {
                resourceDetailManager.saveResourceDetail(o);
            } catch (Exception e) {
                //这里最好入库对象 做补偿
                log.error("kafka资源开通回调，待更新detail表数据异常：{}", JSON.toJSONString(o), e);
            }
        });
    }

    public void coverEcs(ResourceDetailDTO detail,
                         NonStanderWorkOrderProductDTO productDTO,
                         List<ResourceDetailDTO> needInsertList) {
        String propertySnapshot = productDTO.getPropertySnapshot();
        EcsModel escModel = JSON.parseObject(propertySnapshot, EcsModel.class);
        detail.setDomainCode(escModel.getDomainCode());
        detail.setDomainName(escModel.getDomainName());
        updateNetwork(escModel.getPlaneNetworkModel(), detail.getDeviceId());
        List<PlaneNetworkModel> planeNetworkModel = escModel.getPlaneNetworkModel();
        log.debug("coverEcs list:{}", JSONObject.toJSONString(planeNetworkModel));
        //如果domainCode创新池查询项目名称
        if (CatalogueDomain.INNOVATION.getCode().equals(detail.getDomainCode())) {
            String projectId = vmMapper.getByDeviceId(detail.getDeviceId());
            //获取项目名称
            String projectName = cpcProjectManager.getById(projectId);
            //detail中已知任务中心会传值过来的字段,goodsOrderId,deviceName,eip,bandWidth,spec,deviceId,resourcePoolId,resourcePoolName,deviceStatus,resourceApplyTime
            detail.setProjectName(projectName);
        }
        detail.setVpcId(Joiner.on(",").skipNulls().join(StreamUtils.mapArrayFilterNull(planeNetworkModel, PlaneNetworkModel::getId)));
        detail.setVpcName(Joiner.on(",").skipNulls().join(StreamUtils.mapArrayFilterNull(planeNetworkModel, PlaneNetworkModel::getName)));
        //子网可能出现一个以上的情况，用|分隔 每个网络模型的子网id用逗号分隔
        String subnetId = planeNetworkModel.stream()
                .map(networkModel ->
                        Joiner.on(",").skipNulls().join(
                                StreamUtils.mapArrayFilterNull(networkModel.getSubnets(), PlaneNetworkModel.Subnet::getSubnetId)
                        )
                )
                .filter(s -> !s.isEmpty())  // 过滤掉空字符串
                .collect(Collectors.joining("|"));
        String subnetName = planeNetworkModel.stream()
                .map(networkModel ->
                        Joiner.on(",").skipNulls().join(
                                StreamUtils.mapArrayFilterNull(networkModel.getSubnets(), PlaneNetworkModel.Subnet::getSubnetName)
                        )
                )
                .filter(s ->!s.isEmpty())  // 过滤掉空字符串
                .collect(Collectors.joining("|"));
        detail.setSubnetId(subnetId);
        detail.setSubnetName(subnetName);
        detail.setCloudPlatform(escModel.getDomainName());
        detail.setApplyTime(escModel.getApplyTime());//已转换成one_month
        detail.setResourcePoolId(escModel.getRegionId().toString());
        detail.setResourcePoolCode(escModel.getRegionCode());
        detail.setResourcePoolName(escModel.getRegionName());
        detail.setEffectiveTime(LocalDateTime.now());
        detail.setExpireTime(DateUtils.processGoodsExpireTime(LocalDateTime.now(), escModel.getApplyTime()));
        detail.setNetworkModelSnapshot(JSON.toJSONString(escModel.getPlaneNetworkModel()));
        //修改下带宽适配问题
        detail.setBandWidth(bandWidth(detail.getBandWidth()));
        //evs
        if (StringUtils.isNotBlank(detail.getVolumeId())) {
            List<String> list = Arrays.asList(detail.getVolumeId().split(","));
            for (int i = 0; i < list.size(); i++) {
                ResourceDetailDTO detailDTO = getResourceDetailDTO(detail, escModel);
                detailDTO.setType(ProductTypeEnum.EVS.getCode());
                detailDTO.setGoodsOrderId(IdUtil.getSnowflake().nextId());
                detailDTO.setDeviceId(list.get(i));
                detailDTO.setVolumeId(list.get(i));
                detailDTO.setDataDisk(detail.getDataDisk().split(",")[i]);
                detailDTO.setDeviceStatus("USED");
                needInsertList.add(detailDTO);
            }
        }
        //eip
        if (StringUtils.isNotBlank(detail.getEipId())) {
            ResourceDetailDTO detailDTO = getResourceDetailDTO(detail, escModel);
            detailDTO.setDeviceName(detail.getDeviceName() + "-eip");
            detailDTO.setRelatedDeviceId(detail.getDeviceId());
            detailDTO.setRelatedDeviceName(detail.getDeviceName());
            detailDTO.setRelatedDeviceType(detail.getType());
            detailDTO.setType(ProductTypeEnum.EIP.getCode());
            detailDTO.setGoodsOrderId(IdUtil.getSnowflake().nextId());
            detailDTO.setDeviceId(detail.getEipId());
            detailDTO.setEipId(detail.getEipId());
            detailDTO.setEip(detail.getEip());
            detailDTO.setBandWidth(detail.getBandWidth());
            detailDTO.setDeviceStatus("ACTIVE");
            needInsertList.add(detailDTO);
        }
    }

    private void updateNetwork(List<PlaneNetworkModel> list, String deviceId) {
        log.debug("updateNetwork deviceId:{},list:{}", deviceId,JSONObject.toJSONString(list));
        for (PlaneNetworkModel planeNetworkModel : list) {
            for (PlaneNetworkModel.Subnet subnet : planeNetworkModel.getSubnets()) {
                List<IpAddress> ipAddresses = networkIpAddressMapper.selectIpBySubnetId(deviceId, subnet.getSubnetId());
                for (IpAddress ipAddress : ipAddresses) {
                    if (ipAddress.getType().equals("MANAGE")){
                        if (ipAddress.getIp().length() <= 18 && ipAddress.getIp().contains(".")) {
                            subnet.setManageIpv4(ipAddress.getIp());
                        } else {
                            subnet.setManageIpv6(ipAddress.getIp());
                        }
                    }else if (ipAddress.getType().equals("BUSINESS")){
                        if (ipAddress.getIp().length() <= 18 && ipAddress.getIp().contains(".")) {
                            subnet.setBusinessIpv4(ipAddress.getIp());
                        } else {
                            subnet.setBusinessIpv6(ipAddress.getIp());
                        }
                    }else {
                        if (ipAddress.getIp().length() <= 18 && ipAddress.getIp().contains(".")) {
                            subnet.setAddressIpv4(ipAddress.getIp());
                        } else {
                            subnet.setAddressIpv6(ipAddress.getIp());
                        }
                    }
                }
            }
        }
        log.debug("updateNetwork end list:{}",JSONObject.toJSONString(list));
    }


    public ResourceDetailDTO getResourceDetailDTO(ResourceDetailDTO detail, EcsModel escModel) {
        ResourceDetailDTO detailDTO = new ResourceDetailDTO();
        detailDTO.setOrderId(detail.getOrderId());
        detail.setSourceType(SourceTypeEnum.NON_STANDARD.getPrefix());
        detailDTO.setOrderCode(detail.getOrderCode());
        detailDTO.setTenantId(detail.getTenantId());
        detailDTO.setTenantName(detail.getTenantName());
        detailDTO.setBusinessSysName(detail.getBusinessSysName());
        detailDTO.setApplyUserId(detail.getApplyUserId());
        detailDTO.setApplyUserName(detail.getApplyUserName());
        detailDTO.setCreateTime(LocalDateTime.now());
        detailDTO.setStatus(1);
        detailDTO.setBillId(detail.getBillId());
        detailDTO.setCloudPlatform(detail.getDomainName());
        detailDTO.setBusinessSysId(detail.getBusinessSysId());
        detailDTO.setDomainCode(detail.getDomainCode());
        detailDTO.setDomainName(detail.getDomainName());
        detailDTO.setModuleId(detail.getModuleId());
        detailDTO.setModuleName(detail.getModuleName());
        detailDTO.setApplyTime(escModel.getApplyTime());
        detailDTO.setResourcePoolId(escModel.getRegionId().toString());
        detailDTO.setResourcePoolCode(escModel.getRegionCode());
        detailDTO.setResourcePoolName(escModel.getRegionName());
        detailDTO.setEffectiveTime(LocalDateTime.now());
        detailDTO.setExpireTime(DateUtils.processGoodsExpireTime(LocalDateTime.now(), escModel.getApplyTime()));
        detailDTO.setResourceApplyTime(detail.getResourceApplyTime());
        detailDTO.setAzCode(detail.getAzCode());
        detailDTO.setAzName(detail.getAzName());
        detailDTO.setAzId(detail.getAzId());
        detailDTO.setEcsName(detail.getDeviceName() + "(" + detail.getIp() + ")");
        detailDTO.setMountOrNot("是");
        detailDTO.setVmId(detail.getDeviceId());
        return detailDTO;
    }


    private String bandWidth(String bandWidth) {
        if (ObjNullUtils.isNull(bandWidth)) {
            return null;
        }

        String trimmedBandWidth = bandWidth.trim();
        // 如果已经以Mbps或kbps结尾，则直接返回原值
        if (trimmedBandWidth.toLowerCase().endsWith("mbps")) {
            return trimmedBandWidth;
        }

        // 尝试解析为数字，如果是纯数字则添加单位
        return bandWidth + "Mbps";
    }

}

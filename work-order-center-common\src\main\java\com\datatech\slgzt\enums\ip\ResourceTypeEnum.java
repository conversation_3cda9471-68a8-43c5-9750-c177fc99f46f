package com.datatech.slgzt.enums.ip;

import org.springframework.util.StringUtils;

/**
 * 资源类型
 */
public enum ResourceTypeEnum {

    /**
     * 资源类型, abbreviate长度不可超过10char。
     */
    VM("ECS", "云主机", "ecs"),
    GPU("GPU", "GPU云主机", "gcs"),
    VM_PAAS("VM_PAAS", "PAAS云主机","ecs"),
    PRIVATE_LINK("PRIVATE_LINK", "对等连接","p2p"),
    VOLUME("VOLUME", "云硬盘", "evs"),
    SECURITY_GROUP("SECURITY_GROUP", "安全组", "security"),
    SECURITY_GROUP_RULE("SECURITY_GROUP_RULE", "安全组规则", "s_rule"),
    SUBNET("SUBNET", "子网", "sub"),
    NETWORK("NETWORK", "网络", "net"),
    VPC("VPC", "虚拟私有云", "vpc"),
    EIP("EIP", "弹性IP", "eip"),
    IP("IP", "公网IP", "fip"),
    ROUTER("ROUTER", "路由", "router"),
    NAT("NAT", "nat", "nat"),
    SNAT("SNAT", "snat", "snat"),
    DNAT("DNAT", "dnat", "dnat"),
    NAT_RULE("NAT_RULE", "nat规则", "nat_rule"),
    TENANT("TENANT", "租户", "tenant"),
    PROJECT("PROJECT", "项目", "pro"),
    IMAGE("IMAGE", "镜像", "image"),
    PORT("PORT", "虚拟网卡", "nic"),
    FLAVOR("FLAVOR", "规格", "flavor"),
    MC_PORT("PORT", "云端口", "port"),
    MC_PORT_ACCESS("MC_PORT_ACCESS", "云端口接入", "port_access"),
    PAAS_CLOUD_PORT("PAAS_PORT","PAAS云端口","port"),
    SLB("SLB", "负载均衡", "slb"),
    SLB_ACCESS_CONTROL("SLB_ACCESSCONTROL", "负载均衡访问控制", "slb_acl"),
    SLB_CERTIFICATE("SLB_CERTIFICATE", "负载均衡证书", "slb_ctc"),
    SLB_RULE("SLB_RULE", "负载均衡规则", "slb_rule"),
    SLB_LISTENER("SLB_LISTENER", "负载均衡监听", "slb_lsn"),
    SLB_SERVER_GROUP("SLB_SERVERGROUP", "负载均衡服务组", "slb_srg"),
    SLB_MEMBER("SLB_MEMBER","负载均衡服务成员","slb_mem"),
    VM_GROUP("VM_GROUP", "云主机组", "ecs_g"),
    OBS("OBS", "对象存储", "obs"),
    OBS_BUCKET("OBS_BUCKET", "对象存储桶", "obs_buc"),
    RDS_MYSQL("RDS_MYSQL", "MySQL数据库", "rds_mysql"),
    VPN("VPN", "虚拟专有网络", "vpn"),
    NBU("NBU","备份服务","nbu"),
    NBU_VOLUME("NBU_VOLUME", "云硬盘备份", "vbs"),
    NBU_VM("NBU_VM", "云主机备份", "cbr"),
    NBU_JOB("NBU_JOB","云备份策略任务","nbu_job"),
    ENI("ENI","辅助网卡","eni"),
    NET_CARD("NET_CARD","虚拟网卡","net_card"),
    STORAGE_POOL("STORAGE_POOL","存储池","storage_pool"),
    HOST_AGGREGATE("HOST_AGGREGATE","主机集群","host_aggregate"),
    CLUSTER("CLUSTER","集群","cluster"),
    HOST("HOST","宿主机","host"),
    VIP("VIP","虚拟ip","vip"),
    HOST_FILE("HOST_FILE", "云主机文件系统", "host_file"),

    NIC("NIC", "ip地址", "nic"),

    PHY_GPU_CAPACITY("PHY_GPU_CAPACITY","物理主机GPU容量","phy_gpu_capacity"),
    POOL("POOL","资源池","pool"),
    DVSWITCH("DVSWITCH","分布式交换机","dvswitch"),
    REGION("REGION","云区域","region"),
    AZ("AZ","可用区","az"),
    HOST_GROUP("HOST_GROUP","集群","host_group"),
    HOST_PORT("HOST_PORT","主机端口","host_port"),
    HOST_BOND_PORT("HOST_BOND_PORT","主机端口","host_bond_port"),
    BARE_METAL("BARE_METAL","裸金属","bare_metal"),
    EIPV6("EIPV6", "弹性公网IPV6", "eipv6"),
    NAMESPACE("NAMESPACE", "容器空间", "namespace"),
    RDS_PGSQL("RDS_PGSQL", "PgSQL数据库", "rds_pgsql"),
    IPV6("IPV6", "IPV6", "ipv6"),

    REDIS("REDIS", "REDIS", "redis"),
    NAS("NAS", "NAS", "nas"),
    POSTGRESQL("POSTGRESQL", "postgreSQL单例", "postgreSQL"),
    NETWORKAREAS("NETWORKAREAS", "网络域", "NetworkAreas"),
    IPPOOL("IPPOOL", "ip池", "IpPool"),
    APP("APP", "应用", "App"),
    COMPONENT("COMPONENT", "组件", "Component"),
    SERVICE("SERVICE", "服务", "Service"),
    INGRESS("INGRESS", "Ingress", "Ingress"),
    CONTAINER("CONTAINER", "容器", "Container"),
    POD("POD", "Pod", "Pod"),
    RES_USAGE("RES_USAGE", "用量", "res_usage"),
    POOLCAPACITY("POOLCAPACITY", "资源池当前容量", "PoolCapacity"),
    TASK("TASK", "任务", "task"),
    ;

    private final String code;
    private final String desc;
    private final String abbreviate;

    ResourceTypeEnum(String code, String desc, String abbreviate) {
        this.code = code;
        this.desc = desc;
        this.abbreviate = abbreviate;
    }

    /**
     * 通过code获取enum
     *
     * @param code
     * @return
     */
    public static ResourceTypeEnum getByCode(String code) {
        if (!StringUtils.isEmpty(code)) {
            for (ResourceTypeEnum value : values()) {
                if (value.getCode().equals(code)) {
                    return value;
                }
            }
        }
        return null;
    }

    /**
     * Getter method for property <tt>code</tt>.
     *
     * @return property value of code
     */
    public String getCode() {
        return code;
    }

    /**
     * Getter method for property <tt>desc</tt>.
     *
     * @return property value of desc
     */
    public String getDesc() {
        return desc;
    }

    public String getAbbreviate(){
        return abbreviate;
    }
}

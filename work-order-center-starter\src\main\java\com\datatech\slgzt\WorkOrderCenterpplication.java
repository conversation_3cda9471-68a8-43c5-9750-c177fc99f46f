package com.datatech.slgzt;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.batch.core.configuration.annotation.EnableBatchProcessing;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableConfigurationProperties
@SpringBootApplication(exclude = {org.activiti.spring.boot.SecurityAutoConfiguration.class})
@MapperScan(value = {"com.datatech.slgzt.dao.mapper"})
@EnableScheduling
@EnableAsync
@EnableDiscoveryClient
@EnableFeignClients
@EnableBatchProcessing
public class WorkOrderCenterpplication {


    public static void main(String[] args) {
        System.setProperty("java.security.auth.login.config", "classpath:client_jaas.conf");
        SpringApplication.run(WorkOrderCenterpplication.class, args);
    }

}

package com.datatech.slgzt.consumer;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.enums.ActivitiStatusEnum;
import com.datatech.slgzt.enums.ChangeTypeProductStatusEnum;
import com.datatech.slgzt.enums.GoodsTypeEnum;
import com.datatech.slgzt.enums.VmOperationEnum;
import com.datatech.slgzt.manager.ChangeWorkOrderProductManager;
import com.datatech.slgzt.manager.ResourceDetailManager;
import com.datatech.slgzt.manager.WocSecurityGroupManager;
import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import com.datatech.slgzt.model.dto.WocSecurityGroupDTO;
import com.datatech.slgzt.model.vo.callback.TaskVO;
import com.datatech.slgzt.service.change.ChangeWorkOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @program: workordercenterproject
 * @description: 操作消费
 * @author: LK
 * @create: 2025-04-30 11:34
 **/
@Slf4j
@Service
public class OperateResourceConsumer {

    @Resource
    private ResourceDetailManager resourceDetailManager;

    @Resource
    private ChangeWorkOrderService changeWorkOrderService;

    @Resource
    private WocSecurityGroupManager securityGroupManager;

    @Resource
    private ChangeWorkOrderProductManager changeProductManager;

    @Resource
    private WocSecurityGroupManager wocSecurityGroupManager;

    private final static String RESOURCE_OPERATE_TOPIC = "prod_resource_operate_topic";

    /**
     * 监听资源操作消息，更新资源状态
     *
     * @param consumerRecordList
     */
    @KafkaListener(groupId = "prod-work-order-resource-operate-group-standard", topics = {RESOURCE_OPERATE_TOPIC})
    public void consumeResourceOperateMessage(List<ConsumerRecord<String, String>> consumerRecordList) {
        log.info("资源操作监听任务消息: {}条", consumerRecordList.size());
        for (ConsumerRecord<String, String> record : consumerRecordList) {
            TaskVO taskVO = JSONObject.parseObject(record.value(), TaskVO.class);
            String resourceType = taskVO.getResourceType();
            String operateType = taskVO.getOperationType();
            String resourceId = taskVO.getResourceId();
            String attachResourceId = taskVO.getAttachResourceId();
            ResourceDetailDTO detail = resourceDetailManager.getByDeviceId(resourceId);
            log.info("资源操作监听任务消息 taskVO: {}", JSONObject.toJSONString(taskVO));
            if (Objects.nonNull(detail)) {
                if ("ERROR".equalsIgnoreCase(taskVO.getStatus())) {
                    // 失败，还原状态
                    if (GoodsTypeEnum.ECS.getAlias().equals(resourceType)) {
                        if (VmOperationEnum.START.getCode().equals(operateType)) {
                            detail.setDeviceStatus("STOPED");
                            resourceDetailManager.updateById(detail);
                            List<String> workOrderIds = changeProductManager.selectWorkOrderIdsByResourceDetailId(detail.getId(),
                                    ActivitiStatusEnum.RESOURCE_CHANGE.getNode());
                            if (!CollectionUtils.isEmpty(workOrderIds)) {
                                // 1.变更成功，2.detailId，3.workOrder变更中
                                changeProductManager.updateVmOperationFailMessage("自动开机失败：" + taskVO.getMessage(), detail.getId(),
                                        ChangeTypeProductStatusEnum.CHANGE_SUCCESS.getCode(), workOrderIds);
                            }
                        } else if (VmOperationEnum.REBOOT.getCode().equals(operateType) || VmOperationEnum.STOP.getCode().equals(operateType)) {
                            detail.setDeviceStatus("RUNING");
                            resourceDetailManager.updateById(detail);
                        }
                    }
                } else {
                    boolean needStart = false;
                    if (GoodsTypeEnum.ECS.getAlias().equals(resourceType)) {
                        //根据操作类型更新云主机状态
                        if (VmOperationEnum.START.getCode().equals(operateType) || VmOperationEnum.REBOOT.getCode().equals(operateType)) {
                            //启动和重启，接收到成功消息则更新为RUNING
                            detail.setDeviceStatus("RUNING");
                            needStart = true;
                        } else if (VmOperationEnum.STOP.getCode().equals(operateType)) {
                            //关机接收到成功消息则更新为STOPED
                            detail.setDeviceStatus("STOPED");
                        } else if (VmOperationEnum.SG_BIND.getCode().equals(operateType)) {
                            String oldSecurityGroupIds = detail.getSecurityGroupIds();
                            WocSecurityGroupDTO wocSecurityGroupDTO = wocSecurityGroupManager.getSecurityGroupByResourceId(attachResourceId);
                            String securityGroupIds = convertSecurityGroupIds(oldSecurityGroupIds, ListUtil.toList(wocSecurityGroupDTO.getId()), 1);
                            detail.setSecurityGroupIds(securityGroupIds);
                            resourceDetailManager.updateSecurityGroupIdById(detail);
                        } else if (VmOperationEnum.SG_UNBIND.getCode().equals(operateType)) {
                            String oldSecurityGroupIds = detail.getSecurityGroupIds();
                            WocSecurityGroupDTO wocSecurityGroupDTO = wocSecurityGroupManager.getSecurityGroupByResourceId(attachResourceId);
                            if (Objects.nonNull(wocSecurityGroupDTO)) {
                                String securityGroupIds = convertSecurityGroupIds(oldSecurityGroupIds, ListUtil.toList(wocSecurityGroupDTO.getId()), 2);
                                detail.setSecurityGroupIds(securityGroupIds);
                                resourceDetailManager.updateSecurityGroupIdById(detail);
                            }
                        }
                    }
                    if (operateType.contains(VmOperationEnum.DELETE.getCode())) {
                        detail.setDeviceStatus("DELETED");
                    }
                    resourceDetailManager.updateById(detail);
                    // 先更新detail的状态，再尝试发信息
                    if (needStart) {
                        try {
                            changeWorkOrderService.checkAndSendSMS(detail.getId());
                        } catch (Exception e) {
                            log.warn("短信发送失败, detail:{}, msg:{}", detail, ExceptionUtils.getStackTrace(e));
                        }
                    }
                    //已删除状态的数据在资源表里也标记为删除
                    if ("DELETED".equals(detail.getDeviceStatus())) {
                        resourceDetailManager.deleteById(detail.getId());
                    }
                }
            } else {
                //安全组和规则相关操作
                if (VmOperationEnum.SG_CREATE.getCode().equals(operateType)) {
                    WocSecurityGroupDTO securityGroupDTO = securityGroupManager.getSecurityGroupById(Long.valueOf(taskVO.getOrderId()));
                    securityGroupDTO.setOpenStatus(taskVO.getStatus());
                    securityGroupDTO.setResourceId(taskVO.getResourceId());
                    securityGroupManager.updateById(securityGroupDTO);
                }
            }
        }
    }

    /**
     * 处理绑定安全组id
     *
     * @param oldSecurityGroupIds 原先的绑定
     * @param newSecurityGroupIds 新的绑定
     * @param type                1-绑定，2-解绑
     * @return
     */
    private String convertSecurityGroupIds(String oldSecurityGroupIds,
                                           List<Long> newSecurityGroupIds,
                                           Integer type) {
        List<String> ids = new ArrayList<>();
        newSecurityGroupIds.forEach(id -> ids.add(String.valueOf(id)));
        String securityGroupIds = null;
        if (1 == type) {
            if (StringUtils.isNotBlank(oldSecurityGroupIds)) {
                List<String> list = ListUtil.toList(oldSecurityGroupIds.split(","));
                list.addAll(ids);
                list = list.stream().distinct().collect(Collectors.toList());
                securityGroupIds = String.join(",", list);
            } else {
                securityGroupIds = String.join(",", ids);
            }
        } else if (2 == type) {
            List<String> oldList = ListUtil.toList(oldSecurityGroupIds.split(","));
            oldList.removeIf(ids::contains);
            securityGroupIds = oldList.isEmpty() ? null : String.join(",", oldList);
        }
        return securityGroupIds;
    }

}

package com.datatech.slgzt.controller.standard;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.convert.StandardWorkOrderResOpenWebConvert;
import com.datatech.slgzt.enums.*;
import com.datatech.slgzt.enums.domain.CatalogueDomain;
import com.datatech.slgzt.handle.standard.StandardWorkOrderResOpenFillHandle;
import com.datatech.slgzt.manager.*;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.dto.*;
import com.datatech.slgzt.model.nostander.PlaneNetworkModel;
import com.datatech.slgzt.model.opm.ProductGeneralCheckOpm;
import com.datatech.slgzt.model.query.ResourceDetailQuery;
import com.datatech.slgzt.model.query.StandardWorkOrderProductQuery;
import com.datatech.slgzt.model.req.res.OrderStatusNoticeReq;
import com.datatech.slgzt.model.req.standard.StandardWorkOrderResOpenReq;
import com.datatech.slgzt.service.CmdbResourceCenterService;
import com.datatech.slgzt.service.ProductGeneralCheckService;
import com.datatech.slgzt.service.change.ChangeWorkOrderService;
import com.datatech.slgzt.service.cmdb.CmdbReportService;
import com.datatech.slgzt.service.standard.StandardResOpenService;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.Precondition;
import com.google.common.collect.Maps;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

/**
 * 标准的资源开通web控制器
 *
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月13日 14:52:54
 */
@Slf4j
@RestController
@RequestMapping("/standardWorkOrderResOpen")
public class StandardWorkOrderResOpenWebController implements InitializingBean {

    @Resource
    private StandardWorkOrderResOpenWebConvert convert;

    @Resource
    private StandardWorkOrderResOpenFillHandle handle;

    @Resource
    private StaticProductStockManager staticProductStockManager;


    @Resource
    private StandardWorkOrderManager standardWorkOrderManager;

    @Resource
    private ProductGeneralCheckService productGeneralCheckService;

    @Resource
    private StandardWorkOrderProductManager standardWorkOrderProductManager;

    @Resource
    private RecoveryWorkOrderProductManager recoveryWorkOrderProductManager;

    @Resource
    private ChangeWorkOrderProductManager changeWorkOrderProductManager;

    @Resource
    private ChangeWorkOrderService changeWorkOrderService;

    @Resource
    private ResourceDetailManager resourceDetailManager;

    @Resource
    private CmdbReportService cmdbReportService;

    @Resource
    private ObsOpenTaskManager obsOpenTaskManager;

    @Resource
    private CmdbResourceCenterService cmdbResourceCenterService;

    @Resource
    private VnicManager vnicManager;


    @Autowired
    private List<StandardResOpenService> standardResOpenServiceList;

    private final Map<String, StandardResOpenService> standardResOpenServiceMap = Maps.newHashMap();


    /**
     * 开启资源
     *
     * @param requestBody 请求体
     */
    @SneakyThrows
    @RequestMapping("/open")
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Void> nostanderResOpen(@RequestBody StandardWorkOrderResOpenReq req) {
        Precondition.checkArgument(req.getStandardWorkOrderId(), "工单id不能为空");
        Precondition.checkArgument(req.getOpenResIds(), "要开通的资源id不能为空");
        Precondition.checkArgument(req.getOpenResType(), "资源类型不能为空");
        StandardWorkOrderDTO orderDTO = standardWorkOrderManager.getById(req.getStandardWorkOrderId());
        Precondition.checkArgument(orderDTO, "找不到对应工单");
        //------------------检查目前选择要开通的资源有没有被开通------------------
        List<StandardWorkOrderProductDTO> productDTOS =
                standardWorkOrderProductManager.list(new StandardWorkOrderProductQuery()
                        .setOrderId(req.getStandardWorkOrderId())
                        .setProductType(req.getOpenResType())
                        .setIds(req.getOpenResIds()));
        Precondition.checkArgument(productDTOS, "找不到对应资源");
        productDTOS.forEach(productDTO -> {
            Precondition.checkArgument(
                    ResOpenEnum.WAIT_OPEN.getCode()
                                         .equals(productDTO.getOpenStatus()) || ResOpenEnum.OPEN_FAIL.getCode()
                                                                                                     .equals(productDTO.getOpenStatus()), "该资源无法开启");
        });
        // 校验openResIds的数量和ipAddresses的数量是否一致
        checkIpAddressesCount(req);
        //前端传入可用区相关 更新对应开通资源的快照
        for (int i = 0; i < productDTOS.size(); i++) {
            handle.fillStandardWorkOrderResOpen(convert.convert(productDTOS.get(i), req, orderDTO, i));
        }
        //这边要校验下容量够不够
        ProductGeneralCheckOpm checkOpm = convert.convertCheckOpm(productDTOS);
        //存量校验 如果domainCode 是vmware 需要校验AZ维度的产品
        if (orderDTO.getDomainCode().equals(CatalogueDomain.VMWARE.getCode())) {
            productGeneralCheckService.checkProductInAzPool(checkOpm);
        }
        productGeneralCheckService.checkProductInAzCapacity(checkOpm);
        Precondition.checkArgument(ObjNullUtils.isNull(checkOpm.getErrorMessages()), checkOpm.getErrorMessages()
                                                                                             .toString());
        //找到对应的资源开通服务处理器
        StandardResOpenService standardResOpenService = standardResOpenServiceMap.get(req.getOpenResType());
        Precondition.checkArgument(standardResOpenService, "找不到对应资源开通服务处理器");
        for (StandardWorkOrderProductDTO productDTO : productDTOS) {
            //obs特殊处理，单独存入表中，定时处理
            if (ProductTypeEnum.OBS.getCode().equals(productDTO.getProductType())) {
                ObsOpenTaskDTO dto = new ObsOpenTaskDTO();
                dto.setWorkOrderId(productDTO.getWorkOrderId());
                dto.setProductOrderId(productDTO.getId());
                dto.setStatus(ResOpenEnum.WAIT_OPEN.getCode());
                obsOpenTaskManager.insert(dto);
                //工单表改为开通中状态
                standardWorkOrderProductManager.updateStatusById(productDTO.getId(), ResOpenEnum.OPENING.getCode());
            } else {
                standardResOpenService.openStandardResource(productDTO);
            }
        }
        //构建资源开通的参数
        return CommonResult.success(null);
    }

    /**
     * 回调接口
     * 工单中心->调用->编排中心
     * 编排中心->调用->工单中心 这一步的回调方法
     * 返回结果 包括所有产品 这边对应PRODUCT表
     * 改变状态和消息即可
     *
     * @param dto
     * @return
     */
    @PostMapping("/layoutTaskNotify")
    public CommonResult<Void> layoutTaskNotify(@RequestBody @Validated OrderStatusNoticeReq req) {
        //订购类型
        log.info("standard layoutTaskNotify start req:{}", JSONObject.toJSONString(req));
        String type = req.getType();
        //1是产品主任务的回调  2是子产品的回调
        Integer orderType = req.getOrderType();
        switch (OrderTypeEnum.getByCode(type)) {
            case SUBSCRIBE:
                subscribeLayout(orderType, req);
                break;
            case UNSUBSCRIBE:
                unsubscribeLayout(orderType, req);
                break;
            case MODIFY:
                modifyLayout(orderType, req);
                break;
        }
        return CommonResult.build(1, 200, "回调成功", null);
    }

    @GetMapping("pushVmToCMDB")
    public CommonResult<Void> pushVmToCMDB(@RequestParam String orderId) {
        cmdbReportService.createInstance(orderId);
        return CommonResult.success(null);
    }

    @GetMapping("deleteVmFromCMDB")
    public CommonResult<Void> deleteVmFromCMDB(@RequestParam Long resourceId) {
        cmdbReportService.deleteInstance(resourceId);
        return CommonResult.success(null);
    }

    @GetMapping("updateVmFromCMDB")
    public CommonResult<Void> updateInstanceEcs(@RequestParam String orderId) {
        cmdbReportService.updateInstanceEcs(orderId);
        return CommonResult.success(null);
    }

    /**
     * 资源开通回调
     */
    private void subscribeLayout(Integer orderType, OrderStatusNoticeReq req) {
        if (orderType == 1) {
            StandardWorkOrderProductDTO productDTO = standardWorkOrderProductManager.getBySubOrderId(Long.valueOf(req.getOrderId()));
            Precondition.checkArgument(productDTO, "找不到对应产品");
            StandardWorkOrderDTO standardWorkOrderDTO = standardWorkOrderManager.getById(productDTO.getWorkOrderId());
            Precondition.checkArgument(standardWorkOrderDTO, "找不到对应工单");
            //更新对应的状态
            productDTO.setMessage(req.getMessage());
            productDTO.setOpenStatus(ResOpenEnum.adaptTaskCenterResult(req.getHandleResult()).getCode());
            standardWorkOrderProductManager.update(productDTO);
            obsOpenTaskManager.updateByProductOrderId(ResOpenEnum.OPEN_SUCCESS.getCode(), productDTO.getId());
        }
        if (orderType == 2) {
            StandardWorkOrderProductDTO productDTO = standardWorkOrderProductManager.getById(Long.valueOf(req.getOrderId()));
            Precondition.checkArgument(productDTO, "找不到对应产品");
            if (0 == productDTO.getParentProductId()) {
                log.info("该产品是主产品，由主任务来更新状态");
                return;
            }
            //更新对应的状态
            productDTO.setMessage(req.getMessage());
            productDTO.setOpenStatus(ResOpenEnum.adaptTaskCenterResult(req.getHandleResult()).getCode());
            standardWorkOrderProductManager.update(productDTO);
        }
    }

    /**
     * 资源回收回调
     */
    private void unsubscribeLayout(Integer orderType, OrderStatusNoticeReq req) {
        if (orderType == 1) {
            RecoveryWorkOrderProductDTO recoveryProductDTO = recoveryWorkOrderProductManager.getBySubOrderId(Long.valueOf(req.getOrderId()));
            Precondition.checkArgument(recoveryProductDTO, "找不到对应产品");
            recoveryProductDTO.setMessage(1 == req.getHandleResult() ? " " : req.getMessage());
            recoveryProductDTO.setRecoveryStatus(String.valueOf(RecoveryStatusEnum.adaptTaskCenterResult(req.getHandleResult())
                                                                                  .getType()));
            recoveryWorkOrderProductManager.update(recoveryProductDTO);
            //失败了不更新资源状态
            if (1 == req.getHandleResult()) {
                String productType = recoveryProductDTO.getProductType();
                if (ProductTypeEnum.ECS.getCode().equals(productType)
                        || ProductTypeEnum.GCS.getCode().equals(productType)
                        || ProductTypeEnum.MYSQL.getCode().equals(productType)
                        || ProductTypeEnum.REDIS.getCode().equals(productType)) {
                    ecsComplete(recoveryProductDTO);
                } else if (ProductTypeEnum.EIP.getCode().equals(productType)) {
                    eipComplete(recoveryProductDTO);
                } else if (ProductTypeEnum.SLB.getCode().equals(productType) || ProductTypeEnum.NAT.getCode()
                                                                                                   .equals(productType)) {
                    slbAndNatComplete(recoveryProductDTO);
                } else if (ProductTypeEnum.EVS.getCode().equals(productType)) {
                    evsComplete(recoveryProductDTO);
                }
                ResourceDetailDTO resourceDetailDTO = resourceDetailManager.getById(Long.valueOf(recoveryProductDTO.getResourceDetailId()));
                Precondition.checkArgument(resourceDetailDTO, "找不到对应资源");
                cmdbReportService.deleteInstance(Long.valueOf(recoveryProductDTO.getResourceDetailId()));
                cmdbResourceCenterService.delLevel3IpBindBusSys(Long.valueOf(recoveryProductDTO.getResourceDetailId()));
                resourceDetailManager.deleteById(Long.valueOf(recoveryProductDTO.getResourceDetailId()));
            }
        }
        if (orderType == 2) {
            RecoveryWorkOrderProductDTO recoveryProductDTO = recoveryWorkOrderProductManager.getById(Long.valueOf(req.getOrderId()));
            Precondition.checkArgument(recoveryProductDTO, "找不到对应产品");
            if (0 == recoveryProductDTO.getParentProductId()) {
                log.info("该产品是主产品，由主任务来更新状态");
                return;
            }
            recoveryProductDTO.setMessage(req.getMessage());
            recoveryProductDTO.setRecoveryStatus(String.valueOf(RecoveryStatusEnum.adaptTaskCenterResult(req.getHandleResult())
                                                                                  .getType()));
            recoveryWorkOrderProductManager.update(recoveryProductDTO);
            if (1 == req.getHandleResult()) {
                //删除资源数据
                ResourceDetailDTO resourceDetailDTO = resourceDetailManager.getById(Long.valueOf(recoveryProductDTO.getResourceDetailId()));
                Precondition.checkArgument(resourceDetailDTO, "找不到对应资源");
                resourceDetailManager.deleteById(Long.valueOf(recoveryProductDTO.getResourceDetailId()));
            }
        }
    }

    /**
     * 资源变更回调
     */
    private void modifyLayout(Integer orderType, OrderStatusNoticeReq req) {
        ChangeTypeProductStatusEnum changeResult = ChangeTypeProductStatusEnum.adaptTaskCenterResult(req.getHandleResult());
        ChangeWorkOrderProductDTO changeWorkOrderProductDTO = null;
        if (orderType == 1) {
            changeWorkOrderProductDTO = changeWorkOrderProductManager.getBySubOrderId(Long.valueOf(req.getOrderId()));
            Precondition.checkArgument(changeWorkOrderProductDTO, "找不到对应产品");
            changeWorkOrderProductDTO.setMessage(1 == req.getHandleResult() ? " " : req.getMessage());
            changeWorkOrderProductDTO.setChangeStatus(changeResult.getCode());
        }
        if (orderType == 2) {
            changeWorkOrderProductDTO = changeWorkOrderProductManager.getById(Long.valueOf(req.getOrderId()));
            Precondition.checkArgument(changeWorkOrderProductDTO, "找不到对应产品");
            changeWorkOrderProductDTO.setMessage(req.getMessage());
            changeWorkOrderProductDTO.setChangeStatus(String.valueOf(changeResult.getCode()));

        }
        if (changeWorkOrderProductDTO != null) {
            changeWorkOrderProductManager.update(changeWorkOrderProductDTO);
            if (changeResult == ChangeTypeProductStatusEnum.CHANGE_SUCCESS
                    && (ProductTypeEnum.ECS.getCode().equals(changeWorkOrderProductDTO.getProductType())
                    || ProductTypeEnum.GCS.getCode().equals(changeWorkOrderProductDTO.getProductType()))
                    || ProductTypeEnum.MYSQL.getCode().equals(changeWorkOrderProductDTO.getProductType())
                    || ProductTypeEnum.REDIS.getCode().equals(changeWorkOrderProductDTO.getProductType())) {
                ResourceDetailDTO detailDTO = resourceDetailManager.getById(Long.valueOf(changeWorkOrderProductDTO.getResourceDetailId()));
                changeWorkOrderService.tryStartEcs(detailDTO);
            }
            changeWorkOrderService.checkAndSendSMS(Long.valueOf(changeWorkOrderProductDTO.getResourceDetailId()));
        }
    }


    /**
     * 云主机回收成功后回调特殊处理
     *
     * @param productDTO
     */
    private void ecsComplete(RecoveryWorkOrderProductDTO productDTO) {
        String resourceDetailId = productDTO.getResourceDetailId();
        ResourceDetailDTO resourceDetailDTO = resourceDetailManager.getById(Long.valueOf(resourceDetailId));
        Precondition.checkArgument(resourceDetailDTO, "找不到对应资源");
        //删除虚拟网卡
        VnicDTO vnicDTO = vnicManager.getByVmId(resourceDetailDTO.getDeviceId());
        if (Objects.isNull(vnicDTO)) {
            return;
        }
        vnicManager.delete(vnicDTO.getId());
        //获取挂载的资源
        List<ResourceDetailDTO> mountList = resourceDetailManager
                .list(new ResourceDetailQuery().setVmId(resourceDetailDTO.getDeviceId())
                                               .setTypeList(ListUtil.toList("eip", "evs")));
        //如果没有挂载的资源 就pass 如果有就把名字和id都改成null
        if (mountList.isEmpty()) {
            return;
        }
        //平台云特殊处理：平台云的云硬盘回收是跟着云主机的（不会下发云硬盘回收的子任务），在这里要把该云主机下的云硬盘都删掉
        if (DomainCodeEnum.PLF_PROC_NWC_ZJ_PLF_NEW.getCode().equals(resourceDetailDTO.getDomainCode())) {
            mountList.forEach(r -> resourceDetailManager.deleteById(r.getId()));
        }
        for (ResourceDetailDTO mountDTO : mountList) {
            ResourceDetailDTO updateDTO = new ResourceDetailDTO();
//            updateDTO.setVmId(null);
//            updateDTO.setEcsName(null);
            updateDTO.setId(mountDTO.getId());
            if (!productDTO.getSyncRecovery()) {
                updateDTO.setRecoveryStatus(RecoveryStatusEnum.ORDER_TO_BE_RECOVERED.getType());
            }
            resourceDetailManager.clearRelatedInfoById(mountDTO.getId());
            resourceDetailManager.updateVmIdAndEscNameById(updateDTO);
        }
    }

    /**
     * 云硬盘回收成功后回调特特殊处理
     *
     * @param productDTO
     */
    private void evsComplete(RecoveryWorkOrderProductDTO productDTO) {
        String resourceDetailId = productDTO.getResourceDetailId();
        ResourceDetailDTO evsDetail = resourceDetailManager.getById(Long.valueOf(resourceDetailId));
        Precondition.checkArgument(evsDetail, "找不到对应资源");
        String vmId = evsDetail.getVmId();
        if (StringUtils.isNotBlank(vmId)) {
            //如果是挂载了云主机，更新对应的云主机挂盘id和数据盘容量
            ResourceDetailDTO ecsDetail = resourceDetailManager.getByDeviceId(vmId);
            String volumeId = ecsDetail.getVolumeId();
            String dataDisk = ecsDetail.getDataDisk();
            if (StringUtils.isNotBlank(volumeId)) {
                List<String> list = Arrays.asList(volumeId.split(","));
                int index = list.indexOf(evsDetail.getDeviceId());
                List<String> dataDisks = new ArrayList<>(ListUtil.of(dataDisk.split(",")));
                List<String> volumeIds = new ArrayList<>(ListUtil.of(volumeId.split(",")));
                dataDisks.remove(index);
                volumeIds.remove(index);
                ecsDetail.setDataDisk(String.join(",", dataDisks));
                ecsDetail.setVolumeId(String.join(",", volumeIds));
                resourceDetailManager.updateById(ecsDetail);
            }
        }
    }

    /**
     * slb和net回收成功后回调特殊处理
     *
     * @param productDTO
     */
    private void slbAndNatComplete(RecoveryWorkOrderProductDTO productDTO) {
        String resourceDetailId = productDTO.getResourceDetailId();
        ResourceDetailDTO resourceDetailDTO = resourceDetailManager.getById(Long.valueOf(resourceDetailId));
        Precondition.checkArgument(resourceDetailDTO, "找不到对应资源");
        String azId = resourceDetailDTO.getAzId();
        if (StringUtils.isNotBlank(azId)) {
            staticProductStockManager.increaseStockByAzAndType(azId, productDTO.getProductType());
        }
        if (resourceDetailDTO.getEipId() == null) {
            return;
        }
        ResourceDetailDTO resourceDetailEipDTO = resourceDetailManager.getByDeviceId(resourceDetailDTO.getEipId());
        if (resourceDetailEipDTO == null) {
            log.warn("找不到对应eip资源, resource id:{}, eipId:{}", resourceDetailId, resourceDetailDTO.getEipId());
            return;
        }
        if (!productDTO.getSyncRecovery()) {
            resourceDetailEipDTO.setRecoveryStatus(RecoveryStatusEnum.ORDER_TO_BE_RECOVERED.getType());
            resourceDetailManager.clearRelatedInfoById(resourceDetailEipDTO.getId());
            resourceDetailManager.updateEipById(resourceDetailEipDTO);
        } else {
            resourceDetailDTO.setRecoveryStatus(RecoveryStatusEnum.RECOVERY_COMPLETE.getType());
            resourceDetailManager.deleteById(resourceDetailEipDTO.getId());
        }
    }

    /**
     * eip回收成功后回调特殊处理
     *
     * @param productDTO
     */
    private void eipComplete(RecoveryWorkOrderProductDTO productDTO) {
        String resourceDetailId = productDTO.getResourceDetailId();
        ResourceDetailDTO resourceDetailDTO = resourceDetailManager.getById(Long.valueOf(resourceDetailId));
        Precondition.checkArgument(resourceDetailDTO, "找不到对应资源");
        resourceDetailManager.clearEipInfoByEipId(resourceDetailDTO.getDeviceId());
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        for (StandardResOpenService standardResOpenService : standardResOpenServiceList) {
            standardResOpenServiceMap.put(standardResOpenService.registerOpenService()
                                                                .getCode(), standardResOpenService);
        }
    }

    @Scheduled(fixedRate = 150000)
    public void execute() {
        log.info("start obsOpenToInstanceTask");

        //判断是否有开通中的任务（保证一次只能有一个任务在执行）
        ObsOpenTaskDTO openingTask = obsOpenTaskManager.selectObsOpenTaskByStatus(ResOpenEnum.OPENING.getCode());
        log.info("obsOpenHandle--是否有开通中的任务={}", JSON.toJSON(openingTask));
        if (openingTask != null) {
            //查询任务状态，做补偿更新
            StandardWorkOrderProductDTO productDto = standardWorkOrderProductManager.getById(openingTask.getProductOrderId());
            if (Objects.nonNull(productDto) && ResOpenEnum.OPEN_SUCCESS.getCode().equals(productDto.getOpenStatus())) {
                obsOpenTaskManager.updateByProductOrderId(ResOpenEnum.OPEN_SUCCESS.getCode(), openingTask.getProductOrderId());
                log.info("{} 任务实际已开通完成，更新状态", openingTask.getProductOrderId());
            } else {
                log.info("有开通中的obs，productOrderId {}，本次调度跳过", openingTask.getProductOrderId());
            }
        } else {
            ObsOpenTaskDTO waitOpenTask = obsOpenTaskManager.selectObsOpenTaskByStatus(ResOpenEnum.WAIT_OPEN.getCode());
            if (waitOpenTask == null) {
                log.info("当前没有需要执行的obs开通任务");
            } else {
                Long productOrderId = waitOpenTask.getProductOrderId();
                StandardWorkOrderProductDTO productDTO = standardWorkOrderProductManager.getById(productOrderId);
                obsOpenTaskManager.updateByProductOrderId(ResOpenEnum.OPENING.getCode(), productOrderId);
                StandardResOpenService standardResOpenService = standardResOpenServiceMap.get("obs");
                standardResOpenService.openStandardResource(productDTO);
                log.info("任务下发成功,productOrderId = " + productOrderId);
            }
        }
    }

    private void checkIpAddressesCount(StandardWorkOrderResOpenReq req) {
        if (req.getPlaneNetworkModelList() != null) {
            for (PlaneNetworkModel planeNetworkModel : req.getPlaneNetworkModelList()) {
                if (planeNetworkModel.getSubnets() != null) {
                    for (PlaneNetworkModel.Subnet subnet : planeNetworkModel.getSubnets()) {
                        if (subnet.getIpAddresses() != null) {
                            Precondition.checkArgument(subnet.getIpAddresses().size() == req.getOpenResIds().size(),
                                    "要开通的资源数量和IP地址数量不一致");
                        }
                    }
                }
            }
        }
    }


}

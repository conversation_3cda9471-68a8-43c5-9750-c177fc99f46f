package com.datatech.slgzt.service.container.impl;

import com.datatech.slgzt.dao.ContainerQuotaDAO;
import com.datatech.slgzt.dao.model.container.ContainerQuotaDO;
import com.datatech.slgzt.enums.StatusEnum;
import com.datatech.slgzt.model.dto.ContainerQuotaDTO;
import com.datatech.slgzt.model.nostander.CQModel;
import com.datatech.slgzt.model.query.ContainerQuotaQuery;
import com.datatech.slgzt.service.container.ContainerQuotaService;
import com.datatech.slgzt.utils.BeanCopyUtils;
import com.datatech.slgzt.utils.IdUtils;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.warpper.PageWarppers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 容器配额服务实现类
 * <AUTHOR>
 * @description 容器配额业务逻辑实现
 * @date 2025年05月27日
 */
@Slf4j
@Service
public class ContainerQuotaServiceImpl implements ContainerQuotaService {

    @Resource
    private ContainerQuotaDAO containerQuotaDAO;

    @Override
    public ContainerQuotaDTO createContainerQuota(CQModel cqModel, String workOrderId, String subOrderId,
                                                  Long businessSystemId, String businessSystemName) {
        log.info("创建容器配额记录，工单ID：{}，子订单ID：{}，业务系统ID：{}，业务系统名称：{}",
                workOrderId, subOrderId, businessSystemId, businessSystemName);

        // 创建容器配额DO对象
        ContainerQuotaDO containerQuotaDO = new ContainerQuotaDO();

        // 设置基础信息
        containerQuotaDO.setId(IdUtils.generateId());
        containerQuotaDO.setWorkOrderId(workOrderId);
        containerQuotaDO.setSubOrderId(subOrderId);
        containerQuotaDO.setBusinessSystemId(businessSystemId);
        containerQuotaDO.setBusinessSystemName(businessSystemName);
        containerQuotaDO.setEnabled(StatusEnum.NORMAL.code());
        containerQuotaDO.setCreateTime(LocalDateTime.now());
        containerQuotaDO.setModifyTime(LocalDateTime.now());

        // 从CQModel复制属性
        if (cqModel != null) {
            containerQuotaDO.setCqName(cqModel.getCqName());
            containerQuotaDO.setVCpus(cqModel.getVCpus());
            containerQuotaDO.setRam(cqModel.getRam());
            containerQuotaDO.setGpuRatio(cqModel.getGpuRatio());
            containerQuotaDO.setGpuVirtualMemory(cqModel.getGpuVirtualMemory());
            containerQuotaDO.setGpuCore(cqModel.getGpuCore());
            containerQuotaDO.setGpuVirtualCore(cqModel.getGpuVirtualCore());
            containerQuotaDO.setA4Account(cqModel.getA4Account());
            containerQuotaDO.setA4Phone(cqModel.getA4Phone());
            containerQuotaDO.setApplyTime(cqModel.getApplyTime());
            containerQuotaDO.setOpenNum(cqModel.getOpenNum());
            containerQuotaDO.setCatalogueDomainCode(cqModel.getCatalogueDomainCode());
            containerQuotaDO.setCatalogueDomainName(cqModel.getCatalogueDomainName());
            containerQuotaDO.setDomainCode(cqModel.getDomainCode());
            containerQuotaDO.setDomainName(cqModel.getDomainName());
            containerQuotaDO.setRegionId(cqModel.getRegionId());
            containerQuotaDO.setRegionCode(cqModel.getRegionCode());
            containerQuotaDO.setRegionName(cqModel.getRegionName());
            containerQuotaDO.setStatus(cqModel.getStatus());
            containerQuotaDO.setOriginName(cqModel.getOriginName());
        }

        // 保存到数据库
        containerQuotaDAO.insert(containerQuotaDO);

        log.info("容器配额记录创建成功，ID：{}", containerQuotaDO.getId());

        // 转换为DTO返回
        return convertToDTO(containerQuotaDO);
    }

    @Override
    public ContainerQuotaDTO getById(String id) {
        log.info("根据ID查询容器配额，ID：{}", id);
        ContainerQuotaDO containerQuotaDO = containerQuotaDAO.selectById(id);
        return convertToDTO(containerQuotaDO);
    }

    @Override
    public List<ContainerQuotaDTO> getByWorkOrderId(String workOrderId) {
        log.info("根据工单ID查询容器配额列表，工单ID：{}", workOrderId);
        List<ContainerQuotaDO> containerQuotaDOList = containerQuotaDAO.selectByWorkOrderId(workOrderId);
        return convertToDTOList(containerQuotaDOList);
    }

    @Override
    public ContainerQuotaDTO getBySubOrderId(String subOrderId) {
        log.info("根据子订单ID查询容器配额，子订单ID：{}", subOrderId);
        ContainerQuotaDO containerQuotaDO = containerQuotaDAO.selectBySubOrderId(subOrderId);
        return convertToDTO(containerQuotaDO);
    }

    @Override
    public PageResult<ContainerQuotaDTO> queryPage(ContainerQuotaQuery query) {
        log.info("分页查询容器配额，查询条件：{}", query);

        // 使用PageHelper进行分页
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<ContainerQuotaDO> containerQuotaDOList = containerQuotaDAO.selectList(query);

        // 转换为DTO分页结果
        PageResult<ContainerQuotaDTO> result = PageWarppers.box(new PageInfo<>(containerQuotaDOList), this::convertToDTO);

        log.info("分页查询容器配额完成，总记录数：{}，当前页记录数：{}",
                result.getTotal(), result.getRecords().size());

        return result;
    }

    @Override
    public List<ContainerQuotaDTO> queryList(ContainerQuotaQuery query) {
        log.info("查询容器配额列表，查询条件：{}", query);
        List<ContainerQuotaDO> containerQuotaDOList = containerQuotaDAO.selectList(query);
        return convertToDTOList(containerQuotaDOList);
    }

    /**
     * 将DO对象转换为DTO对象
     * @param containerQuotaDO DO对象
     * @return DTO对象
     */
    private ContainerQuotaDTO convertToDTO(ContainerQuotaDO containerQuotaDO) {
        if (containerQuotaDO == null) {
            return null;
        }
        return BeanCopyUtils.copyBean(containerQuotaDO, ContainerQuotaDTO.class);
    }

    /**
     * 将DO对象列表转换为DTO对象列表
     * @param containerQuotaDOList DO对象列表
     * @return DTO对象列表
     */
    private List<ContainerQuotaDTO> convertToDTOList(List<ContainerQuotaDO> containerQuotaDOList) {
        if (CollectionUtils.isEmpty(containerQuotaDOList)) {
            return Collections.emptyList();
        }
        return containerQuotaDOList.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }
}

package com.datatech.slgzt.service.container.impl;

import com.datatech.slgzt.manager.ContainerQuotaManager;
import com.datatech.slgzt.model.dto.ContainerQuotaDTO;
import com.datatech.slgzt.model.nostander.CQModel;
import com.datatech.slgzt.model.query.ContainerQuotaQuery;
import com.datatech.slgzt.service.container.ContainerQuotaService;
import com.datatech.slgzt.utils.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 容器配额服务实现类
 * <AUTHOR>
 * @description 容器配额业务逻辑实现
 * @date 2025年05月27日
 */
@Slf4j
@Service
public class ContainerQuotaServiceImpl implements ContainerQuotaService {

    @Resource
    private ContainerQuotaManager containerQuotaManager;

    @Override
    public ContainerQuotaDTO createContainerQuota(CQModel cqModel, String workOrderId, String subOrderId,
                                                  Long businessSystemId, String businessSystemName) {
        log.info("Service层：创建容器配额记录，工单ID：{}，子订单ID：{}，业务系统ID：{}，业务系统名称：{}",
                workOrderId, subOrderId, businessSystemId, businessSystemName);

        return containerQuotaManager.createContainerQuota(cqModel, workOrderId, subOrderId, businessSystemId, businessSystemName);
    }

    @Override
    public ContainerQuotaDTO getById(String id) {
        log.info("Service层：根据ID查询容器配额，ID：{}", id);
        return containerQuotaManager.getById(id);
    }

    @Override
    public List<ContainerQuotaDTO> getByWorkOrderId(String workOrderId) {
        log.info("Service层：根据工单ID查询容器配额列表，工单ID：{}", workOrderId);
        return containerQuotaManager.getByWorkOrderId(workOrderId);
    }

    @Override
    public ContainerQuotaDTO getBySubOrderId(String subOrderId) {
        log.info("Service层：根据子订单ID查询容器配额，子订单ID：{}", subOrderId);
        return containerQuotaManager.getBySubOrderId(subOrderId);
    }

    @Override
    public PageResult<ContainerQuotaDTO> queryPage(ContainerQuotaQuery query) {
        log.info("Service层：分页查询容器配额，查询条件：{}", query);
        return containerQuotaManager.queryPage(query);
    }

    @Override
    public List<ContainerQuotaDTO> queryList(ContainerQuotaQuery query) {
        log.info("Service层：查询容器配额列表，查询条件：{}", query);
        return containerQuotaManager.queryList(query);
    }
}

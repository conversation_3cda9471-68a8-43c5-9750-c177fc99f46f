package com.datatech.slgzt.convert;

import org.mapstruct.Mapper;

import com.datatech.slgzt.model.dto.ContainerQuotaDTO;
import com.datatech.slgzt.model.query.ContainerQuotaQuery;
import com.datatech.slgzt.model.req.container.ContainerQuotaPageReq;
import com.datatech.slgzt.model.vo.container.ContainerQuotaVO;

@Mapper(componentModel = "spring")
public interface ContainerQuotaWebConvert {
    ContainerQuotaQuery convert(ContainerQuotaPageReq req);
    
    ContainerQuotaVO convert(ContainerQuotaDTO dto);
}

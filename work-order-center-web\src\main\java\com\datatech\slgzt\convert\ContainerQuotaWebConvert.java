package com.datatech.slgzt.convert;

import org.mapstruct.Mapper;

import com.datatech.slgzt.model.dto.ContainerQuotaDTO;
import com.datatech.slgzt.model.query.ContainerQuotaQuery;
import com.datatech.slgzt.model.req.container.ContainerQuotaPageReq;
import com.datatech.slgzt.model.vo.container.ContainerQuotaVO;
import com.datatech.slgzt.model.vo.container.ContainerQuotaExportVO;

@Mapper(componentModel = "spring")
public interface ContainerQuotaWebConvert {
    ContainerQuotaQuery convert(ContainerQuotaPageReq req);

    ContainerQuotaVO convert(ContainerQuotaDTO dto);

    /**
     * DTO转导出VO，需要自定义映射GPU卡数量
     */
    @org.mapstruct.Mapping(target = "gpuCardCount", expression = "java(calculateGpuCardCount(dto.getGpuCore(), dto.getGpuVirtualCore()))")
    ContainerQuotaExportVO convertToExportVO(ContainerQuotaDTO dto);

    /**
     * 计算GPU卡数量
     */
    default Integer calculateGpuCardCount(Integer gpuCore, Integer gpuVirtualCore) {
        int core = gpuCore != null ? gpuCore : 0;
        int virtualCore = gpuVirtualCore != null ? gpuVirtualCore : 0;
        return core + virtualCore;
    }
}

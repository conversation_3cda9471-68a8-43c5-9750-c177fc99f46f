package com.datatech.slgzt.impl.service.standard;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.convert.CmpAppConvert;
import com.datatech.slgzt.dao.mapper.business.BusinessMapper;
import com.datatech.slgzt.dao.model.db.CmpAppDO;
import com.datatech.slgzt.enums.*;
import com.datatech.slgzt.enums.bpmn.ActivityEnum;
import com.datatech.slgzt.enums.bpmn.RecoveryOrderNodeEnum;
import com.datatech.slgzt.exception.UniversalException;
import com.datatech.slgzt.helps.UserHelper;
import com.datatech.slgzt.manager.ResourceDetailManager;
import com.datatech.slgzt.manager.StandardWorkOrderManager;
import com.datatech.slgzt.manager.StandardWorkOrderProductManager;
import com.datatech.slgzt.manager.WorkOrderAuthLogManager;
import com.datatech.slgzt.model.BaseProductModel;
import com.datatech.slgzt.model.baseconfig.OacConfig;
import com.datatech.slgzt.model.bpmn.ActivityTaskVo;
import com.datatech.slgzt.model.bpmn.InnerTask;
import com.datatech.slgzt.model.bpmn.TaskNodeDTO;
import com.datatech.slgzt.model.business.CmpAppDTO;
import com.datatech.slgzt.model.dto.*;
import com.datatech.slgzt.model.dto.order.StandardAuditWorkOrderDTO;
import com.datatech.slgzt.model.dto.tenant.CmpTenantDTO;
import com.datatech.slgzt.model.home.AuditCountVo;
import com.datatech.slgzt.model.nostander.*;
import com.datatech.slgzt.model.order.OrderBill;
import com.datatech.slgzt.model.query.StandardWorkOrderProductQuery;
import com.datatech.slgzt.model.query.StandardWorkOrderQuery;
import com.datatech.slgzt.model.query.WorkOrderAuthLogQuery;
import com.datatech.slgzt.model.usercenter.UserCenterUserDTO;
import com.datatech.slgzt.service.CmdbResourceCenterService;
import com.datatech.slgzt.service.PlatformService;
import com.datatech.slgzt.service.UserService;
import com.datatech.slgzt.service.bpmn.BaseActivity;
import com.datatech.slgzt.service.cmdb.CmdbReportService;
import com.datatech.slgzt.service.config.ConfigService;
import com.datatech.slgzt.service.nostander.NonStanderWorkOrderTempSaveService;
import com.datatech.slgzt.service.standard.StandardWorkOrderService;
import com.datatech.slgzt.service.tenant.CmpTenantService;
import com.datatech.slgzt.service.usercenter.IUserSelectStrategy;
import com.datatech.slgzt.service.yunshu.YunshuReportService;
import com.datatech.slgzt.utils.*;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025年 03月13日 22:54:46
 */
@Service
@Slf4j
public class StandardWorkOrderServiceImpl implements StandardWorkOrderService {


    ExecutorService executor = Executors.newFixedThreadPool(5);

    @Resource
    private StandardWorkOrderProductManager productManager;

    @Resource
    private UserHelper userHelper;

    @Resource
    private BusinessMapper businessMapper;

    @Resource
    private IUserSelectStrategy iUserSelectStrategy;

    @Resource
    private StandardWorkOrderManager standardWorkOrderManager;

    @Resource
    private ResourceDetailManager resourceDetailManager;

    @Resource
    private WorkOrderAuthLogManager workOrderAuthLogManager;

    @Resource
    private NonStanderWorkOrderTempSaveService nonStanderWorkOrderTempSaveService;

    @Resource
    private BaseActivity baseActivity;

    @Resource
    private CmpTenantService cmpTenantService;

    @Resource
    private UserService userService;

    @Resource
    private ConfigService configService;

    @Resource
    private CmpAppConvert appConvert;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private CmdbReportService cmdbReportService;

    @Resource
    private YunshuReportService yunshuReportService;

    @Resource
    private PlatformService platformService;

    @Resource
    private CmdbResourceCenterService cmdbResourceCenterService;


    /**
     * 标准工单草稿key
     */
    private final String SUBSCRIBE_ORDER_DRAFT = "subscribe_order_draft:";

    /**
     * 返回工单公共信息
     *
     * @param orderId
     * @return
     */
    @Override
    public OrderCommonDTO getOrderCommon(String orderId) {
        // 查询工单信息
        StandardWorkOrderDTO orderDTO = standardWorkOrderManager.getById(orderId);
        OrderCommonDTO orderCommonDTO = new OrderCommonDTO();
        orderCommonDTO.setBillId(orderDTO.getBillId());
        orderCommonDTO.setTenantId(orderDTO.getTenantId());
        orderCommonDTO.setUserId(orderDTO.getCreatedBy());
        orderCommonDTO.setBusinessSysId(orderDTO.getBusiSystemId());
        orderCommonDTO.setBusinessSysName(orderDTO.getBusinessSystemName());
        orderCommonDTO.setModuleId(orderDTO.getModuleId());
        orderCommonDTO.setModuleName(orderDTO.getModuleName());
        orderCommonDTO.setUserName(orderDTO.getCreatedUserName());
        orderCommonDTO.setOrderCode(orderDTO.getOrderCode());
        orderCommonDTO.setDomainCode(orderDTO.getDomainCode());
        orderCommonDTO.setDomainName(orderDTO.getDomainName());
        orderCommonDTO.setCatalogueDomainCode(orderDTO.getCatalogueDomainCode());
        orderCommonDTO.setCatalogueDomainName(orderDTO.getCatalogueDomainName());
        orderCommonDTO.setSourceType(SourceTypeEnum.STANDARD.getPrefix());
        // 查询工单产品信息
        List<StandardWorkOrderProductDTO> list = productManager.list(new StandardWorkOrderProductQuery().setOrderId(orderId));
        List<String> regionCodeList = Lists.newArrayList();
        if (ObjNullUtils.isNotNull(list)) {
            regionCodeList = list.stream().map(StandardWorkOrderProductDTO::getPropertySnapshot)
                                 .map(i -> JSON.parseObject(i, BaseProductModel.class))
                                 .map(BaseProductModel::getRegionCode).filter(ObjNullUtils::isNotNull)
                                 .distinct().collect(Collectors.toList());
        }
        orderCommonDTO.setRegionCodeList(StreamUtils.distinct(regionCodeList));
        return orderCommonDTO;
    }

    /**
     * 判断工单是否已经完成
     *
     * @param orderId
     */
    @Override
    public boolean isOrderComplete(String orderId) {
        //查询工单信息
        StandardWorkOrderDTO orderDTO = standardWorkOrderManager.getById(orderId);
        Precondition.checkArgument(ObjNullUtils.isNotNull(orderDTO), "工单不存在");
        String orderStatus = orderDTO.getOrderStatus();
        return OrderStatusEnum.CLOSE.getCode().equals(orderStatus) || OrderStatusEnum.END.getCode().equals(orderStatus);
    }


    /**
     * 创建非标订工单并且开始流程
     *
     * @param dto
     */
    @Override
    public void createNonStandardOrderAndStartProcess(StandardWorkOrderDTO dto, String requestPath, UserCenterUserDTO userDTO) {
        // 启动工作流
        dto.setCreatedBy(userDTO.getId());
        String activityId = "";
        String auditResult = "";
        if (MethodPathEnum.RESOURCE_RESUBMIT_OPEN.getPath().equalsIgnoreCase(requestPath)) {
            auditResult = OrderLogStatusEnum.RESUBMIT.getCode();
            StandardWorkOrderDTO orderDTO = standardWorkOrderManager.getById(dto.getId());
            activityId = orderDTO.getActivitiId();
        } else {
            auditResult = OrderLogStatusEnum.CREATE.getCode();
            activityId = baseActivity.instanceRun(dto, ActivityEnum.ActivityProcessEnum.RESOURCE_PROCESS, null, null);
            if (StringUtils.isEmpty(activityId)) {
                throw UniversalException.build(GlobalExceptionEnum.ACTIVITY_DEFINITION_IS_NOT_FOUND);
            }
        }

        LocalDateTime createTime = LocalDateTime.now();
        workOrderAuthLogManager.createWorkOrderAuthLog(new WorkOrderAuthLogDTO().setCreateTime(createTime)
                                                                                .setModifyTime(createTime)
                                                                                .setWorkOrderId(dto.getId())
                                                                                .setProcessInstanceId(activityId)
                                                                                .setAdvice(ActivitiStatusEnum.USER_TASK.getNodeRemark())
                                                                                .setAuditNodeCode(ActivitiStatusEnum.USER_TASK.getNode())
                                                                                .setAuditNodeName(com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum.USER_TASK.getNodeRemark())
                                                                                .setUserId(dto.getCreatedBy())
                                                                                .setUserName(userDTO.getUserName())
                                                                                .setUserPhone(userDTO.getPhone())
                                                                                .setUserEmail(userDTO.getUserEmail())
                                                                                .setAuditResult(auditResult));
        dto.setActivitiId(activityId);

        standardOrderFill(dto, requestPath, activityId, null, createTime);
        // 流转工作流
        if (!MethodPathEnum.DRAFTS_ORDER_OPEN.getPath().equals(requestPath)) {
            // 添加草稿箱的时候不需要将流程执行到下个节点
            baseActivity.complete(dto, ActivityEnum.ActivityStatusEnum.PASS, activityId, null, ActivityEnum.ActivityProcessEnum.RESOURCE_PROCESS, userDTO.getId(), null, null);
        }
        ActivityTaskVo taskVo = baseActivity.taskNodes(activityId, ActivityEnum.ActivityProcessEnum.RESOURCE_PROCESS);
        dto.setCurrentNodeName(com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum.getNodeRemarkByNode(taskVo.getCurrentTask(), RecoveryOrderNodeEnum.EXAMINING.getCode()));
        dto.setCurrentNodeCode(taskVo.getCurrentTask());
        updateOrderStatus(dto, userDTO.getId(), OrderStatusEnum.EXAMINING.getCode());
        //设置工单操作日志
        //workOrderAuthLogManager.createWorkOrderAuthLog(new WorkOrderAuthLogDTO().setWorkOrderId(dto.getId()).setProcessInstanceId(activityId).setAdvice(ActivitiStatusEnum.SCHEMA_ADMINISTRATOR.getNodeRemark()).setAuditNodeCode(taskVo.getCurrentTask()).setAuditNodeName(com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum.getNodeRemarkByNode(taskVo.getCurrentTask(), null)).setUserId(dto.getCreatedBy()).setUserName(userDTO.getUserName()).setUserPhone(userDTO.getPhone()).setUserEmail(userDTO.getUserEmail()).setAuditResult(auditResult));
        nonStanderWorkOrderTempSaveService.handleDeleteAll(userDTO.getId());

    }

    /**
     * 撤销工单
     *
     * @param workOrderId@return
     */
    @Override
    public void cancel(String workOrderId) {
        StandardWorkOrderDTO orderDTO = standardWorkOrderManager.getById(workOrderId);
        Precondition.checkArgument(orderDTO, "当前工单不存在");
        UserCenterUserDTO currentUser = userHelper.getCurrentUser();
        Precondition.checkArgument(currentUser, "获取当前用户信息失败");
        //判断当前工单是不是当前用户创建不然不能撤销
        Precondition.checkArgument(currentUser.getId()
                                              .equals(orderDTO.getCreatedBy()), "当前用户不是工单创建人，不能撤销");
        // 获取工单当前审核节点
        ActivityTaskVo taskVo = baseActivity.taskNodes(orderDTO.getActivitiId(), ActivityEnum.ActivityProcessEnum.RESOURCE_PROCESS);
        Precondition.checkArgument(taskVo.getCurrentTask() != null, "已无审核流程节点！");
        //如果当前节点是资源开通和网络开通节点则不允许撤销
        Precondition.checkArgument(!Lists.newArrayList(AuthorityCodeEnum.NETWORK_PROVISIONING.code(),
                                                 AuthorityCodeEnum.RESOURCE_CREATION.code())
                                         .contains(orderDTO.getCurrentNodeCode()), "当前节点不允许撤销");
        // 撤销工单
        baseActivity.stop(orderDTO, currentUser.getId(), ActivityEnum.ActivityProcessEnum.RESOURCE_PROCESS);
        // 更新工单状态
        LocalDateTime now = LocalDateTime.now();
        workOrderAuthLogManager.createWorkOrderAuthLog(new WorkOrderAuthLogDTO().setWorkOrderId(workOrderId)
                                                                                .setProcessInstanceId(orderDTO.getActivitiId())
                                                                                .setCreateTime(now)
                                                                                .setModifyTime(now)
                                                                                .setAdvice(ActivitiStatusEnum.ORDER_CANCEL.getNodeRemark())
                                                                                .setAuditNodeCode(ActivitiStatusEnum.ORDER_CANCEL.getNode())
                                                                                .setAuditNodeName(ActivitiStatusEnum.ORDER_CANCEL.getNodeRemark())
                                                                                .setUserId(orderDTO.getCreatedBy())
                                                                                .setUserName(currentUser.getUserName())
                                                                                .setUserPhone(currentUser.getPhone())
                                                                                .setUserEmail(currentUser.getUserEmail())
                                                                                .setAuditResult(ActivitiStatusEnum.ORDER_CANCEL.getNodeRemark()));

        orderDTO.setCurrentNodeCode(ActivitiStatusEnum.ORDER_CANCEL.getNode());
        orderDTO.setCurrentNodeName(ActivitiStatusEnum.ORDER_CANCEL.getNodeRemark());
        orderDTO.setWorkOrderEndTime(now);
        orderDTO.setCurrentNodeStartTime(now);
        updateOrderStatus(orderDTO, userHelper.getCurrentUserId(), OrderStatusEnum.CLOSE.getCode());
    }

    private void standardOrderFill(StandardWorkOrderDTO dto, String requestPath, String activityId, ActivityTaskVo taskVo, LocalDateTime workOrderStartTime) {
        UserCenterUserDTO currentUser = userHelper.getCurrentUser();
        dto.setCreatedBy(currentUser.getId());
        dto.setCreatedByName(currentUser.getUserName());
        dto.setOrderCode(CodeUtil.getOrderCode(CodePrefixEnum.OC.getCode()));
        dto.setActiviteKey(ActivityEnum.ActivityProcessEnum.RESOURCE_PROCESS.getCode());
        dto.setOrderType(OrderTypeEnum.SUBSCRIBE.getCode());
        dto.setOrderStatus(OrderStatusEnum.EXAMINING.getCode());
        dto.setCreateTime(workOrderStartTime);
        dto.setActivitiId(activityId);
        processBillIdAndCustomNo(dto.getBusiSystemId(), dto, requestPath);
        if (dto.getTenantId() != null) {
            CmpTenantDTO tenantDTO = cmpTenantService.selectById(dto.getTenantId());
            dto.setTenantName(tenantDTO.getName());
        }

        dto.setCreatedUserName(currentUser.getUserName());
        dto.setWorkOrderStartTime(workOrderStartTime);
        dto.setCurrentNodeStartTime(LocalDateTime.now());

        if (taskVo != null) {
            dto.setCurrentNodeName(com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum.getNodeRemarkByNode(taskVo.getCurrentTask(), RecoveryOrderNodeEnum.EXAMINING.getCode()));
            dto.setCurrentNodeCode(taskVo.getCurrentTask());
        }

        standardWorkOrderManager.update(dto);
    }

    private void processBillIdAndCustomNo(Long businessSystemId, StandardWorkOrderDTO orderDTO, String requestPath) {
        if (businessSystemId == null) {
            if (MethodPathEnum.DRAFTS_ORDER_OPEN.getPath().equals(requestPath)) {
                return;
            }
            throw UniversalException.build(GlobalExceptionEnum.PARAM_EXCEPTION.getCode(), "资源开通失败，业务系统id不能为空");
        }

        CmpAppDO businessSystem = businessMapper.selectById(businessSystemId);
        // 通过订单绑定的业务系统获取业务系统所属租户id，避免直接从用户中获取，因为用户和租户是一对多的
        if (businessSystem == null) {
            throw UniversalException.build(GlobalExceptionEnum.PARAM_EXCEPTION.getCode(), String.format("资源开通失败，业务系统id：[%s]数据库中不存在", businessSystemId));
        }

        orderDTO.setTenantId(businessSystem.getTenantId());
        orderDTO.setBusinessSystemCode(businessSystem.getSystemCode());
        orderDTO.setBusinessSystemName(businessSystem.getSystemName());
        OrderBill orderBill = iUserSelectStrategy.getBillIdByCode(businessSystem.getSystemCode());
        if (orderBill == null) {
            log.error("业务系统编码:{},添加工单申请时获取billId以及customNo失败", businessSystem.getSystemCode());
        } else {
            orderDTO.setBillId(orderBill.getBillId());
            orderDTO.setCustomNo(orderBill.getCustomNo());
        }
    }

    @Override
    public String draft(StandardWorkOrderDTO dto, String requestPath) {
        UserCenterUserDTO currentUser = userHelper.getCurrentUser();
        createNonStandardOrderAndStartProcess(dto, requestPath, currentUser);
        return dto.getId();
    }

    @Override
    public void audit(StandardAuditWorkOrderDTO dto) {
        Long userId = userHelper.getCurrentUserId();
        dto.setUserId(userId);
        StandardWorkOrderDTO orderDTO = standardWorkOrderManager.getById(dto.getOrderId());
        Precondition.checkArgument(orderDTO, "当前工单不存在");
        //3.获取工作流中，此订单的当前审核节点
        ActivityTaskVo taskVo = baseActivity.taskNodes(orderDTO.getActivitiId(), ActivityEnum.ActivityProcessEnum.RESOURCE_PROCESS);
        Precondition.checkArgument(taskVo, "已无审核流程节点！");
        orderDTO.setCurrentNodeCode(taskVo.getCurrentTask());
        // 判断传入的流程节点和当前节点是否匹配，不匹配则部进行审批操作
        if (StringUtils.isNotEmpty(dto.getCurrentNodeCode()) && !dto.getCurrentNodeCode()
                                                                    .equals(taskVo.getCurrentTask())) {
            List<String> codeList = Arrays.asList(ActivitiStatusEnum.BUSINESS2_DEPART_LEADER.getNode(), ActivitiStatusEnum.BUSINESS_DEPART_LEADER2.getNode());
            if (!ActivitiStatusEnum.BUSINESS_DEPART_LEADER2.getNode()
                                                           .equals(dto.getCurrentNodeCode()) || !codeList.contains(taskVo.getCurrentTask())) {
                log.error("审核时传入的流程节点和流程服务中当前节点不一致，传入的节点：{}，流程服务中当前节点：{}", dto.getCurrentNodeCode(), taskVo.getCurrentTask());
                return;
            }
        }
        auditOperator(dto, orderDTO);
    }

    @Override
    public PageResult<StandardWorkOrderDTO> page(StandardWorkOrderQuery query, Long currentUserId) {
        if (QueryParamCheckUtil.containsPercentage(query)) {
            return new PageResult();
        }
        //判断查询的是待审批 还是已审批 还是驳回的工单
        Precondition.checkArgument(query.getApprovalCode());
        Precondition.checkArgument(currentUserId, "当前用户未登录");
        UserCenterUserDTO user = userService.getUserById(currentUserId);
        List<String> ruleCodeList = UserServiceExt.processRoleData_(user.getOacRoles());
        switch (query.getApprovalCode()) {
            case "pending":
                //关联的运行时任务表 需要当前用户的角色code和用户id
                query.setRoleCodeList(ruleCodeList);
                query.setUserId(currentUserId.toString());
                query.setApprovalCode("pending");
                break;
            case "approved":
                query.setUserId(currentUserId.toString());
                query.setApprovalCode("approved");
                query.setRoleCodeList(ruleCodeList);
                break;
            case "rejected":
                query.setUserId(currentUserId.toString());
                query.setApprovalCode("rejected");
                query.setRoleCodeList(ruleCodeList);
                //需要查询用户历史的里有过审批拒绝相关的流程id
                List<String> processIds = workOrderAuthLogManager.groupByProcessInstanceId(new WorkOrderAuthLogQuery()
                        .setUserId(currentUserId)
                        .setAuditResult(OrderStatusEnum.REJECT.getCode()));
                //如果processIds为空则直接返回空直接返回空
                if (CollectionUtil.isEmpty(processIds)) {
                    return new PageResult<>();
                }
                query.setProcessIds(processIds);
                break;
            default:
                break;
        }
        PageResult<StandardWorkOrderDTO> page = standardWorkOrderManager.page(query);
        return page;
    }

    private void auditOperator(StandardAuditWorkOrderDTO auditDto, StandardWorkOrderDTO orderDTO) {
        log.info("auditOperator auditDto :{},orderDTO:{}", JSONObject.toJSONString(auditDto), orderDTO);
        // 统一架构负责人审核的时候-选择云中心领导-修改流程节点信息
        if (auditDto.getCloudLeaderId() != null && auditDto.getSecondLevelLeaderId() != null) {
            orderDTO.setCloudLeaderId(auditDto.getCloudLeaderId());
            baseActivity.setVariable(orderDTO.getActivitiId(), "cloud", auditDto.getCloudLeaderId());
            iUserSelectStrategy.addUserRole(auditDto.getCloudLeaderId(), AuthorityCodeEnum.CLOUD_LEADER.code());
            try {
                Thread.sleep(200);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            orderDTO.setSecondLevelLeaderId(auditDto.getSecondLevelLeaderId());
            baseActivity.setVariable(orderDTO.getActivitiId(), "cloud_2", auditDto.getSecondLevelLeaderId());
        } else {
            if (auditDto.getCloudLeaderId() != null) {
                orderDTO.setCloudLeaderId(auditDto.getCloudLeaderId());
                baseActivity.setVariable(orderDTO.getActivitiId(), "cloud", auditDto.getCloudLeaderId());
            }
        }

        if (ActivityEnum.ActivityStatusEnum.REJECT.getCode()
                                                  .equals(auditDto.getActiviteStatus()) && Objects.equals(ActivitiStatusEnum.USER_TASK.getNode(), auditDto.getNodeCode()) && orderDTO.getActiviteKey()
                                                                                                                                                                                     .equals(ActivityEnum.ActivityProcessEnum.RESOURCE_PROCESS.getCode())) {
            // 驳回到前两个节点时需要将云平台和类型清楚
            orderDTO.setDomainCode("");
            orderDTO.setCatalogueDomainCode("");
            // todo,目前时驳回到起始节点会将商品产品关联的资源池删除
            // productManager.deleteByWorkOrderId(orderDTO.getId());
        }

        if (orderDTO.getActiviteKey()
                    .equals(ActivityEnum.ActivityProcessEnum.RESOURCE_PROCESS.getCode()) && ActivitiStatusEnum.TENANT_TASK.getNode()
                                                                                                                          .equals(orderDTO.getCurrentNodeCode()) && ActivityEnum.ActivityStatusEnum.PASS.getCode()
                                                                                                                                                                                                        .equals(auditDto.getActiviteStatus())) {
            Object isSkipBusinessNode = baseActivity.getVariable(orderDTO.getActivitiId(), "isSkipBusinessNode");
            if (isSkipBusinessNode == null) {
                baseActivity.setVariable(orderDTO.getActivitiId(), "isSkipBusinessNode", 1L);
            }
        }

        // 云资源部领导（包含二级、三级领导）进行驳回到架构师或者租户确认节点时，将业务二级、三级领导节点跳过，不需要业务领导进行审批操作
        if (ActivityEnum.ActivityStatusEnum.REJECT.getCode()
                                                  .equals(auditDto.getActiviteStatus()) && orderDTO.getActiviteKey()
                                                                                                   .equals(ActivityEnum.ActivityProcessEnum.RESOURCE_PROCESS.getCode())) {
            if (Arrays.asList(ActivitiStatusEnum.cloud_leader.getNode(), ActivitiStatusEnum.cloud_leader_2.getNode())
                      .contains(auditDto.getCurrentNodeCode()) && Arrays.asList(ActivitiStatusEnum.TENANT_TASK.getNode(), ActivitiStatusEnum.SCHEMA_ADMINISTRATOR.getNode())
                                                                        .contains(auditDto.getNodeCode())) {
                baseActivity.setVariable(orderDTO.getActivitiId(), "isSkipBusinessNode", 0L);
                log.info("云领导驳回到架构师或者租户确认节点后续流程将跳过业务领导审核");
            } else {
                baseActivity.setVariable(orderDTO.getActivitiId(), "isSkipBusinessNode", 1L);
            }
        }
        //在租户确认的时候调用创建底层租户的方法
        if (orderDTO.getActiviteKey().equals(ActivityEnum.ActivityProcessEnum.RESOURCE_PROCESS.getCode())
                && ActivitiStatusEnum.cloud_leader_2.getNode().equals(orderDTO.getCurrentNodeCode())
                && ActivityEnum.ActivityStatusEnum.PASS.getCode().equals(auditDto.getActiviteStatus())) {
            executor.execute(() -> {
                try {
                    OrderCommonDTO orderCommon = getOrderCommon(orderDTO.getId());
                    List<String> regionCodeList = orderCommon.getRegionCodeList();
                    for (String regionCode : regionCodeList) {
                        platformService.getOrCreateTenantId(orderCommon.getBillId(), regionCode);
                    }
                } catch (Exception ignore) {
                    //这里百分百会报错的超时问题，所以不需要处理
                }
            });
        }

        //架构审核补齐单子的产品信息，平台信息以及下面要做的补齐单子状态的操作等
        String currentTask = orderDTO.getCurrentNodeCode();
        if (currentTask.equals(ActivitiStatusEnum.SCHEMA_ADMINISTRATOR.getNode()) && ActivityEnum.ActivityStatusEnum.PASS.getCode()
                                                                                                                         .equals(auditDto.getActiviteStatus()) && orderDTO.getActiviteKey()
                                                                                                                                                                          .equals(ActivityEnum.ActivityProcessEnum.RESOURCE_PROCESS.getCode())) {
            log.info("auditOperator 待架构负责人审核 auditDto :{}", JSONObject.toJSONString(auditDto));
            standardWorkOrderManager.fillRegionMessage(auditDto, orderDTO);
            productManager.fillRegionMessage(auditDto);
        }

        if (orderDTO.getActiviteKey()
                    .equals(ActivityEnum.ActivityProcessEnum.RESOURCE_PROCESS.getCode()) && currentTask.equals(ActivitiStatusEnum.cloud_leader_2.getNode()) && ActivityEnum.ActivityStatusEnum.PASS.getCode()
                                                                                                                                                                                                   .equals(auditDto.getActiviteStatus())) {
            if (CollectionUtil.isNotEmpty(orderDTO.getCloudEcsResourceList()) || CollectionUtil.isNotEmpty(orderDTO.getCpuEcsResourceList())) {
                baseActivity.setVariable(orderDTO.getActivitiId(), "isNetOpen", 0L);
            } else {
                baseActivity.setVariable(orderDTO.getActivitiId(), "isNetOpen", 1L);
            }
        }

        // 审核状态
        ActivityEnum.ActivityStatusEnum auditStatus = ActivityEnum.ActivityStatusEnum.getByCode(auditDto.getActiviteStatus());
        ActivityEnum.ActivityProcessEnum activityProcessEnum = ActivityEnum.ActivityProcessEnum.getByCode(orderDTO.getActiviteKey());
        ConfigTypeEnum configTypeEnum = ConfigTypeEnum.findByActivityEnum(activityProcessEnum);
        OacConfig oldTaskCode = configService.getByCode(currentTask, configTypeEnum.getCode(), null, orderDTO.getStartRoleCode());

        // 订单状态
        String orderStatus = null;
        ActivityTaskVo taskNew = baseActivity.taskNodes(orderDTO.getActivitiId(), ActivityEnum.ActivityProcessEnum.RESOURCE_PROCESS);
        orderDTO.setCurrentNodeStartTime(LocalDateTime.now());
        switch (auditStatus) {
            case PASS:
                if (null != taskNew && !ActivitiStatusEnum.RESOURCE_CREATION.getNode()
                                                                            .equals(taskNew.getCurrentTask())) {
                    orderStatus = OrderStatusEnum.EXAMINING.getCode();
                    insertAuthLog(taskNew.getCurrentTask(), orderDTO, auditDto.getAuditAdvice(), OrderLogStatusEnum.PASS.getCode());
                } else {
                    //到这里已经是工单完成节点了
                    if (ActivityEnum.ActivityProcessEnum.RESOURCE_PROCESS.getCode()
                                                                         .equalsIgnoreCase(orderDTO.getActiviteKey())) {
                        CmpAppDO appDO = businessMapper.selectById(orderDTO.getBusiSystemId());
                        if (appDO == null) {
                            throw UniversalException.build(BusinessExceptionEnum.BUSINESS_SYSTEM_ISNULL.code, BusinessExceptionEnum.BUSINESS_SYSTEM_ISNULL.message);
                        }
                        // todo 业务系统创建和上线
                        // appDO.setLifeCycle(BusinessSystemEnum.LifeCycleEnum.ON_NET_TYPE.getCode());
                        CmpAppDTO appDTO = appConvert.do2dto(appDO);
                        String code = iUserSelectStrategy.updateUserAppMessage(appDTO, orderDTO);
                        if ("0".equals(code)) {
                            log.error("添加资源工单时同步修改用户中心业务系统生命周期失败，系统编码：{}", appDO.getSystemCode());
                            throw UniversalException.build(BusinessExceptionEnum.CMDB_INSTANCE_FAILED.code, BusinessExceptionEnum.CMDB_INSTANCE_FAILED.message);
                        }
                        //如果是线下开通这边不做入网交维动作
                        if (orderDTO.getIsOffline() == null || !orderDTO.getIsOffline()) {
                            cmdbReportService.createInstance(orderDTO.getId());
                            yunshuReportService.upper(orderDTO.getId());
                            //---调用资源中心占用网络Ip----
                            cmdbResourceCenterService.createLevel3IpBindBusSys(appDTO, orderDTO);
                        }
                        //
                    }

                    orderDTO.setWorkOrderEndTime(LocalDateTime.now());
                    insertAuthLog(taskNew != null ? taskNew.getCurrentTask() : null, orderDTO, auditDto.getAuditAdvice(), OrderLogStatusEnum.PASS.getCode());
                    orderStatus = OrderStatusEnum.END.getCode();
                }


                //结束操作订单的状态
                break;
            case REJECT:
                insertAuthLog(null, orderDTO, auditDto.getAuditAdvice(), OrderLogStatusEnum.REJECT.getCode());
                orderStatus = OrderStatusEnum.findDescByAuditStatus(auditStatus).getCode();
                break;
            default:
        }
        baseActivity.complete(orderDTO, auditStatus,
                orderDTO.getActivitiId(), auditDto.getAuditAdvice(), activityProcessEnum,
                auditDto.getUserId(), oldTaskCode, auditDto.getNodeCode());

        ActivityTaskVo taskVo = baseActivity.taskNodes(orderDTO.getActivitiId(), ActivityEnum.ActivityProcessEnum.RESOURCE_PROCESS);
        orderDTO.setCurrentNodeCode(taskVo.getCurrentTask());
        orderDTO.setCurrentNodeName(com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum.getNodeRemarkByNode(taskVo.getCurrentTask(), null));
        List<SaveSchemaAuditLogDTO> auditLogList = auditDto.getAuditLogList();
        if (CollectionUtil.isNotEmpty(auditLogList)) {
            orderDTO.setAuditLogList(auditLogList);
        }
        updateOrderStatus(orderDTO, auditDto.getUserId(), orderStatus);
    }

    private void insertAuthLog(String currentTask, StandardWorkOrderDTO orderDTO, String auditAdvice, String auditResult) {
        UserCenterUserDTO currentUser = userHelper.getCurrentUser();
        String orderStatus = currentTask;
        if (StringUtils.isEmpty(currentTask)) {
            if (OrderLogStatusEnum.REJECT.getCode().equalsIgnoreCase(auditResult)) {
                orderStatus = RecoveryOrderNodeEnum.REJECT.getCode();
            } else {
                orderStatus = RecoveryOrderNodeEnum.END.getCode();
            }
        }

        WorkOrderAuthLogDTO logDTO = new WorkOrderAuthLogDTO()
                .setCreateTime(orderDTO.getCurrentNodeStartTime())
                .setModifyTime(orderDTO.getCurrentNodeStartTime())
                .setWorkOrderId(orderDTO.getId())
                .setProcessInstanceId(orderDTO.getActivitiId())
                .setUserId(currentUser.getId())
                .setUserName(currentUser.getUserName())
                .setUserPhone(currentUser.getPhone())
                .setUserEmail(currentUser.getUserEmail())
                .setAdvice(auditAdvice)
                .setAuditNodeCode(currentTask)
                .setAuditResult(auditResult)
                .setAuditResultDesc(OrderLogStatusEnum.getByCode(auditResult).getDesc())
                .setAuditNodeName(com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum.getNodeRemarkByNode(currentTask, orderStatus));
        workOrderAuthLogManager.createWorkOrderAuthLog(logDTO);
    }

    @Override
    public void checkFillEscResource(EcsModel model, String workOrderId) {
        //如果存在挂载数据盘 需要校验挂载数据盘的内容
        if (model.getMountDataDisk()) {
            List<MountDataDiskModel> mountDataDiskList = model.getMountDataDiskList();
            Precondition.checkArgument(mountDataDiskList, "mountDataDiskList不能为空");
            for (MountDataDiskModel mountDataDiskModel : mountDataDiskList) {
                Precondition.checkArgument(mountDataDiskModel.getSysDiskSize(), "挂载数据盘的系统盘大小不能为空");
                Precondition.checkArgument(mountDataDiskModel.getSysDiskType(), "挂载数据盘的系统盘类型不能为空");
                mountDataDiskModel.setAzCode(model.getAzCode());
                mountDataDiskModel.setRegionCode(model.getRegionCode());
            }
        } else {
            model.setMountDataDiskList(null);
        }
        Precondition.checkArgument(model.getBindPublicIp(), "bindPublicIp不能为空");
        //如果绑定公网IP 需要校验公网IP的内容
        if (model.getBindPublicIp()) {
            List<EipModel> eipModelList = model.getEipModelList();
            Precondition.checkArgument(eipModelList, "eipModelList不能为空");
            for (EipModel eipModel : eipModelList) {
                Precondition.checkArgument(eipModel.getBandwidth(), "bandwidth不能为空");
                eipModel.setAzCode(model.getAzCode());
                eipModel.setRegionCode(model.getRegionCode());
            }
        } else {
            model.setEipModelList(null);
        }
        Precondition.checkArgument(model.getApplyTime(), "applyTime不能为空");
        Precondition.checkArgument(model.getOpenNum(), "openNum不能为空");
        //并且大于0
        Precondition.checkArgument(model.getOpenNum() > 0 && model.getOpenNum() < 201, "openNum必须大于0并且小于200");
        //数据都没问题了后创建数据
        //循环开通数量 ---下面部分需要抽离公用的方法
        // 循环创建指定数量的主产品
        //按照主产品的开通数量设置好Id数
        String originalVmName = model.getVmName(); // 保存原始名称
        List<Long> mainIds = generateMainIds(model.getOpenNum());
        model.setMainIds(mainIds);
        for (Long mainId : mainIds) {
            // 创建主产品（ESC）
            String productType = model.getProductType();
            model.setMainIds(mainIds);
            model.setId(mainId);
            //要给model里的vmName设置序号因为多个 按照01 02 03这样的格式
            model.setOriginName(originalVmName);
            model.setVmName(originalVmName + String.format("%03d", mainIds.indexOf(mainId) + 1));
            StandardWorkOrderProductDTO mainProduct = createMainProduct(model, mainId, productType, workOrderId);
            productManager.insert(mainProduct);
            // 创建关联子产品 如果不是ESC和GCS不用创建子产品
            createChildProducts(mainProduct.getId(), workOrderId, model);
        }
    }

    @Override
    public void checkFillSlbResource(SlbModel model, String workOrderId) {
        Precondition.checkArgument(model.getSlbName(), "slbName不能为空");
        //如果绑定公网IP 需要校验公网IP的内容
        if (model.getBindPublicIp()) {
            List<EipModel> eipModelList = model.getEipModelList();
            Precondition.checkArgument(eipModelList, "eipModelList不能为空");
            for (EipModel eipModel : eipModelList) {
                Precondition.checkArgument(eipModel.getBandwidth(), "bandwidth不能为空");
                eipModel.setAzCode(model.getAzCode());
                eipModel.setRegionCode(model.getRegionCode());
            }
        } else {
            model.setEipModelList(null);
        }
        //并且大于0
        Precondition.checkArgument(model.getOpenNum() > 0, "openNum必须大于0");
        List<Long> mainIds = generateMainIds(model.getOpenNum());
        String originalName = model.getSlbName(); // 保存原始名称
        model.setMainIds(mainIds);
        for (Long mainId : mainIds) {
            model.setOriginName(originalName);
            //要给model里的vmName设置序号因为多个 按照01 02 03这样的格式
            model.setSlbName(originalName + String.format("%03d", mainIds.indexOf(mainId) + 1));
            // 创建主产品
            String productType = model.getProductType();
            model.setId(mainId);
            StandardWorkOrderProductDTO product = new StandardWorkOrderProductDTO();
            product.setId(mainId);
            product.setProductType(productType);
            product.setWorkOrderId(workOrderId);
            product.setOpenStatus(ResOpenEnum.WAIT_OPEN.getCode());
            product.setGid(UuidUtil.getGid(ProductTypeEnum.SLB.getCode()));
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            product.setPropertySnapshot(JSON.toJSONString(model));
            productManager.insert(product);
            if (model.getBindPublicIp()) {
                createEipProduct(model.getEipModelList(), mainId, workOrderId);
            }
        }
    }

    @Override
    public void checkFillNatResource(NatGatwayModel model, String workOrderId) {
        Precondition.checkArgument(model.getNatName(), "natName不能为空");
        if (model.getBindPublicIp()) {
            List<EipModel> eipModelList = model.getEipModelList();
            Precondition.checkArgument(eipModelList, "eipModelList不能为空");
            for (EipModel eipModel : eipModelList) {
                Precondition.checkArgument(eipModel.getBandwidth(), "bandwidth不能为空");
                eipModel.setAzCode(model.getAzCode());
                eipModel.setRegionCode(model.getRegionCode());
            }
        } else {
            model.setEipModelList(null);
        }
        //并且大于0
        Precondition.checkArgument(model.getOpenNum() > 0, "openNum必须大于0");
        List<Long> mainIds = generateMainIds(model.getOpenNum());
        String originalName = model.getNatName(); // 保存原始名称
        model.setMainIds(mainIds);
        for (Long mainId : mainIds) {
            model.setOriginName(originalName);
            //要给model里的vmName设置序号因为多个 按照01 02 03这样的格式
            model.setNatName(originalName + String.format("%03d", mainIds.indexOf(mainId) + 1));
            // 创建主产品
            String productType = model.getProductType();
            model.setId(mainId);
            StandardWorkOrderProductDTO product = new StandardWorkOrderProductDTO();
            product.setId(mainId);
            product.setProductType(productType);
            product.setWorkOrderId(workOrderId);
            product.setOpenStatus(ResOpenEnum.WAIT_OPEN.getCode());
            product.setGid(UuidUtil.getGid(ProductTypeEnum.NAT.getCode()));
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            product.setPropertySnapshot(JSON.toJSONString(model));
            productManager.insert(product);
            if (model.getBindPublicIp()) {
                createEipProduct(model.getEipModelList(), mainId, workOrderId);
            }
        }
    }

    /**
     * evs
     *
     * @param model
     * @param workOrderId
     */
    @Override
    public void checkFillEvsResource(MountDataDiskModel model, String workOrderId, AtomicReference<String> vmDomainCode) {
        //并且大于0
        Precondition.checkArgument(model.getOpenNum() > 0, "openNum必须大于0");
        //如果存在要挂载的主机id
        String vmId = model.getVmId();
        if (StringUtils.isNotEmpty(vmId)) {
            //查询挂载机器是否存在
            ResourceDetailDTO detailDTO = resourceDetailManager.getByDeviceId(model.getVmId());
            Precondition.checkArgument(detailDTO, "挂载的主机不存在");
            if (StringUtils.isNotEmpty(vmDomainCode.get())) {
                //vmDomainCode 不为空要校验vmDomainCode是否等于要挂载的云主机的domainCode
                String domainCode = detailDTO.getDomainCode();
                Precondition.checkArgument(vmDomainCode.get().equals(domainCode), "挂载的主机的云平台和必须要保持一致");
            } else {
                vmDomainCode.set(detailDTO.getDomainCode());
            }
            //获取regionCode
            model.setRegionCode(detailDTO.getResourcePoolCode());
            String resourcePoolId = detailDTO.getResourcePoolId();
            //获取regionId
            if (StringUtils.isNotEmpty(resourcePoolId)) {
                model.setRegionId(Long.valueOf(resourcePoolId));
            }
            //获取regionName
            model.setRegionName(detailDTO.getResourcePoolName());
            //获取azCode
            model.setAzCode(detailDTO.getAzCode());
            //获取azName
            model.setAzName(detailDTO.getAzName());
            //获取azId
            String azId = detailDTO.getAzId();
            if (StringUtils.isNotEmpty(azId)) {
                model.setAzId(Long.valueOf(azId));
            }
        }
        List<Long> mainIds = generateMainIds(model.getOpenNum());
        model.setMainIds(mainIds);
        for (Long mainId : mainIds) {
            // 创建主产品
            String productType = model.getProductType();
            model.setId(mainId);
            StandardWorkOrderProductDTO product = new StandardWorkOrderProductDTO();
            product.setId(mainId);
            product.setProductType(productType);
            product.setWorkOrderId(workOrderId);
            product.setOpenStatus(ResOpenEnum.WAIT_OPEN.getCode());
            product.setGid(UuidUtil.getGid(ProductTypeEnum.EVS.getCode()));
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            product.setPropertySnapshot(JSON.toJSONString(model));
            productManager.insert(product);
        }
    }

    /**
     * eip
     *
     * @param model
     * @param workOrderId
     */
    @Override
    public void checkFillEipResource(EipModel model, String workOrderId, AtomicReference<String> vmDomainCode) {
        //并且大于0
        Precondition.checkArgument(model.getOpenNum() > 0, "openNum必须大于0");
        //必须要挂载的主机id
//        Precondition.checkArgument(StringUtils.isNotBlank(model.getVmId()), "必须要挂载到主机上");

        if (StringUtils.isNotEmpty(model.getVmId())) {
            //查询挂载机器是否存在
            ResourceDetailDTO detailDTO = resourceDetailManager.getByDeviceId(model.getVmId());
            Precondition.checkArgument(detailDTO, "挂载的主机不存在");
            if (StringUtils.isNotEmpty(vmDomainCode.get())) {
                //vmDomainCode 不为空要校验vmDomainCode是否等于要挂载的云主机的domainCode
                String domainCode = detailDTO.getDomainCode();
                Precondition.checkArgument(vmDomainCode.get().equals(domainCode), "挂载的主机的云平台和必须要保持一致");
            } else {
                vmDomainCode.set(detailDTO.getDomainCode());
            }

            //获取regionCode
            model.setRegionCode(detailDTO.getResourcePoolCode());
            String resourcePoolId = detailDTO.getResourcePoolId();
            //获取regionId
            if (StringUtils.isNotEmpty(resourcePoolId)) {
                model.setRegionId(Long.valueOf(resourcePoolId));
            }
            //获取regionName
            model.setRegionName(detailDTO.getResourcePoolName());
            //获取azCode
            model.setAzCode(detailDTO.getAzCode());
            //获取azName
            model.setAzName(detailDTO.getAzName());
            //获取azId
            String azId = detailDTO.getAzId();
            if (StringUtils.isNotEmpty(azId)) {
                model.setAzId(Long.valueOf(azId));
            }
        }

        List<Long> mainIds = generateMainIds(model.getOpenNum());
        String originalName = model.getEipName(); // 保存原始名称
        model.setMainIds(mainIds);
        for (Long mainId : mainIds) {
            model.setOriginName(originalName);
            // 创建主产品
            String productType = model.getProductType();
            model.setId(mainId);
            StandardWorkOrderProductDTO product = new StandardWorkOrderProductDTO();
            product.setId(mainId);
            product.setProductType(productType);
            product.setWorkOrderId(workOrderId);
            product.setOpenStatus(ResOpenEnum.WAIT_OPEN.getCode());
            product.setGid(UuidUtil.getGid(ProductTypeEnum.EIP.getCode()));
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            product.setPropertySnapshot(JSON.toJSONString(model));
            productManager.insert(product);
        }
    }

    @Override
    public void checkFillObsResource(ObsModel model, String workOrderId) {
        //并且大于0
        Precondition.checkArgument(model.getOpenNum() > 0, "openNum必须大于0");
        List<Long> mainIds = generateMainIds(model.getOpenNum());
        String originalName = model.getObsName(); // 保存原始名称
        model.setMainIds(mainIds);
        for (Long mainId : mainIds) {
            // 创建主产品
            model.setOriginName(originalName);
            //要给model里的vmName设置序号因为多个 按照01 02 03这样的格式
            model.setObsName(originalName + String.format("%03d", mainIds.indexOf(mainId) + 1));
            String productType = model.getProductType();
            model.setId(mainId);
            StandardWorkOrderProductDTO product = new StandardWorkOrderProductDTO();
            product.setId(mainId);
            product.setProductType(productType);
            product.setWorkOrderId(workOrderId);
            product.setOpenStatus(ResOpenEnum.WAIT_OPEN.getCode());
            product.setGid(UuidUtil.getGid(ProductTypeEnum.OBS.getCode()));
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            product.setPropertySnapshot(JSON.toJSONString(model));
            productManager.insert(product);
        }
    }

    @Override
    public void checkFillCQResource(CQModel model, StandardWorkOrderDTO workOrderDTO ) {
        // 校验必填字段
        Precondition.checkArgument(StringUtils.isNotEmpty(model.getCqName()), "容器名称不能为空");
        Precondition.checkArgument(model.getVCpus() != null && model.getVCpus() > 0, "容器配额-核心数必须大于0");
        Precondition.checkArgument(model.getRam() != null && model.getRam() > 0, "容器配额-内存大小必须大于0");
        Precondition.checkArgument(StringUtils.isNotEmpty(model.getApplyTime()), "申请时长不能为空");
        Precondition.checkArgument(StringUtils.isNotEmpty(model.getA4Account()), "4A账号不能为空");
        Precondition.checkArgument(StringUtils.isNotEmpty(model.getA4Phone()), "4A账号绑定的手机不能为空");
        Precondition.checkArgument(model.getOpenNum() != null && model.getOpenNum() > 0, "开通数量必须大于0");

        // 填入业务系统信息
        model.setBusinessSystemId(workOrderDTO.getBusiSystemId());
        model.setBusinessSystemName(workOrderDTO.getBusinessSystemName());

        // 获取当前用户信息，填入申请用户信息
        UserCenterUserDTO currentUser = userHelper.getCurrentUser();
        if (currentUser != null) {
            model.setApplyUserId(currentUser.getId());
            model.setApplyUserName(currentUser.getUserName());
        }

        // trim一下，底层要判断是否重复
        model.setA4Account(model.getA4Account().trim());
        model.setA4Phone(model.getA4Phone().trim());
        model.setCqName(model.getCqName().trim());

        List<Long> mainIds = generateMainIds(model.getOpenNum());
        String originalName = model.getCqName(); // 保存原始名称
        model.setMainIds(mainIds);
        for (Long mainId : mainIds) {
            // 创建主产品
            model.setOriginName(originalName);
            // 要给model里的containerName设置序号因为多个 按照01 02 03这样的格式
            model.setCqName(originalName + String.format("%03d", mainIds.indexOf(mainId) + 1));
            String productType = ProductTypeEnum.CQ.getCode();
            model.setProductType(productType);
            model.setId(mainId);
            StandardWorkOrderProductDTO product = new StandardWorkOrderProductDTO();
            product.setId(mainId);
            product.setProductType(productType);
            product.setWorkOrderId(workOrderDTO.getId());
            product.setOpenStatus(ResOpenEnum.WAIT_OPEN.getCode());
            product.setGid(UuidUtil.getGid(productType));
            product.setSubOrderId(IdUtil.getSnowflake().nextId());
            product.setPropertySnapshot(JSON.toJSONString(model));
            productManager.insert(product);
        }
    }

    @Override
    public ActivityTaskVo getTaskNodes(String orderId) {
        StandardWorkOrderDTO orderDTO = standardWorkOrderManager.getById(orderId);
        Precondition.checkArgument(orderDTO, "工单不存在");
        ActivityTaskVo taskNode = baseActivity.taskNodes(orderDTO.getActivitiId(), ActivityEnum.ActivityProcessEnum.RESOURCE_PROCESS);
        if (taskNode != null) {
            String remark = com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum.getNodeRemarkByNode(taskNode.getCurrentTask(), null);
            taskNode.setCurrentTaskName(remark);
            List<InnerTask> tasks = taskNode.getAllTasks();
            tasks.forEach(task -> {
                String taskName = com.datatech.slgzt.enums.bpmn.ActivitiStatusEnum.getNodeRemarkByNode(task.getTask(), null);
                task.setTaskName(taskName);
            });
        }
        return taskNode;
    }

    public void getNextTaskNodes(String orderId, List<WorkOrderAuthLogDTO> authLogDTOS, String currentTaskName) {
        StandardWorkOrderDTO orderDTO = standardWorkOrderManager.getById(orderId);
        Precondition.checkArgument(orderDTO, "工单不存在");
        TaskNodeDTO nextTaskNode = baseActivity.nextTaskNodes(orderDTO.getActivitiId(), ActivityEnum.ActivityProcessEnum.RESOURCE_PROCESS);
        if (nextTaskNode == null) {
            return;
        }
        String assignee = nextTaskNode.getAssignee();
        if (assignee == null) {
            return;
        }
        String name = "";
        String email = "";
        if (assignee.matches("[-+]?\\d+(\\.\\d+)?")) {
            UserCenterUserDTO userCenterUserDTO = userService.getUserById(Long.valueOf(assignee));
            name = userCenterUserDTO.getUserName();
            email = userCenterUserDTO.getUserEmail();
        } else {
            if (AuthorityCodeEnum.NETWORK_PROVISIONING.code().equals(assignee)
                    || AuthorityCodeEnum.RESOURCE_CREATION.code().equals(assignee)) {
                assignee = AuthorityCodeEnum.OPERATION_GROUP.code();
            }
            List<UserCenterUserDTO> list = userService.getUserListByRoleCode(assignee);
            name = String.join(",", list.stream().map(UserCenterUserDTO::getUserName).collect(Collectors.toList()));
            email = String.join(",", list.stream().map(UserCenterUserDTO::getUserEmail).collect(Collectors.toList()));
        }
        WorkOrderAuthLogDTO workOrderAuthLogDTO = new WorkOrderAuthLogDTO();
        workOrderAuthLogDTO.setWorkOrderId(orderId);
        workOrderAuthLogDTO.setUserName(name);
        workOrderAuthLogDTO.setUserEmail(email);
        workOrderAuthLogDTO.setAuditNodeCode(nextTaskNode.getBpmnName());
        workOrderAuthLogDTO.setAuditNodeName(currentTaskName);
        workOrderAuthLogDTO.setAuditResultDesc("待审核");
        authLogDTOS.add(workOrderAuthLogDTO);
    }

    // 创建主产品（ESC）
    private StandardWorkOrderProductDTO createMainProduct(EcsModel model, Long mainId, String productType, String workOrderId) {
        StandardWorkOrderProductDTO product = new StandardWorkOrderProductDTO();
        product.setId(mainId);
        product.setProductType(productType);
        product.setWorkOrderId(workOrderId);
        product.setOpenStatus(ResOpenEnum.WAIT_OPEN.getCode());
        product.setGid(UuidUtil.getGid(productType));
        product.setSubOrderId(IdUtil.getSnowflake().nextId());
        product.setPropertySnapshot(JSON.toJSONString(model));
        return product;
    }

    @Override
    public StandardWorkOrderDTO orderDetail(String orderId) {
        return standardWorkOrderManager.getById(orderId);
    }

    @Override
    public AuditCountVo orderCountCount(StandardWorkOrderQuery orderQuery, Long userId) {
        orderQuery.setApprovalCode(ApprovalTypeEnum.TODO_TYPE.getType());
        PageResult<StandardWorkOrderDTO> page = page(orderQuery, userId);
        Long todoSize = Optional.ofNullable(page.getTotal()).orElse(0L);
        //Integer todoSize = standardWorkOrderManager.orderCountStatistics(orderQuery);
        orderQuery.setApprovalCode(ApprovalTypeEnum.DONE_TYPE.getType());
        page = page(orderQuery, userId);
        Long doneSize = Optional.ofNullable(page.getTotal()).orElse(0L);
        //Integer doneSize = standardWorkOrderManager.orderCountStatistics(orderQuery);
        orderQuery.setApprovalCode(ApprovalTypeEnum.REJECT_TYPE.getType());
        page = page(orderQuery, userId);
        Long rejectSize = Optional.ofNullable(page.getTotal()).orElse(0L);
        //Integer rejectSize = standardWorkOrderManager.orderCountStatistics(orderQuery);
        return new AuditCountVo().setPendingCount(todoSize).setApprovedCount(doneSize).setRejectedCount(rejectSize);
    }

    // 批量创建子产品（SSD/EIP）
    private void createChildProducts(Long parentId, String workOrderId, EcsModel model) {
        // 创建SSD子产品
        createEvsProduct(model.getMountDataDiskList(), parentId, workOrderId);

        // 创建EIP子产品
        createEipProduct(model.getEipModelList(), parentId, workOrderId);
    }

    private void createEvsProduct(List<MountDataDiskModel> models, Long parentId, String workOrderId) {
        if (models == null) return;
        models.stream().map(model -> {
            long id = IdUtil.getSnowflake().nextId();
            model.setId(id);
            StandardWorkOrderProductDTO product = new StandardWorkOrderProductDTO();
            product.setId(id);
            product.setProductType(ProductTypeEnum.EVS.getCode());
            product.setWorkOrderId(workOrderId);
            product.setOpenStatus(ResOpenEnum.WAIT_OPEN.getCode());
            product.setPropertySnapshot(JSON.toJSONString(model));
            product.setParentProductId(parentId);
            product.setGid(UuidUtil.getGid(ProductTypeEnum.EVS.getCode()));
            return product;
        }).forEach(productManager::insert);
    }

    private void createEipProduct(List<EipModel> models, Long parentId, String workOrderId) {
        if (models == null) return;
        models.stream().map(model -> {
            long id = IdUtil.getSnowflake().nextId();
            model.setId(id);
            StandardWorkOrderProductDTO product = new StandardWorkOrderProductDTO();
            product.setId(id);
            product.setProductType(ProductTypeEnum.EIP.getCode());
            product.setWorkOrderId(workOrderId);
            product.setOpenStatus(ResOpenEnum.WAIT_OPEN.getCode());
            product.setPropertySnapshot(JSON.toJSONString(model));
            product.setParentProductId(parentId);
            product.setGid(UuidUtil.getGid(ProductTypeEnum.EIP.getCode()));
            return product;
        }).forEach(productManager::insert);
    }

    private List<Long> generateMainIds(int openNum) {
        List<Long> mainIds = Lists.newArrayListWithCapacity(openNum);
        for (int i = 0; i < openNum; i++) {
            mainIds.add(IdUtil.getSnowflake().nextId());
        }
        return mainIds;
    }

    public void updateOrderStatus(StandardWorkOrderDTO oacOrder, Long userId, String orderStatus) {
        //1. 修改操作订单
        oacOrder.setOrderStatus(orderStatus);
        oacOrder.setModifyTime(LocalDateTime.now());
        oacOrder.setUpdatedBy(userId);
        standardWorkOrderManager.update(oacOrder);
    }

    private void checkFillProduct(StandardWorkOrderDTO orderDTO) {

    }


    /**
     * 草稿暂存
     */
    @Override
    public void draftSave(StandardWorkOrderDTO dto) {
        String id = dto.getId();
        UserCenterUserDTO currentUser = userHelper.getCurrentUser();
        Precondition.checkArgument(currentUser, "当前用户未登录");
        RBucket<String> bucket = redissonClient.getBucket(SUBSCRIBE_ORDER_DRAFT + currentUser.getId() + ":" + id);
        bucket.set(JSON.toJSONString(dto), 30, TimeUnit.DAYS);
    }

    @Override
    public void draftDel(Long userId, String orderId) {
        Precondition.checkArgument(userId, "userId不能为空");
        RBucket<String> bucket = redissonClient.getBucket(SUBSCRIBE_ORDER_DRAFT + userId + ":" + orderId);
        bucket.delete();
    }

    /**
     * 获取草稿
     */
    @Override
    public StandardWorkOrderDTO getDraft(Long userId, String orderId) {
        Precondition.checkArgument(userId, "userId不能为空");
        RBucket<String> bucket = redissonClient.getBucket(SUBSCRIBE_ORDER_DRAFT + userId + ":" + orderId);
        String json = bucket.get();
        return JSON.parseObject(json, StandardWorkOrderDTO.class);
    }


    /**
     * 注册
     */
    @Override
    public OrderTypeEnum register() {
        return OrderTypeEnum.SUBSCRIBE;
    }
}

package com.datatech.slgzt.service.container;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.datatech.slgzt.dao.ContainerQuotaDAO;
import com.datatech.slgzt.dao.model.container.ContainerQuotaDO;
import com.datatech.slgzt.model.dto.ContainerQuotaDTO;
import com.datatech.slgzt.model.nostander.CQModel;
import com.datatech.slgzt.model.query.ContainerQuotaQuery;
import com.datatech.slgzt.service.container.impl.ContainerQuotaServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * 容器配额服务测试类
 * <AUTHOR>
 * @description 容器配额服务单元测试
 * @date 2025年05月27日
 */
@ExtendWith(MockitoExtension.class)
class ContainerQuotaServiceTest {

    @Mock
    private ContainerQuotaDAO containerQuotaDAO;

    @InjectMocks
    private ContainerQuotaServiceImpl containerQuotaService;

    private CQModel cqModel;
    private ContainerQuotaDO containerQuotaDO;
    private String workOrderId;
    private String subOrderId;
    private Long businessSystemId;
    private String businessSystemName;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        workOrderId = "WO123456789";
        subOrderId = "SO123456789";
        businessSystemId = 1001L;
        businessSystemName = "测试业务系统";

        // 创建CQModel测试数据
        cqModel = new CQModel();
        cqModel.setCqName("测试容器配额");
        cqModel.setVCpus(4);
        cqModel.setRam(8);
        cqModel.setGpuRatio(2);
        cqModel.setGpuVirtualMemory(4);
        cqModel.setGpuCore(1);
        cqModel.setGpuVirtualCore(2);
        cqModel.setA4Account("test_account");
        cqModel.setA4Phone("***********");
        cqModel.setApplyTime("1年");
        cqModel.setOpenNum(1);
        cqModel.setCatalogueDomainCode("public");
        cqModel.setCatalogueDomainName("公有云");
        cqModel.setDomainCode("huawei");
        cqModel.setDomainName("华为云");
        cqModel.setRegionId(2001L);
        cqModel.setRegionCode("cn-north-1");
        cqModel.setRegionName("华北-北京一");
        cqModel.setAzId(3001L);
        cqModel.setAzCode("cn-north-1a");
        cqModel.setAzName("可用区1a");
        cqModel.setProductType("cq");
        cqModel.setFunctionalModule("容器服务");

        // 创建ContainerQuotaDO测试数据
        containerQuotaDO = new ContainerQuotaDO();
        containerQuotaDO.setId("test_id_123");
        containerQuotaDO.setWorkOrderId(workOrderId);
        containerQuotaDO.setSubOrderId(subOrderId);
        containerQuotaDO.setBusinessSystemId(businessSystemId);
        containerQuotaDO.setBusinessSystemName(businessSystemName);
        containerQuotaDO.setCqName(cqModel.getCqName());
        containerQuotaDO.setVCpus(cqModel.getVCpus());
        containerQuotaDO.setRam(cqModel.getRam());
        containerQuotaDO.setEnabled(true);
        containerQuotaDO.setCreateTime(LocalDateTime.now());
        containerQuotaDO.setModifyTime(LocalDateTime.now());
    }

    @Test
    void testCreateContainerQuota() {
        // 模拟DAO插入操作
        doNothing().when(containerQuotaDAO).insert(any(ContainerQuotaDO.class));

        // 执行测试
        ContainerQuotaDTO result = containerQuotaService.createContainerQuota(
                cqModel, workOrderId, subOrderId, businessSystemId, businessSystemName);

        // 验证结果
        assertNotNull(result);
        assertEquals(workOrderId, result.getWorkOrderId());
        assertEquals(subOrderId, result.getSubOrderId());
        assertEquals(businessSystemId, result.getBusinessSystemId());
        assertEquals(businessSystemName, result.getBusinessSystemName());
        assertEquals(cqModel.getCqName(), result.getCqName());
        assertEquals(cqModel.getVCpus(), result.getVCpus());
        assertEquals(cqModel.getRam(), result.getRam());

        // 验证DAO方法被调用
        verify(containerQuotaDAO, times(1)).insert(any(ContainerQuotaDO.class));
    }

    @Test
    void testGetById() {
        // 模拟DAO查询操作
        when(containerQuotaDAO.selectById(anyString())).thenReturn(containerQuotaDO);

        // 执行测试
        ContainerQuotaDTO result = containerQuotaService.getById("test_id_123");

        // 验证结果
        assertNotNull(result);
        assertEquals(containerQuotaDO.getId(), result.getId());
        assertEquals(containerQuotaDO.getCqName(), result.getCqName());

        // 验证DAO方法被调用
        verify(containerQuotaDAO, times(1)).selectById("test_id_123");
    }

    @Test
    void testGetByWorkOrderId() {
        // 创建测试数据列表
        List<ContainerQuotaDO> containerQuotaDOList = new ArrayList<>();
        containerQuotaDOList.add(containerQuotaDO);

        // 模拟DAO查询操作
        when(containerQuotaDAO.selectByWorkOrderId(anyString())).thenReturn(containerQuotaDOList);

        // 执行测试
        List<ContainerQuotaDTO> result = containerQuotaService.getByWorkOrderId(workOrderId);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(containerQuotaDO.getId(), result.get(0).getId());

        // 验证DAO方法被调用
        verify(containerQuotaDAO, times(1)).selectByWorkOrderId(workOrderId);
    }

    @Test
    void testGetBySubOrderId() {
        // 模拟DAO查询操作
        when(containerQuotaDAO.selectBySubOrderId(anyString())).thenReturn(containerQuotaDO);

        // 执行测试
        ContainerQuotaDTO result = containerQuotaService.getBySubOrderId(subOrderId);

        // 验证结果
        assertNotNull(result);
        assertEquals(containerQuotaDO.getId(), result.getId());
        assertEquals(containerQuotaDO.getSubOrderId(), result.getSubOrderId());

        // 验证DAO方法被调用
        verify(containerQuotaDAO, times(1)).selectBySubOrderId(subOrderId);
    }

    @Test
    void testQueryList() {
        // 创建查询条件
        ContainerQuotaQuery query = new ContainerQuotaQuery();
        query.setCqName("测试");
        query.setBusinessSystemId(businessSystemId);

        // 创建测试数据列表
        List<ContainerQuotaDO> containerQuotaDOList = new ArrayList<>();
        containerQuotaDOList.add(containerQuotaDO);

        // 模拟DAO查询操作
        when(containerQuotaDAO.selectList(any(ContainerQuotaQuery.class))).thenReturn(containerQuotaDOList);

        // 执行测试
        List<ContainerQuotaDTO> result = containerQuotaService.queryList(query);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(containerQuotaDO.getCqName(), result.get(0).getCqName());

        // 验证DAO方法被调用
        verify(containerQuotaDAO, times(1)).selectList(query);
    }

    @Test
    void testCreateContainerQuotaWithNullCQModel() {
        // 模拟DAO插入操作
        doNothing().when(containerQuotaDAO).insert(any(ContainerQuotaDO.class));

        // 执行测试（传入null的CQModel）
        ContainerQuotaDTO result = containerQuotaService.createContainerQuota(
                null, workOrderId, subOrderId, businessSystemId, businessSystemName);

        // 验证结果
        assertNotNull(result);
        assertEquals(workOrderId, result.getWorkOrderId());
        assertEquals(subOrderId, result.getSubOrderId());
        assertEquals(businessSystemId, result.getBusinessSystemId());
        assertEquals(businessSystemName, result.getBusinessSystemName());
        // CQModel为null时，相关字段应该为null
        assertNull(result.getCqName());
        assertNull(result.getVCpus());

        // 验证DAO方法被调用
        verify(containerQuotaDAO, times(1)).insert(any(ContainerQuotaDO.class));
    }

    @Test
    void testGetByIdWithNullResult() {
        // 模拟DAO查询返回null
        when(containerQuotaDAO.selectById(anyString())).thenReturn(null);

        // 执行测试
        ContainerQuotaDTO result = containerQuotaService.getById("non_exist_id");

        // 验证结果
        assertNull(result);

        // 验证DAO方法被调用
        verify(containerQuotaDAO, times(1)).selectById("non_exist_id");
    }
}

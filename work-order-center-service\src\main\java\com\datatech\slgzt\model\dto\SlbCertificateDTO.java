package com.datatech.slgzt.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * SLB证书DTO
 */
@Data
@Accessors(chain = true)
public class SlbCertificateDTO {
    
    /**
     * 主键ID
     */
    private String id;

    /**
     * 证书名称
     */
    private String certificateName;

    /**
     * 公钥内容
     */
    private String publicKeyContent;

    /**
     * 私钥内容
     */
    private String privateKeyContent;

    /**
     * 证书类型
     */
    private String certificateType;

    /**
     * 域名编码
     */
    private String domainCode;

    /**
     * 域名名称
     */
    private String domainName;

    /**
     * 关联的监听器关系JSON
     */
    private Map<String,String> slbListenerRel;
} 
package com.datatech.slgzt.model.vo.network.ip;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class IpCheckRc implements Serializable {

    private String cloud;
    @NotNull(message = "资源池编号不能为空")
    private String regionCode;

    @NotNull(message = "二级子网配置项编号")
    private String instanceId;

    @NotNull(message = "需要校验的⼦⽹数据")
    private List<String> subnets;

    private String networkPlane;

}

package com.datatech.slgzt.service.standard;

import com.datatech.slgzt.model.bpmn.ActivityTaskVo;
import com.datatech.slgzt.model.dto.StandardWorkOrderDTO;
import com.datatech.slgzt.model.dto.WorkOrderAuthLogDTO;
import com.datatech.slgzt.model.dto.order.StandardAuditWorkOrderDTO;
import com.datatech.slgzt.model.home.AuditCountVo;
import com.datatech.slgzt.model.nostander.*;
import com.datatech.slgzt.model.query.StandardWorkOrderQuery;
import com.datatech.slgzt.model.usercenter.UserCenterUserDTO;
import com.datatech.slgzt.service.OrderCommonService;
import com.datatech.slgzt.utils.PageResult;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 标准订工单服务
 */
public interface StandardWorkOrderService extends OrderCommonService {

    /**
     * 创建非标订工单并且开始流程
     *
     * @param requestBody 请求体
     */
    void createNonStandardOrderAndStartProcess(StandardWorkOrderDTO dto, String requestPath, UserCenterUserDTO userDTO);

    /**
     * 撤销工单
     * @param dto
     * @param rquestPath
     * @return
     */
    void cancel(String workOrderId);


    @Transactional(rollbackFor = Exception.class)
    String draft(StandardWorkOrderDTO dto, String rquestPath);

    void audit(StandardAuditWorkOrderDTO dto);

    PageResult<StandardWorkOrderDTO> page(StandardWorkOrderQuery query, Long userId);

    void checkFillEscResource(EcsModel model, String workOrderId);

    void checkFillSlbResource(SlbModel model, String workOrderId);

    void checkFillNatResource(NatGatwayModel model, String workOrderId);

    /**
     * evs
     */
    void checkFillEvsResource(MountDataDiskModel model, String workOrderId, AtomicReference<String> vmDomainCode);

    /**
     * eip
     */
    void checkFillEipResource(EipModel model, String workOrderId, AtomicReference<String> vmDomainCode);

    void checkFillObsResource(ObsModel model, String workOrderId);

    void checkFillCQResource(CQModel model, StandardWorkOrderDTO workOrderDTO);

    ActivityTaskVo getTaskNodes(String orderId);
    void getNextTaskNodes(String orderId, List<WorkOrderAuthLogDTO> authLogDTOS,String currentTaskName);

    StandardWorkOrderDTO orderDetail(String orderId);

    AuditCountVo orderCountCount(StandardWorkOrderQuery orderQuery, Long userId);

    void draftSave(StandardWorkOrderDTO dto);


    void draftDel(Long userId, String orderId);

    StandardWorkOrderDTO getDraft(Long userId, String orderId);
}

INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","<PERSON><PERSON><PERSON>_TYPE","CO<PERSON><PERSON>_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'resource-create-process-1','user_task','发起工单',null,1,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","<PERSON><PERSON><PERSON>_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'resource-create-process-1','schema_administrator','架构负责人审核',null,2,null,sysdate,null,null,1,null,'schema_administrator');
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'resource-create-process-1','tenant_task','租户确认',null,3,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'resource-create-process-1','business2_depart_leader','三级业务部门领导审核',null,4,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'resource-create-process-1','business_depart_leader','二级业务部门领导审核',null,5,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'resource-create-process-1','cloud_leader','三级云资源部领导审核',null,6,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'resource-create-process-1','cloud_leader_2','二级云资源部领导审核',null,7,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'resource-create-process-1','network_provisioning','网络开通',null,8,null,sysdate,null,null,1,null,'network_provisioning');
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'resource-create-process-1','resource_creation','资源开通',null,9,null,sysdate,null,null,1,null,'resource_creation');
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'resource-create-process-1','autodit end','开通完成',null,11,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'resource-recovery-process','user_task','发起工单',null,1,null,sysdate,null,null,1,'user_task',null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'resource-recovery-process','schema_administrator','统一架构负责人审核',null,2,null,sysdate,null,null,1,'user_task','schema_administrator');
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'resource-recovery-process','retreat_dimension','交维清退',null,3,null,sysdate,null,null,1,'user_task','retreat_dimension');
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'resource-recovery-process','resource_recovery','资源回收',null,4,null,sysdate,null,null,1,'user_task','resource_recovery');
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'resource-recovery-process','tenant_task','租户回收确认',null,5,null,sysdate,null,null,1,'user_task','tenant_task');
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'resource-recovery-process','network_recovery','网络回收',null,6,null,sysdate,null,null,1,'user_task','network_recovery');
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'resource-recovery-process','autodit end','完成审批',null,7,null,sysdate,null,null,1,'user_task',null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'resource-recovery-process','user_task','发起工单',null,1,null,sysdate,null,null,1,'system',null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'resource-recovery-process','tenant_task','租户回收确认',null,2,null,sysdate,null,null,1,'system',null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'resource-recovery-process','schema_administrator','统一架构负责人审核',null,3,null,sysdate,null,null,1,'system','schema_administrator');
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'resource-recovery-process','retreat_dimension','交维清退',null,4,null,sysdate,null,null,1,'system','retreat_dimension');
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'resource-recovery-process','resource_recovery','资源回收',null,5,null,sysdate,null,null,1,'system','resource_recovery');
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'resource-recovery-process','network_recovery','网络回收',null,6,null,sysdate,null,null,1,'system','network_recovery');
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'resource-recovery-process','autodit end','完成审批',null,7,null,sysdate,null,null,1,'system',null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'time','one_month','1个月','plf_prov_moc_zj_vmware',1,null,sysdate,null,null,1,'month','1');
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'time','three_months','3个月','plf_prov_moc_zj_vmware',2,null,sysdate,null,null,1,'month','3');
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'time','six_months','6个月','plf_prov_moc_zj_vmware',3,null,sysdate,null,null,1,'month','6');
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'time','one_year','1年','plf_prov_moc_zj_vmware',4,null,sysdate,null,null,1,'year','1');
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'time','two_years','2年','plf_prov_moc_zj_vmware',5,null,sysdate,null,null,1,'year','2');
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'goodsSort','ecs','云主机','common',1,null,sysdate,null,null,1,null,'云主机');
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'goodsSort','gcs','GPU云主机','common',2,null,sysdate,null,null,1,null,'GPU云主机');
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'goodsSort','obs','对象存储','common',3,null,sysdate,null,null,1,null,'对象存储');
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'goodsSort','nat','NAT网关','common',4,null,sysdate,null,null,1,null,'NAT网关');
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'goodsSort','slb','负载均衡','common',5,null,sysdate,null,null,1,null,'负载均衡');
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'goodsSort','evs','云硬盘','common',6,null,sysdate,null,null,1,null,'云硬盘');
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'securityDomain','core','核心区',null,1,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'securityDomain','dmz','DMZ区',null,2,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'plane','DCN平面188网段','DCN平面188网段',null,1,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'plane','DCN平面10网段','DCN平面10网段',null,2,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'plane','承载网','承载网',null,3,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'plane','私网地址','私网地址',null,4,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'plane','公网地址','公网地址',null,5,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'functionalModule','app','App',null,1,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'functionalModule','web','web',null,2,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'functionalModule','db','DB',null,3,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'functionalModule','api','API',null,4,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'functionalModule','server','server',null,5,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'functionalModule','lb','LB',null,6,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'functionalModule','other','other',null,7,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'businessSystemType','100000000000','基础通信类系统',null,1,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'businessSystemType','100000000001','增值业务类系统',null,2,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'businessSystemType','100000000002','网管支撑类系统',null,3,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'businessSystemType','100000000003','业务平台类系统',null,4,null,sysdate,null,null,1,null,null);
-- 操作系统通过CONFIG_CODE在MC_IMAGES_T表中获取镜像过滤版本为空的数据
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'imageOs','BCLinux for Anolis','BCLinux for Anolis',null,1,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'imageOs','openEuler-B','openEuler-B',null,2,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'imageOs','Windows Server','Windows Server',null,3,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'imageOs','BCLinux for Euler','BCLinux for Euler',null,4,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'imageOs','openAnolis-B','openAnolis-B',null,5,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'imageOs','CentOS','CentOS',null,6,null,sysdate,null,null,1,null,null);
-- 实例规格通过CONFIG_TYPE去MC_FLAVOR_MODEL_T表中根据STACK_CODE获取
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'ecs','通用型','通用型',null,1,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'ecs','网络云（独有）','网络云（独有）',null,2,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'gcs','通用型','通用型',null,1,null,sysdate,null,null,1,null,null);
-- 实例规格中的系统盘通过CONFIG_TYPE去MC_FLAVOR_MODEL_T表中根据STACK_CODE获取，默认domain_code为plf_prov_moc_zj_vmware
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'evs','100000000004','SAS云盘',null,1,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'evs','100000000005','SSD云盘',null,2,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'evs','100000000021','HDD云盘',null,3,null,sysdate,null,null,1,null,null);
-- 实例规格中的系统盘，数据盘通过CONFIG_TYPE去MC_FLAVOR_MODEL_T表中根据STACK_CODE获取
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'nat','100000000009','小型',null,1,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'nat','100000000010','中型',null,2,null,sysdate,null,null,1,null,null);
-- 实例规格中的eip通过CONFIG_TYPE去MC_FLAVOR_MODEL_T表中根据STACK_CODE获取
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'eip','100000000001','EIP',null,1,null,sysdate,null,null,1,null,null);
-- 实例规格中的slb通过CONFIG_TYPE去MC_FLAVOR_MODEL_T表中根据STACK_CODE获取
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'slb','100000000014','普通型',null,1,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'slb','100000000011','优享型I',null,2,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'slb','100000000012','优享型I',null,3,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'slb','100000000015','高端型I',null,4,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'slb','100000000013','旗舰型',null,5,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'slb','100000000016','高端型II',null,6,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'obs','100000000002','OSS',null,1,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'importanceLevel','1','核心',null,1,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'importanceLevel','2','重要',null,2,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'importanceLevel','3','一般',null,3,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'lifeCycleStates','1','工程',null,1,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'lifeCycleStates','2','在网',null,2,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'lifeCycleStates','3','下线',null,3,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'trueOrFalse','0','否',null,1,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'trueOrFalse','1','是',null,2,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'netRange','10.0.0.0/8','10.0.0.0/8',null,1,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'netRange','172.16.0.0/12','172.16.0.0/12',null,2,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'netRange','192.168.0.0/16','192.168.0.0/16',null,3,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'vmStatus','RUNING','运行中',null,1,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'vmStatus','STOPED','关机',null,2,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'vmStatus','ERROR','错误',null,3,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'vmStatus','CREATING','创建中',null,4,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'vmStatus','DELETED','已删除',null,5,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'userStatus','1','正常',null,1,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'userStatus','2','冻结',null,2,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'userCategory','OA','内部用户',null,1,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'userCategory','ESOP','外部用户',null,2,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'userCategory','MAN','厂商用户',null,3,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'evsStatus','EXPIRE','超过有效期',null,1,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'evsStatus','ACTIVE','运行中',null,2,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'evsStatus','USED','运行中',null,3,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'evsStatus','DELETED','已删除',null,4,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'obsStatus','EXPIRE','超过有效期',null,1,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'obsStatus','ACTIVE','运行中',null,2,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'obsStatus','DELETED','已删除',null,3,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'slbStatus','EXPIRE','超过有效期',null,1,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'slbStatus','BUILDING','运行中',null,2,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'slbStatus','ON','运行中',null,3,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'slbStatus','DELETED','已删除',null,4,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'natStatus','EXPIRE','超过有效期',null,1,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'natStatus','AVAILABLE','运行中',null,2,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'natStatus','RUNING','运行中',null,3,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'natStatus','DELETED','已删除',null,4,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'vmStatus','STARTING','开机中',null,1,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'vmStatus','STOPING','关机中',null,2,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'vmStatus','RESTARTING','重启中',null,3,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'recoveryStatus','0','-',null,1,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'recoveryStatus','1','待回收',null,2,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'recoveryStatus','2','回收中',null,3,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'recoveryStatus','3','回收完成',null,4,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'recoveryStatus','4','回收失败',null,5,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'sysDisk','100000000003','SAS云盘',null,1,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'sysDisk','100000000006','SSD云盘',null,2,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'sysDisk','100000000020','HDD云盘',null,3,null,sysdate,null,null,1,null,null);
--LK 增加obs状态
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.NEXTVAL,'obsStatus','RUNNING','运行中',null,4,null,sysdate,null,null,1,null,null);

-- 工单创建对应节点描述
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'subscribe','user_task','发起工单',null,1,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'subscribe','schema_administrator','架构负责人审核',null,2,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'subscribe','tenant_task','租户确认',null,3,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'subscribe','business_depart_leader2','三级业务部门领导审核',null,4,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'subscribe','business_depart_leader','二级业务部门领导审核',null,4,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'subscribe','cloud_leader','三级云资源部领导审核',null,5,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'subscribe','cloud_leader_2','二级云资源部领导审核',null,6,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'subscribe','network_provisioning','网络开通',null,7,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'subscribe','resource_creation','资源开通',null,8,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'subscribe','CLOSE','工单已撤销',null,9,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'subscribe','END','完成审批',null,10,null,sysdate,null,null,1,null,null);

-- 工单回收对应节点描述
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'recovery','user_task','发起工单',null,1,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'recovery','business_depart_leader2','三级业务部门领导审核',null,2,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'recovery','operation_group','资源回收',null,3,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'recovery','tenant_task','租户确认',null,4,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'recovery','CLOSE','工单已撤销',null,5,null,sysdate,null,null,1,null,null);
INSERT INTO "SLGZT"."WOC_CONFIG" ("ID","CONFIG_TYPE","CONFIG_CODE","CONFIG_NAME","CONFIG_DESC","SORT","CREATED_BY","CREATED_TIME","UPDATED_BY","UPDATED_TIME","STATUS","CONFIG_GROUP","CONFIG_VALUE" ) VALUES (SEQ_WOC_CONFIG.nextval,'recovery','END','完成审批',null,6,null,sysdate,null,null,1,null,null);
-- OBS定时任务表
CREATE TABLE "SLGZT"."WOC_OBS_OPEN_TASK"
(
    "PRODUCT_ORDER_ID" NUMBER NOT NULL,
    "WORK_ORDER_ID" VARCHAR2(32),
    "STATUS" VARCHAR2(255),
    "CREATE_TIME" TIMESTAMP(0) DEFAULT SYSDATE,
    "UPDATE_TIME" TIMESTAMP(0) DEFAULT SYSDATE,
    "MESSAGE" VARCHAR2(255),
    CONSTRAINT "PK_WOC_OBS_OPEN_TASK" NOT CLUSTER PRIMARY KEY("PRODUCT_ORDER_ID")) STORAGE(ON "SLGZT", CLUSTERBTR);
-- 任务中心模板
INSERT INTO ERC_TASK_API_DEF ("ID","TEMPLATE_ID","TASK_ID","API_ID","MONITOR_COUNT","MONITOR_INTERVAL","CREATED_TIME","UPDATED_TIME","CREATED_BY","UPDATED_BY","STATUS") VALUES ('8640a12507437f0aabb8b1932515ae08', 'a2dcd575760b4d2598047ec22e1e8404', 'fb44ed2f92d8426eb74eab7ba62a88eb','be35c379a5cb46ee9466360b176ee5c4',360,10,sysdate(),null ,null,null,1);
INSERT INTO ERC_TASK_API_DEF ("ID","TEMPLATE_ID","TASK_ID","API_ID","MONITOR_COUNT","MONITOR_INTERVAL","CREATED_TIME","UPDATED_TIME","CREATED_BY","UPDATED_BY","STATUS") VALUES ('a8b31a518e359104173dea69e330a9ba', 'a2dcd575760b4d2598047ec22e1e8404', 'a01076735a29458aac1cc28914cf70bd','8df482ee7bd54b27b5d3a18db5973821',0,0,sysdate(),null ,null,null,1);
INSERT INTO ERC_TEMPLATE_TASK_DEF ("ID","TEMPLATE_ID","TASK_ID","RETRY_COUNT","REL_RULE","CREATED_TIME","UPDATED_TIME","CREATED_BY","UPDATED_BY","STATUS") VALUES ('570d1a929c211c16ffdfd4a213e1dca6', 'a2dcd575760b4d2598047ec22e1e8404', 'fb44ed2f92d8426eb74eab7ba62a88eb', 3, 'MULTIPLE', SYSDATE(),NULL,NULL,NULL,1);
INSERT INTO ERC_TEMPLATE_TASK_DEF ("ID","TEMPLATE_ID","TASK_ID","RETRY_COUNT","REL_RULE","CREATED_TIME","UPDATED_TIME","CREATED_BY","UPDATED_BY","STATUS") VALUES ('55e544c773d4d746dd29cf785f031a25', 'a2dcd575760b4d2598047ec22e1e8404', 'a01076735a29458aac1cc28914cf70bd', 3, 'MULTIPLE', SYSDATE(),NULL,NULL,NULL,1);
INSERT INTO ERC_TASKS_REL_DEF ("ID","TEMPLATE_ID","TASK_ID","REL_TASK_ID","CREATED_TIME","UPDATED_TIME","CREATED_BY","UPDATED_BY","STATUS","REL_TYPE","DESCRIPTION","REL_STATE") VALUES ('65818b4856f00b05766f7678c5cdb4fb', 'a2dcd575760b4d2598047ec22e1e8404', 'a01076735a29458aac1cc28914cf70bd', 'fb44ed2f92d8426eb74eab7ba62a88eb', SYSDATE(), NULL, NULL,NULL,1,'EXECUTE','弹性ip变更任务状态通知依赖EIP变更完成',24);
INSERT INTO ERC_TASKS_REL_DEF ("ID","TEMPLATE_ID","TASK_ID","REL_TASK_ID","CREATED_TIME","UPDATED_TIME","CREATED_BY","UPDATED_BY","STATUS","REL_TYPE","DESCRIPTION","REL_STATE") VALUES ('4aec691a8ebbe924d9aec1a4b9dcb170', 'a2dcd575760b4d2598047ec22e1e8404', 'a01076735a29458aac1cc28914cf70bd', 'fb44ed2f92d8426eb74eab7ba62a88eb', SYSDATE(), NULL, NULL,NULL,1,'CREATE','EIP变更状态通知创建依赖EIP变更任务创建',null );
INSERT INTO ERC_TASKS_REL_DEF ("ID","TEMPLATE_ID","TASK_ID","REL_TASK_ID","CREATED_TIME","UPDATED_TIME","CREATED_BY","UPDATED_BY","STATUS","REL_TYPE","DESCRIPTION","REL_STATE") VALUES ('af4c309d93b86aa027d231baf8617676', 'a2dcd575760b4d2598047ec22e1e8404', 'e62c4106800a41ef9a8e23a27193d00b', 'fb44ed2f92d8426eb74eab7ba62a88eb', SYSDATE(), NULL, NULL,NULL,1,'EXECUTE','主任务状态通知依赖于EIP变更任务执行完成',24);
INSERT INTO ERC_TASKS_REL_DEF ("ID","TEMPLATE_ID","TASK_ID","REL_TASK_ID","CREATED_TIME","UPDATED_TIME","CREATED_BY","UPDATED_BY","STATUS","REL_TYPE","DESCRIPTION","REL_STATE") VALUES ('5beca3639ba6a25826e441319adfe01c', 'a2dcd575760b4d2598047ec22e1e8404', 'e62c4106800a41ef9a8e23a27193d00b', 'a01076735a29458aac1cc28914cf70bd', SYSDATE(), NULL, NULL,NULL,1,'EXECUTE','主任务状态通知执行依赖EIP变更任务状态通知任务执行完成',24);
-- 任务中心模板增加依赖关系（网卡1创建依赖挂载任务）
INSERT INTO ERC_TASKS_REL_DEF ("ID","TEMPLATE_ID","TASK_ID","REL_TASK_ID","CREATED_TIME","UPDATED_TIME","CREATED_BY","UPDATED_BY","STATUS","REL_TYPE","DESCRIPTION","REL_STATE" ) VALUES ('f15820dc247779c13f70593f032df7a2','b9462ff5cc5adc4464300ac64693d394', '0dd942a3c076adc6c808ba47209be0eb', '0a8b0ff399ab447b974849b351d5d649', SYSDATE(), NULL, NULL,NULL,1,'EXECUTE','网卡1创建依赖挂盘任务',8);
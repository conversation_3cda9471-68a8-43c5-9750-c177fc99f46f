package com.datatech.slgzt.model.query;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月10日 14:44:07
 */
@Data
@Accessors(chain = true)
public class ImagesQuery {

    //azId
    private Long azId;


    //regionId
    private Long regionId;

    private String version;

    private String osType;

    private String domainCode;

    private List<String> domainCodeList;

    // 镜像名称
    private String name;

    // 描述
    private String description;

    private String diskFormat;

    private Integer shares;

    private String osTypeSource;

    private Integer pageSize;

    private Integer pageNum;
}

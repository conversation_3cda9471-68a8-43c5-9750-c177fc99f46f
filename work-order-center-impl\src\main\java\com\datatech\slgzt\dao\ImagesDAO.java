package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.datatech.slgzt.dao.mapper.ImagesMapper;
import com.datatech.slgzt.dao.model.ImagesDO;
import com.datatech.slgzt.model.query.ImagesQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月10日 14:40:36
 */
@Repository
public class ImagesDAO {


    @Resource
    private ImagesMapper mapper;


    /**
     * list
     */
    public List<ImagesDO> list(ImagesQuery query) {
        return mapper.selectList(Wrappers.<ImagesDO>lambdaQuery()
                .eq(ObjNullUtils.isNotNull(query.getDomainCode()), ImagesDO::getDomainCode, query.getDomainCode())
                .in(ObjNullUtils.isNotNull(query.getDomainCodeList()), ImagesDO::getDomainCode, query.getDomainCodeList())
                .eq(ObjNullUtils.isNotNull(query.getAzId()), ImagesDO::getAzId, query.getAzId())
                .eq(ObjNullUtils.isNotNull(query.getRegionId()), ImagesDO::getRegionId, query.getRegionId())
                .eq(ObjNullUtils.isNotNull(query.getVersion()), ImagesDO::getVersion, query.getVersion())
                .eq(ObjNullUtils.isNotNull(query.getOsType()), ImagesDO::getOsType, query.getOsType())
                .eq(ObjNullUtils.isNotNull(query.getShares()), ImagesDO::getShares, query.getShares())

        );
    }


    /**
     * list
     */
    public List<ImagesDO> queryImage(ImagesQuery query) {
        return mapper.selectList(Wrappers.<ImagesDO>lambdaQuery()
                .eq(ObjNullUtils.isNotNull(query.getDomainCode()), ImagesDO::getDomainCode, query.getDomainCode())
                .eq(ObjNullUtils.isNotNull(query.getDiskFormat()), ImagesDO::getDiskFormat, query.getDiskFormat())
                .in(ObjNullUtils.isNotNull(query.getDomainCodeList()), ImagesDO::getDomainCode, query.getDomainCodeList())
                .eq(ObjNullUtils.isNotNull(query.getAzId()), ImagesDO::getAzId, query.getAzId())
                .eq(ObjNullUtils.isNotNull(query.getRegionId()), ImagesDO::getRegionId, query.getRegionId())
                .like(ObjNullUtils.isNotNull(query.getVersion()), ImagesDO::getVersion, query.getVersion())
                .like(ObjNullUtils.isNotNull(query.getName()), ImagesDO::getName, query.getName())
                .like(ObjNullUtils.isNotNull(query.getDescription()), ImagesDO::getDescription, query.getDescription())
                .eq(ObjNullUtils.isNotNull(query.getOsType()), ImagesDO::getOsType, query.getOsType())
                .eq(ObjNullUtils.isNotNull(query.getShares()), ImagesDO::getShares, query.getShares())
                .eq(ObjNullUtils.isNotNull(query.getOsTypeSource()), ImagesDO::getOsTypeSource, query.getOsTypeSource())
        );
    }

}

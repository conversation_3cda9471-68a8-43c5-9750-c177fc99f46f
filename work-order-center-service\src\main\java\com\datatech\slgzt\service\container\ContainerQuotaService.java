package com.datatech.slgzt.service.container;

import com.datatech.slgzt.model.dto.ContainerQuotaDTO;
import com.datatech.slgzt.model.nostander.CQModel;
import com.datatech.slgzt.model.query.ContainerQuotaQuery;
import com.datatech.slgzt.utils.PageResult;

import java.util.List;

/**
 * 容器配额服务接口
 * <AUTHOR>
 * @description 容器配额业务逻辑接口
 * @date 2025年05月27日
 */
public interface ContainerQuotaService {

    /**
     * 创建容器配额记录
     * @param cqModel CQ模型数据
     * @param workOrderId 工单ID
     * @param subOrderId 子订单ID
     * @param businessSystemId 业务系统ID
     * @param businessSystemName 业务系统名称
     * @return 容器配额DTO
     */
    ContainerQuotaDTO createContainerQuota(CQModel cqModel, String workOrderId, String subOrderId,
                                          Long businessSystemId, String businessSystemName);

    /**
     * 根据ID查询容器配额
     * @param id 主键ID
     * @return 容器配额DTO
     */
    ContainerQuotaDTO getById(String id);

    /**
     * 根据工单ID查询容器配额列表
     * @param workOrderId 工单ID
     * @return 容器配额列表
     */
    List<ContainerQuotaDTO> getByWorkOrderId(String workOrderId);

    /**
     * 根据子订单ID查询容器配额
     * @param subOrderId 子订单ID
     * @return 容器配额DTO
     */
    ContainerQuotaDTO getBySubOrderId(String subOrderId);

    /**
     * 分页查询容器配额
     * @param query 查询条件
     * @return 分页结果
     */
    PageResult<ContainerQuotaDTO> queryPage(ContainerQuotaQuery query);

    /**
     * 查询容器配额列表
     * @param query 查询条件
     * @return 容器配额列表
     */
    List<ContainerQuotaDTO> queryList(ContainerQuotaQuery query);
}

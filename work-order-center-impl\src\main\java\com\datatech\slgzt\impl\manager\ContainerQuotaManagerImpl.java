package com.datatech.slgzt.impl.manager;

import cn.hutool.core.util.IdUtil;
import com.datatech.slgzt.convert.ContainerQuotaManagerConvert;
import com.datatech.slgzt.dao.ContainerQuotaDAO;
import com.datatech.slgzt.dao.model.container.ContainerQuotaDO;
import com.datatech.slgzt.enums.StatusEnum;
import com.datatech.slgzt.manager.ContainerQuotaManager;
import com.datatech.slgzt.model.dto.ContainerQuotaDTO;
import com.datatech.slgzt.model.nostander.CQModel;
import com.datatech.slgzt.model.query.ContainerQuotaQuery;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.utils.StreamUtils;
import com.datatech.slgzt.warpper.PageWarppers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 容器配额管理器实现类
 *
 * <AUTHOR>
 * @description 容器配额管理器业务逻辑实现
 * @date 2025年05月27日
 */
@Slf4j
@Service
public class ContainerQuotaManagerImpl implements ContainerQuotaManager {

    @Resource
    private ContainerQuotaDAO containerQuotaDAO;

    @Resource
    private ContainerQuotaManagerConvert convert;

    @Override
    public ContainerQuotaDTO createContainerQuota(CQModel cqModel, String workOrderId, String subOrderId,
                                                  Long businessSystemId, String businessSystemName) {
        log.info("创建容器配额记录，工单ID：{}，子订单ID：{}，业务系统ID：{}，业务系统名称：{}",
                workOrderId, subOrderId, businessSystemId, businessSystemName);

        // 创建容器配额DO对象
        ContainerQuotaDO containerQuotaDO = new ContainerQuotaDO();

        // 设置基础信息
        containerQuotaDO.setId(IdUtil.getSnowflake().nextId());
        containerQuotaDO.setWorkOrderId(workOrderId);
        containerQuotaDO.setSubOrderId(subOrderId);
        containerQuotaDO.setBusinessSystemId(businessSystemId);
        containerQuotaDO.setBusinessSystemName(businessSystemName);
        containerQuotaDO.setStatus(StatusEnum.NORMAL.code().toString());
        containerQuotaDO.setCreateTime(LocalDateTime.now());
        containerQuotaDO.setModifyTime(LocalDateTime.now());

        // 从CQModel复制属性
        if (cqModel != null) {
            containerQuotaDO = convert.cqModel2DO(cqModel);
        }

        // 保存到数据库
        containerQuotaDAO.insert(containerQuotaDO);

        log.info("容器配额记录创建成功，ID：{}", containerQuotaDO.getId());

        // 转换为DTO返回
        return convert.do2DTO(containerQuotaDO);
    }

    @Override
    public ContainerQuotaDTO getById(String id) {
        log.info("根据ID查询容器配额，ID：{}", id);
        ContainerQuotaDO containerQuotaDO = containerQuotaDAO.selectById(id);
        return convert.do2DTO(containerQuotaDO);
    }

    @Override
    public List<ContainerQuotaDTO> getByWorkOrderId(String workOrderId) {
        log.info("根据工单ID查询容器配额列表，工单ID：{}", workOrderId);
        List<ContainerQuotaDO> containerQuotaDOList = containerQuotaDAO.selectByWorkOrderId(workOrderId);
        return StreamUtils.mapArray(containerQuotaDOList, convert::do2DTO);
    }

    @Override
    public ContainerQuotaDTO getBySubOrderId(String subOrderId) {
        log.info("根据子订单ID查询容器配额，子订单ID：{}", subOrderId);
        ContainerQuotaDO containerQuotaDO = containerQuotaDAO.selectBySubOrderId(subOrderId);
        return convert.do2DTO(containerQuotaDO);
    }

    @Override
    public PageResult<ContainerQuotaDTO> queryPage(ContainerQuotaQuery query) {
        log.info("分页查询容器配额，查询条件：{}", query);

        // 使用PageHelper进行分页
        if (query.getPageNum() != null && query.getPageSize() != null) {
            PageHelper.startPage(query.getPageNum(), query.getPageSize());
        }
        List<ContainerQuotaDO> containerQuotaDOList = containerQuotaDAO.selectList(query);

        // 转换为DTO分页结果
        PageResult<ContainerQuotaDTO> result = PageWarppers.box(new PageInfo<>(containerQuotaDOList), convert::do2DTO);

        log.info("分页查询容器配额完成，总记录数：{}，当前页记录数：{}",
                result.getTotal(), result.getRecords().size());

        return result;
    }

    @Override
    public List<ContainerQuotaDTO> queryList(ContainerQuotaQuery query) {
        log.info("查询容器配额列表，查询条件：{}", query);
        List<ContainerQuotaDO> containerQuotaDOList = containerQuotaDAO.selectList(query);
        return StreamUtils.mapArray(containerQuotaDOList, convert::do2DTO);
    }
}

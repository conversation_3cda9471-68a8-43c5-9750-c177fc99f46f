package com.datatech.slgzt.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.datatech.slgzt.dao.mapper.ContainerQuotaMapper;
import com.datatech.slgzt.dao.model.container.ContainerQuotaDO;
import com.datatech.slgzt.enums.StatusEnum;
import com.datatech.slgzt.model.query.ContainerQuotaQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * 容器配额DAO
 * <AUTHOR>
 * @description 容器配额数据访问对象
 * @date 2025年05月27日
 */
@Repository
public class ContainerQuotaDAO {

    @Resource
    private ContainerQuotaMapper mapper;

    /**
     * 插入容器配额记录
     * @param containerQuotaDO 容器配额数据对象
     */
    public void insert(ContainerQuotaDO containerQuotaDO) {
        mapper.insert(containerQuotaDO);
    }

    /**
     * 根据ID查询容器配额
     * @param id 主键ID
     * @return 容器配额数据对象
     */
    public ContainerQuotaDO selectById(String id) {
        return mapper.selectById(id);
    }

    /**
     * 根据工单ID查询容器配额列表
     * @param workOrderId 工单ID
     * @return 容器配额列表
     */
    public List<ContainerQuotaDO> selectByWorkOrderId(String workOrderId) {
        LambdaQueryWrapper<ContainerQuotaDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContainerQuotaDO::getWorkOrderId, workOrderId)
                   .eq(ContainerQuotaDO::getEnabled, StatusEnum.NORMAL.code());
        return mapper.selectList(queryWrapper);
    }

    /**
     * 根据子订单ID查询容器配额
     * @param subOrderId 子订单ID
     * @return 容器配额数据对象
     */
    public ContainerQuotaDO selectBySubOrderId(String subOrderId) {
        LambdaQueryWrapper<ContainerQuotaDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContainerQuotaDO::getSubOrderId, subOrderId)
                   .eq(ContainerQuotaDO::getEnabled, StatusEnum.NORMAL.code());
        return mapper.selectOne(queryWrapper);
    }

    /**
     * 分页查询容器配额
     * @param query 查询条件
     * @return 分页结果
     */
    public IPage<ContainerQuotaDO> selectPage(ContainerQuotaQuery query) {
        Page<ContainerQuotaDO> page = new Page<>(query.getPageNum(), query.getPageSize());
        QueryWrapper<ContainerQuotaDO> queryWrapper = buildQueryWrapper(query);
        return mapper.selectPage(page, queryWrapper);
    }

    /**
     * 查询容器配额列表
     * @param query 查询条件
     * @return 容器配额列表
     */
    public List<ContainerQuotaDO> selectList(ContainerQuotaQuery query) {
        QueryWrapper<ContainerQuotaDO> queryWrapper = buildQueryWrapper(query);
        return mapper.selectList(queryWrapper);
    }

    /**
     * 构建查询条件
     * @param query 查询参数
     * @return 查询包装器
     */
    private QueryWrapper<ContainerQuotaDO> buildQueryWrapper(ContainerQuotaQuery query) {
        QueryWrapper<ContainerQuotaDO> queryWrapper = new QueryWrapper<>();
        
        // 默认查询启用状态的记录
        queryWrapper.eq("ENABLED", StatusEnum.NORMAL.code());
        
        // 配额名称模糊查询
        if (StringUtils.hasText(query.getCqName())) {
            queryWrapper.like("CQ_NAME", query.getCqName());
        }
        
        // 业务系统ID精确查询
        if (ObjNullUtils.isNotNull(query.getBusinessSystemId())) {
            queryWrapper.eq("BUSINESS_SYSTEM_ID", query.getBusinessSystemId());
        }
        
        // 业务系统名称模糊查询
        if (StringUtils.hasText(query.getBusinessSystemName())) {
            queryWrapper.like("BUSINESS_SYSTEM_NAME", query.getBusinessSystemName());
        }
        
        // 工单ID精确查询
        if (StringUtils.hasText(query.getWorkOrderId())) {
            queryWrapper.eq("WORK_ORDER_ID", query.getWorkOrderId());
        }
        
        // 状态查询
        if (StringUtils.hasText(query.getStatus())) {
            queryWrapper.eq("STATUS", query.getStatus());
        }
        
        // 云平台编码查询
        if (StringUtils.hasText(query.getDomainCode())) {
            queryWrapper.eq("DOMAIN_CODE", query.getDomainCode());
        }
        
        // 资源池编码查询
        if (StringUtils.hasText(query.getRegionCode())) {
            queryWrapper.eq("REGION_CODE", query.getRegionCode());
        }
        
        // 按创建时间倒序排列
        queryWrapper.orderByDesc("CREATE_TIME");
        
        return queryWrapper;
    }
}

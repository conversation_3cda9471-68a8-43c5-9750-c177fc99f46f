package com.datatech.slgzt.model.query;

import lombok.Data;

/**
 * 容器配额查询条件
 * <AUTHOR>
 * @description 容器配额查询参数对象
 * @date 2025年05月27日
 */
@Data
public class ContainerQuotaQuery {

    /**
     * 页码
     */
    private Integer pageNum = 1;

    /**
     * 页大小
     */
    private Integer pageSize = 10;

    /**
     * 配额名称（支持模糊查询）
     */
    private String cqName;

    /**
     * 业务系统ID
     */
    private Long businessSystemId;

    /**
     * 业务系统名称（支持模糊查询）
     */
    private String businessSystemName;

    /**
     * 工单ID
     */
    private String workOrderId;

    /**
     * 子订单ID
     */
    private String subOrderId;

    /**
     * 状态
     */
    private String status;

    /**
     * 云平台编码
     */
    private String domainCode;

    /**
     * 云平台名称
     */
    private String domainName;

    /**
     * 资源池编码
     */
    private String regionCode;

    /**
     * 资源池名称
     */
    private String regionName;

    /**
     * 4A账号
     */
    private String a4Account;

    /**
     * 申请用户ID
     */
    private Long applyUserId;

    /**
     * 申请用户名称（模糊查询）
     */
    private String applyUserName;

    /**
     * 资源池ID
     */
    private String regionId;

    /**
     * 云类型编码
     */
    private String catalogueDomainCode;

    /**
     * 4A账号绑定的手机
     */
    private String a4Phone;

    /**
     * 申请时长
     */
    private String applyTime;

    /**
     * 容器配额-核心数
     */
    private Integer vCpus;

    /**
     * 容器配额-内存大小，单位G
     */
    private Integer ram;

    /**
     * GPU算力
     */
    private Integer gpuRatio;

    /**
     * GPU显存大小，单位GB
     */
    private Integer gpuVirtualMemory;

    /**
     * 物理GPU卡(个)
     */
    private Integer gpuCore;

    /**
     * 虚拟GPU卡(个)
     */
    private Integer gpuVirtualCore;

    /**
     * 创建时间开始
     */
    private String createTimeStart;

    /**
     * 创建时间结束
     */
    private String createTimeEnd;
}

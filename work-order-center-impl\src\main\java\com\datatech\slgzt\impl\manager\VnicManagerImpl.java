package com.datatech.slgzt.impl.manager;

import cn.hutool.core.collection.CollectionUtil;
import com.datatech.slgzt.convert.VnicManagerConvert;
import com.datatech.slgzt.dao.VnicDAO;
import com.datatech.slgzt.dao.model.VnicDO;
import com.datatech.slgzt.manager.VnicManager;
import com.datatech.slgzt.model.dto.VnicDTO;
import com.datatech.slgzt.model.query.VnicQuery;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.warpper.PageWarppers;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 虚拟网卡Manager实现
 */
@Service
public class VnicManagerImpl implements VnicManager {

    @Resource
    private VnicDAO vnicDAO;
    
    @Resource
    private VnicManagerConvert vnicManagerConvert;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(VnicDTO dto) {
        VnicDO vnicDO = vnicManagerConvert.convertToDO(dto);
        vnicDO.setCreateTime(LocalDateTime.now());
        vnicDO.setModifyTime(LocalDateTime.now());
        vnicDO.setEnabled(true);
        vnicDAO.insert(vnicDO);
    }

    @Override
    public void update(VnicDTO dto) {
        VnicDO vnicDO = vnicManagerConvert.convertToDO(dto);
        vnicDO.setModifyTime(LocalDateTime.now());
        vnicDAO.update(vnicDO);
    }

    @Override
    public void updateVmById(VnicDTO dto) {
        vnicDAO.updateVmById(dto.getId(), dto.getVmId(), dto.getVmName());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        vnicDAO.delete(id);
    }
    
    @Override
    public VnicDTO getById(String id) {
        VnicDO vnicDO = vnicDAO.getById(id);
        return vnicManagerConvert.convert(vnicDO);
    }

    @Override
    public VnicDTO getByVmId(String vmId) {
        VnicQuery vnicQuery = new VnicQuery();
        vnicQuery.setVmId(vmId);
        List<VnicDO> list = vnicDAO.list(vnicQuery);
        if (CollectionUtil.isNotEmpty(list)) {
            return vnicManagerConvert.convert(list.get(0));
        } else {
            return null;
        }
    }

    @Override
    public VnicDTO getByVnicId(String vnicId) {
        VnicDO vnicDO = vnicDAO.getByVnicId(vnicId);
        return vnicManagerConvert.convert(vnicDO);
    }

    @Override
    public PageResult<VnicDTO> page(VnicQuery query) {
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<VnicDO> list = vnicDAO.list(query);
        return PageWarppers.box(new PageInfo<>(list), vnicManagerConvert::convert);
    }
} 
package com.datatech.slgzt.consumer;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.datatech.slgzt.enums.ChangeTypeEnum;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.VmOperationEnum;
import com.datatech.slgzt.manager.ChangeWorkOrderManager;
import com.datatech.slgzt.manager.ChangeWorkOrderProductManager;
import com.datatech.slgzt.manager.ResourceDetailManager;
import com.datatech.slgzt.model.change.ChangeSlbModel;
import com.datatech.slgzt.model.dto.ChangeWorkOrderProductDTO;
import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import com.datatech.slgzt.model.dto.StandardWorkOrderProductDTO;
import com.datatech.slgzt.model.dto.change.ChangeWorkOrderDTO;
import com.datatech.slgzt.model.query.ChangeWorkOrderProductQuery;
import com.datatech.slgzt.model.query.VmOperateQuery;
import com.datatech.slgzt.service.change.ChangeWorkOrderService;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.StreamUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月12日 15:20:59
 */
@Slf4j
@Service
public class ChangeResourceDetailConsumer {



    @Resource
    private ResourceDetailManager resourceDetailManager;


    @Resource
    private ChangeWorkOrderProductManager productManager;
    @Resource
    private ChangeWorkOrderManager changeWorkOrderManager;
    @Resource
    private ChangeWorkOrderService changeWorkOrderService;

    @Transactional(rollbackFor = Exception.class)
    public void consumeResourceMessage(List<ResourceDetailDTO> list) {
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        //处理集合封装资源详情
        Map<Long, ResourceDetailDTO> gooodId2IdentityMap = StreamUtils.toMap(list, ResourceDetailDTO::getGoodsOrderId);
        log.info("监听任务消息: 标准工单对象列表={}", JSON.toJSON(gooodId2IdentityMap));
        List<ChangeWorkOrderProductDTO> productDTOS = productManager.list(new ChangeWorkOrderProductQuery()
                .setSubOrderIds(gooodId2IdentityMap.keySet()));
        if (CollectionUtil.isNotEmpty(productDTOS)) {
            productDTOS.forEach(productDTO -> {
                ResourceDetailDTO fixDetail = gooodId2IdentityMap.get(productDTO.getSubOrderId());
                if (ObjNullUtils.isNull(fixDetail.getDeviceId())) {
                    return;
                }
                //从数据库查询detail对象
                ResourceDetailDTO dbDetail = resourceDetailManager.getByDeviceId(fixDetail.getDeviceId());
                if (Objects.isNull(dbDetail)) {
                    log.error("资源详情表中没有找到对应的资源信息，kafka传入的对象fixDetail={}", JSON.toJSONString(fixDetail));
                    return;
                }
                ChangeWorkOrderDTO changeWorkOrderDTO = changeWorkOrderManager.getById(productDTO.getWorkOrderId());
                if (changeWorkOrderDTO == null) {
                    log.warn("can not find changeWorkOrderDTO by id:{}, productDTO:{}", productDTO.getWorkOrderId(), productDTO);
                }
                switch (ProductTypeEnum.getByCode(fixDetail.getType())) {
                    case ECS:
                    case GCS:
                    case MYSQL:
                    case REDIS:
                        coverEcs(changeWorkOrderDTO, dbDetail, fixDetail);
                        break;
                    case EIP:
                        coverEip(changeWorkOrderDTO, dbDetail, fixDetail);
                        break;
                    case EVS:
                        convertEvs(changeWorkOrderDTO, dbDetail, fixDetail);
                        break;
                    case OBS:
                        //coverObs(dbDetail, productDTO);
                        ////加个补充逻辑，更新工单为开通成功
                        //productManager.updateStatusById(productDTO.getId(), ResOpenEnum.OPEN_SUCCESS.getCode());
                        //productManager.updateStatusByParentId(productDTO.getId(), ResOpenEnum.OPEN_SUCCESS.getCode());
                        //obsOpenTaskMapper.updateByProductOrderId(ResOpenEnum.OPEN_SUCCESS.getCode(), productDTO.getId());
                        break;
                    case SLB:
                        coverSlb(changeWorkOrderDTO, dbDetail, fixDetail, productDTO);
                        break;
                    case NAT:
                       // coverNat(dbDetail, productDTO);
                        break;
                    default:
                }
                try {
                    changeWorkOrderService.checkAndSendSMS(dbDetail.getId());
                } catch (Exception e) {
                    log.warn("短信发送失败, dbDetail:{}, msg:{}", dbDetail, ExceptionUtils.getStackTrace(e));
                }
            });
        };
    }

    public void coverEcs(ChangeWorkOrderDTO changeWorkOrderDTO, ResourceDetailDTO dbDetail, ResourceDetailDTO fixDetail) {
        ResourceDetailDTO ecsUpdateDTO = new ResourceDetailDTO();
        ecsUpdateDTO.setId(dbDetail.getId());
        ecsUpdateDTO.setSpec(fixDetail.getSpec());
        ecsUpdateDTO.setDataDisk(fixDetail.getDataDisk());
        ecsUpdateDTO.setBandWidth(fixDetail.getBandWidth());
        fillHistoryChangeOrder(changeWorkOrderDTO, dbDetail, ecsUpdateDTO);
        resourceDetailManager.updateById(ecsUpdateDTO);
        //如果云主机下面有挂载云硬盘、eip，要更新对应子产品的值
        //云硬盘
        if (StringUtils.isNotBlank(fixDetail.getVolumeId())) {
            List<String> list = Arrays.asList(fixDetail.getVolumeId().split(","));
            for (int i = 0; i < list.size(); i++) {
                ResourceDetailDTO evsUpdateDTO = resourceDetailManager.getByDeviceId(list.get(i));
                evsUpdateDTO.setDataDisk(fixDetail.getDataDisk().split(",")[i]);
                resourceDetailManager.updateById(evsUpdateDTO);
            }
        }
        //eip
        if (StringUtils.isNotBlank(fixDetail.getEipId())) {
            ResourceDetailDTO eipUpdateDTO = resourceDetailManager.getByDeviceId(fixDetail.getEipId());
            eipUpdateDTO.setBandWidth(fixDetail.getBandWidth());
            resourceDetailManager.updateById(eipUpdateDTO);
        }
    }

    public void convertEvs(ChangeWorkOrderDTO changeWorkOrderDTO, ResourceDetailDTO dbDetail, ResourceDetailDTO fixDetail) {
        ResourceDetailDTO updateDTO = new ResourceDetailDTO();
        updateDTO.setId(dbDetail.getId());
        String dataDisk = fixDetail.getDataDisk();
        updateDTO.setDataDisk(dataDisk);
        updateDTO.setVolumeId(fixDetail.getVolumeId());
        fillHistoryChangeOrder(changeWorkOrderDTO, dbDetail, updateDTO);
        resourceDetailManager.updateById(updateDTO);
        String vmId = fixDetail.getVmId();
        if (StringUtils.isNotBlank(vmId)) {
            //如果是挂载了云主机，更新对应的云主机挂盘id和数据盘容量
            ResourceDetailDTO ecsDetail = resourceDetailManager.getByDeviceId(vmId);
            String volumeId = ecsDetail.getVolumeId();
            if (StringUtils.isNotBlank(volumeId)) {
                List<String> list = Arrays.asList(volumeId.split(","));
                int index = list.indexOf(fixDetail.getDeviceId());
                List<String> dataDisks = new ArrayList<>(ListUtil.of(ecsDetail.getDataDisk().split(",")));
                dataDisks.remove(index);
                dataDisks.add(index, dataDisk);
                ecsDetail.setDataDisk(String.join(",", dataDisks));
                resourceDetailManager.updateById(ecsDetail);
            }
        }
    }

    private void coverEip(ChangeWorkOrderDTO changeWorkOrderDTO, ResourceDetailDTO dbDetail, ResourceDetailDTO fixDetail) {
        ResourceDetailDTO updateDTO = new ResourceDetailDTO();
        updateDTO.setId(dbDetail.getId());
        updateDTO.setBandWidth(fixDetail.getBandWidth());
        fillHistoryChangeOrder(changeWorkOrderDTO, dbDetail, updateDTO);
        resourceDetailManager.updateById(updateDTO);

        //如果是挂载了云主机/nat/slb，更新对应的eip和带宽
        resourceDetailManager.updateEipInfoByEipId(fixDetail.getDeviceId(), fixDetail.getEip(), fixDetail.getBandWidth());
    }


    public void coverNat(ResourceDetailDTO detail, StandardWorkOrderProductDTO productDTO) {

    }

    public void coverSlb(ChangeWorkOrderDTO changeWorkOrderDTO, ResourceDetailDTO dbDetail,
                         ResourceDetailDTO fixDetail, ChangeWorkOrderProductDTO productDTO) {
        log.info("slb资源详情变更，coverSlb fixDetail ：{}", JSONObject.toJSONString(fixDetail));
        String propertySnapshot = productDTO.getPropertySnapshot();
        ChangeSlbModel changeSlbModel = JSON.parseObject(propertySnapshot, ChangeSlbModel.class);
        boolean needFillHistoryChangeOrder = false;
        if(productDTO.getChangeType().contains(ChangeTypeEnum.INSTANCE_SPEC_CHANGE.getCode())){
            ResourceDetailDTO slbUpdateDTO = new ResourceDetailDTO();
            slbUpdateDTO.setId(dbDetail.getId());
            slbUpdateDTO.setSpec(changeSlbModel.getChangeFlavorName());
            needFillHistoryChangeOrder = true;
            resourceDetailManager.updateById(slbUpdateDTO);
        }
        //eip
        if (StringUtils.isNotBlank(fixDetail.getEipId())
                && productDTO.getChangeType().contains(ChangeTypeEnum.BANDWIDTH_EXPAND.getCode())) {
            ResourceDetailDTO eipDTO = resourceDetailManager.getByDeviceId(fixDetail.getEipId());
            ResourceDetailDTO eipUpdateDTO = new ResourceDetailDTO();
            eipUpdateDTO.setId(eipDTO.getId());
            eipUpdateDTO.setBandWidth(fixDetail.getBandWidth());
            resourceDetailManager.updateById(eipUpdateDTO);
            ResourceDetailDTO slbUpdateDTO = new ResourceDetailDTO();
            slbUpdateDTO.setId(dbDetail.getId());
            slbUpdateDTO.setBandWidth(fixDetail.getBandWidth());
            resourceDetailManager.updateById(slbUpdateDTO);
            needFillHistoryChangeOrder = true;
        }
        if (needFillHistoryChangeOrder) {
            ResourceDetailDTO slbUpdateDTO = new ResourceDetailDTO();
            slbUpdateDTO.setId(dbDetail.getId());
            fillHistoryChangeOrder(changeWorkOrderDTO, dbDetail, slbUpdateDTO);
            resourceDetailManager.updateById(slbUpdateDTO);
        }
    }

    public void coverObs(ResourceDetailDTO detail, StandardWorkOrderProductDTO productDTO) {

    }

    private static void fillHistoryChangeOrder(ChangeWorkOrderDTO changeWorkOrderDTO, ResourceDetailDTO dbDetail, ResourceDetailDTO updateDTO) {
        if (changeWorkOrderDTO != null) {
            updateDTO.setHisChangeOrderIds(StringUtils.isBlank(dbDetail.getHisChangeOrderIds()) ?
                    changeWorkOrderDTO.getId() :
                    String.format("%s,%s", dbDetail.getHisChangeOrderIds(), changeWorkOrderDTO.getId()));
            updateDTO.setHisChangeOrderCodes(StringUtils.isBlank(dbDetail.getHisChangeOrderCodes()) ?
                    changeWorkOrderDTO.getOrderCode() :
                    String.format("%s,%s", dbDetail.getHisChangeOrderCodes(), changeWorkOrderDTO.getOrderCode()));
        }
    }

}

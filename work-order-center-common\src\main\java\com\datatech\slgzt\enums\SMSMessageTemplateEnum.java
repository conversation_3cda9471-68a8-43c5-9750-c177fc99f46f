package com.datatech.slgzt.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * @description 短信模板
 * <AUTHOR>
 * @date 2025年 02月06日 14:36:31
 */
public enum SMSMessageTemplateEnum {

    OPEN("open", "尊敬的用户，您好！算力工作台资源开通工单：%s，到达节点为：%s。请您关注工单进展并及时处理相关事务。感谢您的配合！"),
    REJECT("reject", "尊敬的用户，您好，算力工作台资源开通工单：%s，已驳回，到达节点为：%s。请您关注工单进展并及时处理相关事务。感谢您的配合！"),
    NETWORK("network", "尊敬的用户，您好，算力工作台网络开通线下已处理完成。请您关注工单进展并及时处理相关事务。感谢您的配合！"),
    OPEN_SUCCESS("openSuccess", "尊敬的用户，您好！算力工作台资源开通工单：%s，已完成开通。"),

    //资源回收操作相关的标语-----------------------------------------------------------------------
    // 回收 同意
    RECYCLE_PASS("recycle_architecture_pass", "尊敬的用户，您好！算力工作台资源回收工单：%s，到达节点为：%s。请您关注工单进展并及时处理相关事务。感谢您的配合!"),

    // 回收 拒绝
    RECYCLE_REJECT("recycle_architecture_reject", "尊敬的用户，您好！算力工作台资源回收工单：%s，已到达节点：%s，已驳回。请您根据反馈意见进行调整后重新提交。感谢您的配合!"),

    //回收流程完成
    RECYCLE_COMPLE("recycle_comple","尊敬的用户，您好，算力工作台资源回收工单：%s，已完成回收。!"),

    // 工单变更消息模板
    CHANGE_PASS("change_pass", "尊敬的用户，您好！算力工作台资源变更工单：%s，到达节点为：%s。请您关注工单进展并及时处理相关事务。感谢您的配合!"),
    CHANGE_PASS_SHUTDOWN("change_pass_shutdown", "尊敬的用户，您好，算力工作台资源变更工单：%s，当前进度：二级云资源部领导审核完成进入到云主机关机环节，请在云主机关机之前联系总控台（13588724724）完成已交维云主机的告警屏蔽。请您关注工单进展并及时处理相关事务。感谢您的配合！"),
    CHANGE_REJECT("change_reject", "尊敬的用户，您好，算力工作台资源变更工单：%s，已驳回，到达节点为：%s。请您关注工单进展并及时处理相关事务。感谢您的配合！"),
    CHANGE_COMPLETE("change_complete", "尊敬的用户，您好！算力工作台资源变更工单：%s，已完成变更!"),

    // 资源变更环节
    CHANGE_RESOURCE_CHANGE_SUCCESS("resource_change_success","尊敬的用户，您好，算力工作台资源变更工单：%s，当前进度：资源变更完成。请您关注工单进展并及时处理相关事务。感谢您的配合！"),
    CHANGE_RESOURCE_CHANGE_FAIL("resource_change_fail","尊敬的用户，您好，算力工作台资源变更工单：%s，当前进度：资源变更失败。请您及时通知算力工作台运维人员人工介入。感谢您的配合！"),
    // 非标工单开通消息模板
    NON_STANDARD_PASS("non_standard_pass", "尊敬的用户，您好！算力工作台资源开通非标工单：%s，到达节点为：%s。请您关注工单进展并及时处理相关事务。感谢您的配合!"),
    NON_STANDARD_REJECT("non_standard_reject", "尊敬的用户，您好，算力工作台资源开通非标工单：%s，已驳回，到达节点为：%s。请您关注工单进展并及时处理相关事务。感谢您的配合！"),
    NON_STANDARD_COMPLETE("non_standard_complete", "尊敬的用户，您好！算力工作台资源开通非标工单：%s，已完成开通!"),

    DUE_ORDER("due_order", "尊敬的用户，您好！您的工单%s对应的资源即将到期，请及时发起资源延期申请，资源最后保留日期为%s，如到期未进行延期则系统会自动发起资源回收工单。"),
    EXPIRE_ORDER("expire_order", "尊敬的用户，您好！您的工单%s对应的资源已到期，请及时发起资源延期申请，资源最后保留日期为%s，如到期未进行延期则系统会自动发起资源回收工单。"),
    RECOVERY_ORDER("recovery_order", "尊敬的用户，您好！您的工单%s对应的资源已到期，已自动发起资源回收工单，请您关注工单进展并及时处理相关事务。感谢您的配合!"),
    /**
     * 租户确认提醒
     */
    TENANT_CONFIRM_REMINDER("tenant_confirm_reminder", "尊敬的用户，您好！算力工作台%s工单：%s（%s）已到达租户确认环节，请您尽快登录系统进行确认，以免影响%s进度。感谢您的配合！"),
    /**
     * 租户确认提醒-资源组
     */
    TENANT_CONFIRM_REMINDER_RESOURCE("tenant_confirm_reminder_resource", "尊敬的用户，您好！算力工作台%s工单：%s（%s）已到达租户确认环节，请联系租户尽快登录系统进行确认，以免影响%s进度。感谢您的配合！"),
    UNKNOWN("unknown", "-"),
    ;

    private String code;
    private String message;

    SMSMessageTemplateEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String code() {
        return code;
    }

    public String message() {
        return message;
    }

    /**
     * 通过code获取enum
     *
     * @param code
     * @return
     */
    public static SMSMessageTemplateEnum getByCode(String code) {
        if (!StringUtils.isEmpty(code)) {
            for (SMSMessageTemplateEnum value : values()) {
                if (value.code().equals(code)) {
                    return value;
                }
            }
        }
        return SMSMessageTemplateEnum.UNKNOWN;
    }
}

package com.datatech.slgzt.convert;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.datatech.slgzt.dao.model.SlbCertificateDO;
import com.datatech.slgzt.model.dto.SlbCertificateDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.HashMap;
import java.util.Map;

/**
 * SLB证书转换器
 */
@Mapper(componentModel = "spring")
public interface SlbCertificateManagerConvert {

    /**
     * DTO转DO
     */
    @Mapping(target = "slbListenerRel", source = "slbListenerRel", qualifiedByName = "modelToString")
    SlbCertificateDO dto2do(SlbCertificateDTO dto);
    
    /**
     * DO转DTO
     */
    @Mapping(target = "slbListenerRel", source = "slbListenerRel", qualifiedByName = "stringToModel")
    SlbCertificateDTO do2dto(SlbCertificateDO entity);


    /**
     * 将Model对象转换为JSON字符串
     */
    @Named("modelToString")
    default String modelToString(Map<String,String> model) {
        if (model == null) {
            return null;
        }
        return JSON.toJSONString(model);
    }

    /**
     * 将JSON字符串转换为ServerInfoModel列表
     */
    @Named("stringToModel")
    default Map<String,String> stringToModel(String json) {
        if (json == null) {
            return new HashMap<>();
        }
        return JSON.parseObject(json, new TypeReference<Map<String, String>>(){});
    }
}
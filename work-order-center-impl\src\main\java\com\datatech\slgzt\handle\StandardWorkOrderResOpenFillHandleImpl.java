package com.datatech.slgzt.handle;

import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.manager.VirtualIpManager;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.enums.domain.CatalogueDomain;
import com.datatech.slgzt.handle.standard.StandardWorkOrderResOpenFillHandle;
import com.datatech.slgzt.manager.*;
import com.datatech.slgzt.model.dto.FlavorDTO;
import com.datatech.slgzt.model.dto.ImagesDTO;
import com.datatech.slgzt.model.dto.StandardWorkOrderProductDTO;
import com.datatech.slgzt.model.nostander.*;
import com.datatech.slgzt.model.opm.StandardWorkOrderResOpenFillHandleOpm;
import com.datatech.slgzt.model.query.FlavorQuery;
import com.datatech.slgzt.model.query.ImagesQuery;
import com.datatech.slgzt.model.query.StandardWorkOrderProductQuery;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.utils.StreamUtils;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月13日 17:23:54
 */
@Service
public class StandardWorkOrderResOpenFillHandleImpl implements StandardWorkOrderResOpenFillHandle {

    @Resource
    private AzManager azManager;

    @Resource
    private FlavorModelManager flavorModelManager;

    @Resource
    private ImagesManager imagesManager;


    @Resource
    private RegionManager regionManager;

    @Resource
    private StandardWorkOrderProductManager standardWorkOrderProductManager;

    @Resource
    private VirtualIpManager virtualIpManager;

    private List<String> vpcChannelList = Lists.newArrayList(CatalogueDomain.H3C.getCode(), CatalogueDomain.VMWARE.getCode(), CatalogueDomain.HUAWEI.getCode(),CatalogueDomain.INSPUR.getCode());
    private List<String> netChannelList = Lists.newArrayList(CatalogueDomain.INNOVATION.getCode(), CatalogueDomain.PLATFORM_CHILD.getCode());



    @Override
    public void fillStandardWorkOrderResOpen(StandardWorkOrderResOpenFillHandleOpm opm) {
        StandardWorkOrderProductDTO dto = opm.getDto();
        ProductTypeEnum typeEnum = ProductTypeEnum.getByCode(dto.getProductType());
        switch (typeEnum){
            case ECS:
                ecsFill(opm);
                break;
            case GCS:
                gcsFill(opm);
                break;
            case EVS:
               evsFill(opm);
                break;
            case EIP:
                eipFill(opm);
                //productDTO.setPlaneNetworkModelList(req.getPlaneNetworkModelList());
                break;
            case OBS:
                obsFill(opm);
                break;
            case SLB:
                slbFill(opm);
                break;
            case NAT:
                natFill(opm);
                break;
            case CQ:
                cqFill(opm);
                break;
        }
    }

    //---------------------------------------------ecs-----------------------------------------------------------
    private void ecsFill(StandardWorkOrderResOpenFillHandleOpm opm){
        StandardWorkOrderProductDTO productDTO = opm.getDto();
        String propertySnapshot = productDTO.getPropertySnapshot();
        CloudEcsResourceModel cloudEcsResourceModel = JSON.parseObject(propertySnapshot, CloudEcsResourceModel.class);
        cloudEcsResourceModel.setAzId(opm.getAzId());
        cloudEcsResourceModel.setAzCode(opm.getAzCode());
        cloudEcsResourceModel.setAzName(opm.getAzName());
        //--------------------获取规格---------------------------------------
        FlavorQuery flavorQuery = new FlavorQuery().setRegionId(cloudEcsResourceModel.getRegionId())
                .setTemplateCode(opm.getTemplateCode()).setName(cloudEcsResourceModel.getFlavorName());
        //如果是创新池
        if(CatalogueDomain.INNOVATION.getCode().equals(opm.getOrderDTO().getDomainCode())){
            flavorQuery.setAzId(cloudEcsResourceModel.getAzId());
        }
        List<FlavorDTO> flavorDTOS = flavorModelManager.listFlavor(flavorQuery);
        Precondition.checkArgument(flavorDTOS,"规格不存在,请联系管理员");
        Precondition.checkArgument(flavorDTOS.size()==1,"规格不唯一,请联系管理员");
        cloudEcsResourceModel.setTemplateCode(opm.getTemplateCode());
        cloudEcsResourceModel.setFlavorCode(flavorDTOS.get(0).getId());
        cloudEcsResourceModel.setFlavorId(flavorDTOS.get(0).getId());
        cloudEcsResourceModel.setFlavorName(flavorDTOS.get(0).getName());
        //----------------------获取镜像---------------------------
        ImagesDTO imagesDTO = StreamUtils.findAny(imagesManager.list(new ImagesQuery()
                .setRegionId(cloudEcsResourceModel.getRegionId())
                //.setAzId(cloudEcsResourceModel.getAzId())
                .setOsType(cloudEcsResourceModel.getImageOs())
                .setVersion(cloudEcsResourceModel.getImageVersion())));
        Precondition.checkArgument(flavorDTOS.size()==1,"规格不唯一,请联系管理员");
        Precondition.checkArgument(imagesDTO,"镜像不存在,请联系管理员");
        cloudEcsResourceModel.setImageId(imagesDTO.getId());
        //----------------------设置网络---------------------------
        List<PlaneNetworkModel> planeNetworkModelList = opm.getPlaneNetworkModelList();
        planeNetworkModelHandle(planeNetworkModelList, opm.getOrderDTO().getDomainCode(), true);
        //如果domainCode 为 vmware/h3c/huawei 需要走VPC
        cloudEcsResourceModel.setPlaneNetworkModel(opm.getPlaneNetworkModelList());
        //查询到对应的子产品设置AZ和Region
        List<StandardWorkOrderProductDTO> productDTOList = standardWorkOrderProductManager.list(new StandardWorkOrderProductQuery()
                .setParentId(productDTO.getId()));
        for(StandardWorkOrderProductDTO standardWorkOrderProductDTO : productDTOList) {
            String productType = standardWorkOrderProductDTO.getProductType();
            String propertyJson = standardWorkOrderProductDTO.getPropertySnapshot();
            if(productType.equals(ProductTypeEnum.EVS.getCode())){
                MountDataDiskModel mountDataDiskModel = JSON.parseObject(propertyJson, MountDataDiskModel.class);
                mountDataDiskModel.setAzId(opm.getAzId());
                mountDataDiskModel.setAzCode(opm.getAzCode());
                mountDataDiskModel.setAzName(opm.getAzName());
                propertyJson=JSON.toJSONString(mountDataDiskModel);
            }
            if (productType.equals(ProductTypeEnum.EIP.getCode())) {
                EipModel eipModel = JSON.parseObject(propertyJson, EipModel.class);
                eipModel.setAzId(opm.getAzId());
                eipModel.setAzCode(opm.getAzCode());
                eipModel.setAzName(opm.getAzName());
                propertyJson=JSON.toJSONString(eipModel);
            }
            standardWorkOrderProductDTO.setPropertySnapshot(propertyJson);
            standardWorkOrderProductManager.update(standardWorkOrderProductDTO);
        }
        //存储到数据库
        productDTO.setPropertySnapshot(JSON.toJSONString(cloudEcsResourceModel));
        standardWorkOrderProductManager.update(productDTO);

    }
    //---------------------------------------------gcs-----------------------------------------------------------
    private void gcsFill(StandardWorkOrderResOpenFillHandleOpm opm){
        StandardWorkOrderProductDTO productDTO = opm.getDto();
        String propertySnapshot = productDTO.getPropertySnapshot();
        CpuEcsResourceModel ecsResourceModel = JSON.parseObject(propertySnapshot, CpuEcsResourceModel.class);
        ecsResourceModel.setAzId(opm.getAzId());
        ecsResourceModel.setAzCode(opm.getAzCode());
        ecsResourceModel.setAzName(opm.getAzName());
        //获取规格 规格通过
        //--------------------获取规格---------------------------------------
        //--------------------获取规格---------------------------------------
        FlavorQuery flavorQuery = new FlavorQuery().setRegionId(ecsResourceModel.getRegionId())
                .setTemplateCode(opm.getTemplateCode()).setName(ecsResourceModel.getFlavorName());
        //如果是创新池
        if(CatalogueDomain.INNOVATION.getCode().equals(opm.getOrderDTO().getDomainCode())){
            flavorQuery.setAzId(ecsResourceModel.getAzId());
        }
        List<FlavorDTO> flavorDTOS = flavorModelManager.listFlavor(flavorQuery);
        Precondition.checkArgument(flavorDTOS,"规格不存在,请联系管理员");
        Precondition.checkArgument(flavorDTOS.size()==1,"规格不唯一,请联系管理员");
        ecsResourceModel.setTemplateCode(opm.getTemplateCode());
        ecsResourceModel.setFlavorCode(flavorDTOS.get(0).getId());
        ecsResourceModel.setFlavorName(flavorDTOS.get(0).getName());
        //获取镜像
        ImagesDTO imagesDTO = StreamUtils.findAny(imagesManager.list(new ImagesQuery()
                .setRegionId(ecsResourceModel.getRegionId())
                .setOsType(ecsResourceModel.getImageOs())
                .setVersion(ecsResourceModel.getImageVersion())));
        Precondition.checkArgument(flavorDTOS.size()==1,"规格不唯一,请联系管理员");
        Precondition.checkArgument(imagesDTO,"镜像不存在,请联系管理员");
        ecsResourceModel.setImageId(imagesDTO.getId());
        //----------------------设置网络---------------------------
        List<PlaneNetworkModel> planeNetworkModelList = opm.getPlaneNetworkModelList();
        planeNetworkModelHandle(planeNetworkModelList, opm.getOrderDTO().getDomainCode(), true);
        ecsResourceModel.setPlaneNetworkModel(opm.getPlaneNetworkModelList());
        //查询到对应的子产品设置AZ和Region
        List<StandardWorkOrderProductDTO> productDTOList = standardWorkOrderProductManager.list(new StandardWorkOrderProductQuery()
                .setParentId(productDTO.getId()));
        for(StandardWorkOrderProductDTO standardWorkOrderProductDTO : productDTOList) {
            String productType = standardWorkOrderProductDTO.getProductType();
            String propertyJson = standardWorkOrderProductDTO.getPropertySnapshot();
            if(productType.equals(ProductTypeEnum.EVS.getCode())){
                MountDataDiskModel mountDataDiskModel = JSON.parseObject(propertyJson, MountDataDiskModel.class);
                mountDataDiskModel.setAzId(opm.getAzId());
                mountDataDiskModel.setAzCode(opm.getAzCode());
                mountDataDiskModel.setAzName(opm.getAzName());
                propertyJson=JSON.toJSONString(mountDataDiskModel);
            }
            if (productType.equals(ProductTypeEnum.EIP.getCode())) {
                EipModel eipModel = JSON.parseObject(propertyJson, EipModel.class);
                eipModel.setAzId(opm.getAzId());
                eipModel.setAzCode(opm.getAzCode());
                eipModel.setAzName(opm.getAzName());
                propertyJson=JSON.toJSONString(eipModel);
            }
            standardWorkOrderProductDTO.setPropertySnapshot(propertyJson);
            standardWorkOrderProductManager.update(standardWorkOrderProductDTO);
        }
        //存储到数据库
        productDTO.setPropertySnapshot(JSON.toJSONString(ecsResourceModel));
        standardWorkOrderProductManager.update(productDTO);

    }
    //---------------------------------------------evs-----------------------------------------------------------
    private void evsFill(StandardWorkOrderResOpenFillHandleOpm opm){
        StandardWorkOrderProductDTO productDTO = opm.getDto();
        String propertySnapshot = productDTO.getPropertySnapshot();
        MountDataDiskModel ecsResourceModel = JSON.parseObject(propertySnapshot, MountDataDiskModel.class);
        ecsResourceModel.setAzId(opm.getAzId());
        ecsResourceModel.setAzCode(opm.getAzCode());
        ecsResourceModel.setAzName(opm.getAzName());
        //存储到数据库
        productDTO.setPropertySnapshot(JSON.toJSONString(ecsResourceModel));
        standardWorkOrderProductManager.update(productDTO);
    }

    //---------------------------------------------eip-----------------------------------------------------------
    private void eipFill(StandardWorkOrderResOpenFillHandleOpm opm) {
        StandardWorkOrderProductDTO productDTO = opm.getDto();
        String propertySnapshot = productDTO.getPropertySnapshot();
        EipModel eipModel = JSON.parseObject(propertySnapshot, EipModel.class);
        eipModel.setAzId(opm.getAzId());
        eipModel.setAzCode(opm.getAzCode());
        eipModel.setAzName(opm.getAzName());
        //存储到数据库
        productDTO.setPropertySnapshot(JSON.toJSONString(eipModel));
        standardWorkOrderProductManager.update(productDTO);
    }


    //---------------------------------------------obs-----------------------------------------------------------
    private void obsFill(StandardWorkOrderResOpenFillHandleOpm opm){
        StandardWorkOrderProductDTO productDTO = opm.getDto();
        String propertySnapshot = productDTO.getPropertySnapshot();
        ObsModel obsModel = JSON.parseObject(propertySnapshot, ObsModel.class);
        obsModel.setAzId(opm.getAzId());
        obsModel.setAzCode(opm.getAzCode());
        obsModel.setAzName(opm.getAzName());
        //存储到数据库
        productDTO.setPropertySnapshot(JSON.toJSONString(obsModel));
        standardWorkOrderProductManager.update(productDTO);
    }

    //---------------------------------------------slb-----------------------------------------------------------
    private void slbFill(StandardWorkOrderResOpenFillHandleOpm opm){
        StandardWorkOrderProductDTO productDTO = opm.getDto();
        String propertySnapshot = productDTO.getPropertySnapshot();
        SlbModel slbModel = JSON.parseObject(propertySnapshot, SlbModel.class);
        slbModel.setAzId(opm.getAzId());
        slbModel.setAzCode(opm.getAzCode());
        slbModel.setAzName(opm.getAzName());
        //--------------------获取规格---------------------------------------
        List<FlavorDTO> flavorDTOS = flavorModelManager.listFlavor(new FlavorQuery()
                .setRegionId(slbModel.getRegionId())
                .setName(slbModel.getFlavorName()));
        Precondition.checkArgument(flavorDTOS,"规格不存在,请联系管理员");
        Precondition.checkArgument(flavorDTOS.size()==1,"规格不唯一,请联系管理员");
        slbModel.setFlavorCode(flavorDTOS.get(0).getId());
        slbModel.setFlavorName(flavorDTOS.get(0).getName());
        slbModel.setAzCode(opm.getAzCode());
        slbModel.setAzName(opm.getAzName());
        slbModel.setAzId(opm.getAzId());
        //----------------------设置网络---------------------------
        List<PlaneNetworkModel> planeNetworkModelList = opm.getPlaneNetworkModelList();
        planeNetworkModelHandle(planeNetworkModelList, opm.getOrderDTO().getDomainCode(), false);
        slbModel.setPlaneNetworkModel(opm.getPlaneNetworkModelList().get(0));
        //查询到对应的子产品设置AZ和Region
        List<StandardWorkOrderProductDTO> productDTOList = standardWorkOrderProductManager.list(new StandardWorkOrderProductQuery()
                .setParentId(productDTO.getId()));
        for(StandardWorkOrderProductDTO standardWorkOrderProductDTO : productDTOList) {
            String productType = standardWorkOrderProductDTO.getProductType();
            String propertyJson = standardWorkOrderProductDTO.getPropertySnapshot();
            if (productType.equals(ProductTypeEnum.EIP.getCode())) {
                EipModel eipModel = JSON.parseObject(propertyJson, EipModel.class);
                eipModel.setAzId(opm.getAzId());
                eipModel.setAzCode(opm.getAzCode());
                eipModel.setAzName(opm.getAzName());
                propertyJson=JSON.toJSONString(eipModel);
            }
            standardWorkOrderProductDTO.setPropertySnapshot(propertyJson);
            standardWorkOrderProductManager.update(standardWorkOrderProductDTO);
        }
        //存储到数据库
        productDTO.setPropertySnapshot(JSON.toJSONString(slbModel));
        standardWorkOrderProductManager.update(productDTO);
    }

    private void natFill(StandardWorkOrderResOpenFillHandleOpm opm){
        StandardWorkOrderProductDTO productDTO = opm.getDto();
        String propertySnapshot = productDTO.getPropertySnapshot();
        NatGatwayModel natGatwayModel = JSON.parseObject(propertySnapshot, NatGatwayModel.class);
        natGatwayModel.setAzId(opm.getAzId());
        natGatwayModel.setAzCode(opm.getAzCode());
        natGatwayModel.setAzName(opm.getAzName());
        //--------------------获取规格---------------------------------------
        //--------------------获取规格---------------------------------------
        List<FlavorDTO> flavorDTOS = flavorModelManager.listFlavor(new FlavorQuery()
                .setRegionId(natGatwayModel.getRegionId())
                .setName(natGatwayModel.getFlavorName()));
        Precondition.checkArgument(flavorDTOS,"规格不存在,请联系管理员");
        Precondition.checkArgument(flavorDTOS.size()==1,"规格不唯一,请联系管理员");
        natGatwayModel.setFlavorCode(flavorDTOS.get(0).getId());
        natGatwayModel.setFlavorName(flavorDTOS.get(0).getName());

        //----------------------设置网络---------------------------
        List<PlaneNetworkModel> planeNetworkModelList = opm.getPlaneNetworkModelList();
        planeNetworkModelHandle(planeNetworkModelList, opm.getOrderDTO().getDomainCode(), false);
        natGatwayModel.setPlaneNetworkModel(opm.getPlaneNetworkModelList().get(0));
        //查询到对应的子产品设置AZ和Region
        List<StandardWorkOrderProductDTO> productDTOList = standardWorkOrderProductManager.list(new StandardWorkOrderProductQuery()
                .setParentId(productDTO.getId()));
        for(StandardWorkOrderProductDTO standardWorkOrderProductDTO : productDTOList) {
            String productType = standardWorkOrderProductDTO.getProductType();
            String propertyJson = standardWorkOrderProductDTO.getPropertySnapshot();
            if (productType.equals(ProductTypeEnum.EIP.getCode())) {
                EipModel eipModel = JSON.parseObject(propertyJson, EipModel.class);
                eipModel.setAzId(opm.getAzId());
                eipModel.setAzCode(opm.getAzCode());
                eipModel.setAzName(opm.getAzName());
                propertyJson=JSON.toJSONString(eipModel);
            }
            standardWorkOrderProductDTO.setPropertySnapshot(propertyJson);
            standardWorkOrderProductManager.update(standardWorkOrderProductDTO);
        }
        //存储到数据库
        productDTO.setPropertySnapshot(JSON.toJSONString(natGatwayModel));
        standardWorkOrderProductManager.update(productDTO);

    }

    /**
     * 网络流向处理方法
     * 主要通过domainCode来判断是否需要走VPC
     * 设置好类型
     */
    public void planeNetworkModelHandle(List<PlaneNetworkModel> planeNetworkModelList, String domainCode, boolean checkIpAddress) {
//        HashMap<String,List<String>> subnetId2IpListMap = new HashMap<>();
        for (PlaneNetworkModel planeNetworkModel : planeNetworkModelList) {
            if (vpcChannelList.contains(domainCode)) {
                planeNetworkModel.setType("vpc");
            } else if (netChannelList.contains(domainCode)) {
                if (checkIpAddress) {
                    for (PlaneNetworkModel.Subnet subnet : planeNetworkModel.getSubnets()) {
                        if (subnet.getIpAddress() != null) {
                            Precondition.checkArgument(subnet.getIpAddress(), "子网ID不能为空");
                            String errMsg = virtualIpManager.checkIpAvailable(subnet.getSubnetId(), subnet.getIpAddress());
                            Precondition.checkArgument(errMsg == null, errMsg);
    //                        List<String> ipList = subnetId2IpListMap.get(subnet.getSubnetId());
    //                        if (ipList == null) {
    //                            ipList = new ArrayList<>();
    //                            subnetId2IpListMap.put(subnet.getSubnetId(), ipList);
    //                        }
    //                        ipList.add(subnet.getIpAddress());
                        }
                    }
                }
                planeNetworkModel.setType("net");
            }
        }
//        if (checkIpAddress) {
//            // 校验ip
//            for (Map.Entry<String, List<String>> entry : subnetId2IpListMap.entrySet()) {
//                String errMsg = virtualIpManager.checkIpAvailableBatch(entry.getKey(), entry.getValue());
//                Precondition.checkArgument(errMsg == null, errMsg);
//            }
//        }
    }

    //---------------------------------------------container-----------------------------------------------------------
    private void cqFill(StandardWorkOrderResOpenFillHandleOpm opm){
        StandardWorkOrderProductDTO productDTO = opm.getDto();
        String propertySnapshot = productDTO.getPropertySnapshot();
        CQModel CQModel = JSON.parseObject(propertySnapshot, CQModel.class);

        // 设置AZ信息
        CQModel.setAzId(opm.getAzId());
        CQModel.setAzCode(opm.getAzCode());
        CQModel.setAzName(opm.getAzName());

        // 存储到数据库
        productDTO.setPropertySnapshot(JSON.toJSONString(CQModel));
        standardWorkOrderProductManager.update(productDTO);
    }
}

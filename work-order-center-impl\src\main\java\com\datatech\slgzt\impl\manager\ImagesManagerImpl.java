package com.datatech.slgzt.impl.manager;

import cn.hutool.core.collection.CollectionUtil;
import com.datatech.slgzt.convert.ImagesManagerConvert;
import com.datatech.slgzt.dao.ImagesDAO;
import com.datatech.slgzt.dao.model.ImagesDO;
import com.datatech.slgzt.dao.model.ResourceDetailDO;
import com.datatech.slgzt.manager.ImagesManager;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.dto.ImagesDTO;
import com.datatech.slgzt.model.dto.ResourceDetailDTO;
import com.datatech.slgzt.model.query.ImagesQuery;
import com.datatech.slgzt.utils.ObjNullUtils;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.utils.StreamUtils;
import com.datatech.slgzt.warpper.PageWarppers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月10日 15:02:03
 */
@Service
public class ImagesManagerImpl implements ImagesManager {

    @Resource
    private ImagesDAO dao;

    @Resource
    private ImagesManagerConvert convert;

    @Override
    public List<ImagesDTO> list(ImagesQuery query) {
        if(ObjNullUtils.isNull(query.getShares())){
            query.setShares(1);
        }
        return StreamUtils.mapArray(dao.list(query), convert::do2dto);
    }

    @Override
    public PageResult<ImagesDTO> queryImage(ImagesQuery query) {
        if(ObjNullUtils.isNull(query.getShares())){
            query.setShares(1);
        }
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<ImagesDO> list = dao.list(query);
        return PageWarppers.box(new PageInfo<>(list), convert::do2dto);
    }
}

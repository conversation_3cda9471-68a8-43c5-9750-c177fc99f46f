package com.datatech.slgzt.utils;

import java.math.BigInteger;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * IP地址工具类
 * 提供IPv4和IPv6地址转换、解析和生成功能
 *
 * <AUTHOR>
 */
public class IpUtils {

    /**
     * IPv4地址正则表达式
     */
    private static final String IPV4_REGEX =
            "^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\." +
            "(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\." +
            "(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\." +
            "(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$";

    /**
     * IPv4 CIDR格式正则表达式
     */
    private static final String IPV4_CIDR_REGEX =
            "^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\." +
            "(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\." +
            "(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\." +
            "(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)/(3[0-2]|[12][0-9]|[0-9])$";

    /**
     * IPv6地址正则表达式
     */
    private static final String IPV6_REGEX =
            "^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|" +
            "^::([0-9a-fA-F]{1,4}:){0,6}[0-9a-fA-F]{1,4}$|" +
            "^([0-9a-fA-F]{1,4}:){1,7}:$|" +
            "^([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}$|" +
            "^([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}$|" +
            "^([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}$|" +
            "^([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}$|" +
            "^([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}$|" +
            "^[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})$|" +
            "^:((:[0-9a-fA-F]{1,4}){1,7}|:)$";

    /**
     * IPv6 CIDR格式正则表达式
     */
    private static final String IPV6_CIDR_REGEX =
            "^([0-9a-fA-F:]+)/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$";

    /**
     * 兼容的CIDR格式正则表达式（IPv4）
     */
    private static final String CIDR_REGEX = IPV4_CIDR_REGEX;

    // ==================== IP地址类型检测方法 ====================

    /**
     * 检测IP地址是否为IPv4格式
     *
     * @param ip IP地址字符串
     * @return 如果是IPv4格式返回true，否则返回false
     */
    public static boolean isIPv4(String ip) {
        return ip != null && Pattern.matches(IPV4_REGEX, ip);
    }

    /**
     * 检测IP地址是否为IPv6格式
     *
     * @param ip IP地址字符串
     * @return 如果是IPv6格式返回true，否则返回false
     */
    public static boolean isIPv6(String ip) {
        if (ip == null) {
            return false;
        }
        try {
            InetAddress addr = InetAddress.getByName(ip);
            return addr instanceof java.net.Inet6Address;
        } catch (UnknownHostException e) {
            return false;
        }
    }

    /**
     * 检测CIDR是否为IPv4格式
     *
     * @param cidr CIDR字符串
     * @return 如果是IPv4 CIDR格式返回true，否则返回false
     */
    public static boolean isIPv4Cidr(String cidr) {
        return cidr != null && Pattern.matches(IPV4_CIDR_REGEX, cidr);
    }

    /**
     * 检测CIDR是否为IPv6格式
     *
     * @param cidr CIDR字符串
     * @return 如果是IPv6 CIDR格式返回true，否则返回false
     */
    public static boolean isIPv6Cidr(String cidr) {
        if (cidr == null || !cidr.contains("/")) {
            return false;
        }

        String[] parts = cidr.split("/");
        if (parts.length != 2) {
            return false;
        }

        // 检查IP部分是否为IPv6
        if (!isIPv6(parts[0])) {
            return false;
        }

        // 检查前缀长度是否有效（0-128）
        try {
            int prefixLength = Integer.parseInt(parts[1]);
            return prefixLength >= 0 && prefixLength <= 128;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    // ==================== IPv4相关方法 ====================

    /**
     * 将IPv4地址转换为整数
     *
     * @param ipv4 IPv4地址字符串，如"***********"
     * @return 转换后的整数值
     * @throws IllegalArgumentException 如果输入的IP地址格式不正确
     */
    public static long ipv4ToLong(String ipv4) {
        if (ipv4 == null || !Pattern.matches(IPV4_REGEX, ipv4)) {
            throw new IllegalArgumentException("无效的IPv4地址格式: " + ipv4);
        }

        long result = 0;
        String[] segments = ipv4.split("\\.");
        for (int i = 0; i < segments.length; i++) {
            result = (result << 8) | Integer.parseInt(segments[i]);
        }
        return result;
    }

    /**
     * 将整数转换为IPv4地址
     *
     * @param ip 表示IP的整数值
     * @return IPv4地址字符串，如"***********"
     */
    public static String longToIpv4(long ip) {
        StringBuilder sb = new StringBuilder();
        sb.append((ip >> 24) & 0xFF).append('.');
        sb.append((ip >> 16) & 0xFF).append('.');
        sb.append((ip >> 8) & 0xFF).append('.');
        sb.append(ip & 0xFF);
        return sb.toString();
    }

    /**
     * 解析CIDR格式的IP地址
     *
     * @param cidr CIDR格式的IP地址，如"***********/24"
     * @return 包含IP地址和掩码长度的数组，如["***********", "24"]
     * @throws IllegalArgumentException 如果输入的CIDR格式不正确
     */
    public static String[] parseCidr(String cidr) {
        if (cidr == null || !Pattern.matches(CIDR_REGEX, cidr)) {
            throw new IllegalArgumentException("无效的CIDR格式: " + cidr);
        }

        return cidr.split("/");
    }

    /**
     * 计算CIDR的网络地址
     *
     * @param cidr CIDR格式的IP地址，如"***********/24"
     * @return 网络地址的long值
     */
    public static long getNetworkLong(String cidr) {
        String[] parts = parseCidr(cidr);
        String ipPart = parts[0];
        int prefixLength = Integer.parseInt(parts[1]);

        long ipLong = ipv4ToLong(ipPart);
        long mask = prefixLength == 0 ? 0 : ~((1L << (32 - prefixLength)) - 1);

        return ipLong & mask;
    }

    /**
     * 计算CIDR的网络地址
     *
     * @param cidr CIDR格式的IP地址，如"***********/24"
     * @return 网络地址，如"***********"
     */
    public static String getNetworkAddress(String cidr) {
        long networkLong = getNetworkLong(cidr);
        return longToIpv4(networkLong);
    }

    /**
     * 计算CIDR的广播地址的long值
     *
     * @param cidr CIDR格式的IP地址，如"***********/24"
     * @return 广播地址的long值
     */
    public static long getBroadcastLong(String cidr) {
        String[] parts = parseCidr(cidr);
        String ipPart = parts[0];
        int prefixLength = Integer.parseInt(parts[1]);

        long ipLong = ipv4ToLong(ipPart);
        long mask = prefixLength == 0 ? 0 : ~((1L << (32 - prefixLength)) - 1);
        long broadcastLong = ipLong | ~mask;

        return broadcastLong;
    }

    /**
     * 计算CIDR的广播地址
     *
     * @param cidr CIDR格式的IP地址，如"***********/24"
     * @return 广播地址，如"*************"
     */
    public static String getBroadcastAddress(String cidr) {
        long broadcastLong = getBroadcastLong(cidr);
        return longToIpv4(broadcastLong);
    }

    /**
     * 生成指定数量的可用IP地址（自动检测IPv4/IPv6）
     *
     * @param cidr CIDR格式的IP地址，如"***********/24"或"2001:db8::/64"
     * @param usedIps 已使用的IP列表，可为null
     * @param filterIps 需要过滤的IP列表，可为null
     * @param count 需要生成的IP数量
     * @return 可用的IP地址列表
     */
    public static List<String> generateAvailableIps(String cidr, List<String> usedIps, List<String> filterIps, int count) {
        // 自动检测IP版本并调用相应的方法
        if (isIPv4Cidr(cidr)) {
            return generateAvailableIpv4s(cidr, usedIps, filterIps, count);
        } else if (isIPv6Cidr(cidr)) {
            return generateAvailableIpv6s(cidr, usedIps, filterIps, count);
        } else {
            throw new IllegalArgumentException("无效的CIDR格式: " + cidr);
        }
    }

    /**
     * 生成指定数量的可用IPv4地址
     *
     * @param cidr IPv4 CIDR格式的IP地址，如"***********/24"
     * @param usedIps 已使用的IP列表，可为null
     * @param filterIps 需要过滤的IP列表，可为null
     * @param count 需要生成的IP数量
     * @return 可用的IPv4地址列表
     */
    public static List<String> generateAvailableIpv4s(String cidr, List<String> usedIps, List<String> filterIps, int count) {
        if (cidr == null || !Pattern.matches(CIDR_REGEX, cidr)) {
            throw new IllegalArgumentException("无效的IPv4 CIDR格式: " + cidr);
        }

        if (count <= 0) {
            return new ArrayList<>();
        }

        // 计算网络地址和广播地址
        String networkAddress = getNetworkAddress(cidr);
        String broadcastAddress = getBroadcastAddress(cidr);

        // 计算可用IP数量
        long networkLong = ipv4ToLong(networkAddress);
        long broadcastLong = ipv4ToLong(broadcastAddress);
        long availableIpCount = broadcastLong - networkLong - 1;

        if (availableIpCount <= 0) {
            return new ArrayList<>();
        }

        // 转换已使用和需要过滤的IP为集合
        List<String> unavailableIps = new ArrayList<>();
        if (usedIps != null) {
            unavailableIps.addAll(usedIps);
        }
        if (filterIps != null) {
            unavailableIps.addAll(filterIps);
        }

        // 添加网络地址和广播地址到不可用列表
        unavailableIps.add(networkAddress);
        unavailableIps.add(broadcastAddress);

        // 生成可用IP列表
        List<String> availableIps = new ArrayList<>();
        long currentIp = networkLong + 1;

        while (availableIps.size() < count && currentIp < broadcastLong) {
            String ipAddress = longToIpv4(currentIp);
            if (!unavailableIps.contains(ipAddress)) {
                availableIps.add(ipAddress);
            }
            currentIp++;
        }

        return availableIps;
    }


    /**
     * 判断指定的IP地址是否在CIDR网段内
     *
     * @param ip 需要检查的IP地址，如"***********00"
     * @param cidr CIDR格式的网段，如"***********/24"
     * @return 如果IP在网段内返回true，否则返回false
     * @throws IllegalArgumentException 如果IP格式或CIDR格式不正确
     */
    public static boolean isIpInCidr(String ip, String cidr) {
        if (ip == null || !Pattern.matches(IPV4_REGEX, ip)) {
            throw new IllegalArgumentException("无效的IPv4地址格式: " + ip);
        }

        if (cidr == null || !Pattern.matches(CIDR_REGEX, cidr)) {
            throw new IllegalArgumentException("无效的CIDR格式: " + cidr);
        }

        // 将IP地址转换为长整型
        long ipLong = ipv4ToLong(ip);

        // 获取CIDR的网络地址和广播地址
        long networkLong = getNetworkLong(cidr);
        long broadcastLong = getBroadcastLong(cidr);

        // 判断IP是否在网段范围内（包括网络地址和广播地址）
        return ipLong >= networkLong && ipLong <= broadcastLong;
    }

    // ==================== IPv6相关方法 ====================

    /**
     * 将IPv6地址转换为BigInteger
     *
     * @param ipv6 IPv6地址字符串，如"2001:db8::1"
     * @return 转换后的BigInteger值
     * @throws IllegalArgumentException 如果输入的IP地址格式不正确
     */
    public static BigInteger ipv6ToBigInteger(String ipv6) {
        if (!isIPv6(ipv6)) {
            throw new IllegalArgumentException("无效的IPv6地址格式: " + ipv6);
        }

        try {
            InetAddress addr = InetAddress.getByName(ipv6);
            byte[] bytes = addr.getAddress();
            return new BigInteger(1, bytes);
        } catch (UnknownHostException e) {
            throw new IllegalArgumentException("无效的IPv6地址格式: " + ipv6, e);
        }
    }

    /**
     * 将BigInteger转换为IPv6地址
     *
     * @param ip 表示IPv6的BigInteger值
     * @return IPv6地址字符串，如"2001:db8::1"
     */
    public static String bigIntegerToIpv6(BigInteger ip) {
        if (ip == null || ip.compareTo(BigInteger.ZERO) < 0) {
            throw new IllegalArgumentException("无效的IPv6数值: " + ip);
        }

        // 确保是128位
        byte[] bytes = ip.toByteArray();
        if (bytes.length > 16) {
            throw new IllegalArgumentException("IPv6数值超出范围: " + ip);
        }

        // 补齐到16字节
        byte[] ipv6Bytes = new byte[16];
        System.arraycopy(bytes, 0, ipv6Bytes, 16 - bytes.length, bytes.length);

        try {
            InetAddress addr = InetAddress.getByAddress(ipv6Bytes);
            String fullAddress = addr.getHostAddress();
            // 压缩IPv6地址格式
            return compressIpv6Address(fullAddress);
        } catch (UnknownHostException e) {
            throw new IllegalArgumentException("无法转换为IPv6地址: " + ip, e);
        }
    }

    /**
     * 压缩IPv6地址格式，去除多余的0
     *
     * @param ipv6 完整格式的IPv6地址，如"2001:0db8:0000:0000:0000:0000:0000:0001"
     * @return 压缩格式的IPv6地址，如"2001:db8::1"
     */
    public static String compressIpv6Address(String ipv6) {
        if (ipv6 == null || ipv6.isEmpty()) {
            return ipv6;
        }

        // 如果已经是压缩格式，直接返回
        if (ipv6.contains("::")) {
            return ipv6;
        }

        // 分割成8个部分
        String[] parts = ipv6.split(":");
        if (parts.length != 8) {
            return ipv6; // 格式不正确，返回原始值
        }

        // 去除每个部分前面的0
        for (int i = 0; i < parts.length; i++) {
            parts[i] = parts[i].replaceFirst("^0+", "");
            if (parts[i].isEmpty()) {
                parts[i] = "0";
            }
        }

        // 查找最长的连续0序列
        int maxZeroStart = -1;
        int maxZeroLength = 0;
        int currentZeroStart = -1;
        int currentZeroLength = 0;

        for (int i = 0; i < parts.length; i++) {
            if ("0".equals(parts[i])) {
                if (currentZeroStart == -1) {
                    currentZeroStart = i;
                    currentZeroLength = 1;
                } else {
                    currentZeroLength++;
                }
            } else {
                if (currentZeroLength > maxZeroLength) {
                    maxZeroStart = currentZeroStart;
                    maxZeroLength = currentZeroLength;
                }
                currentZeroStart = -1;
                currentZeroLength = 0;
            }
        }

        // 检查最后一段
        if (currentZeroLength > maxZeroLength) {
            maxZeroStart = currentZeroStart;
            maxZeroLength = currentZeroLength;
        }

        // 如果最长的0序列长度大于1，则进行压缩
        if (maxZeroLength > 1) {
            StringBuilder result = new StringBuilder();

            // 添加压缩前的部分
            for (int i = 0; i < maxZeroStart; i++) {
                if (i > 0) result.append(":");
                result.append(parts[i]);
            }

            // 添加压缩标记
            if (maxZeroStart == 0) {
                result.append("::");
            } else {
                result.append("::");
            }

            // 添加压缩后的部分
            boolean first = true;
            for (int i = maxZeroStart + maxZeroLength; i < parts.length; i++) {
                if (!first) result.append(":");
                result.append(parts[i]);
                first = false;
            }

            return result.toString();
        } else {
            // 没有连续的0，只是去除前导0
            return String.join(":", parts);
        }
    }

    /**
     * 解析IPv6 CIDR格式的IP地址
     *
     * @param cidr IPv6 CIDR格式的IP地址，如"2001:db8::/32"
     * @return 包含IP地址和掩码长度的数组，如["2001:db8::", "32"]
     * @throws IllegalArgumentException 如果输入的CIDR格式不正确
     */
    public static String[] parseIpv6Cidr(String cidr) {
        if (!isIPv6Cidr(cidr)) {
            throw new IllegalArgumentException("无效的IPv6 CIDR格式: " + cidr);
        }

        return cidr.split("/");
    }

    /**
     * 计算IPv6 CIDR的网络地址
     *
     * @param cidr IPv6 CIDR格式的IP地址，如"2001:db8::/32"
     * @return 网络地址的BigInteger值
     */
    public static BigInteger getIpv6NetworkBigInteger(String cidr) {
        String[] parts = parseIpv6Cidr(cidr);
        String ipPart = parts[0];
        int prefixLength = Integer.parseInt(parts[1]);

        BigInteger ipBigInt = ipv6ToBigInteger(ipPart);

        if (prefixLength == 0) {
            return BigInteger.ZERO;
        }

        if (prefixLength >= 128) {
            return ipBigInt;
        }

        // 创建网络掩码
        BigInteger hostMask = BigInteger.valueOf(2).pow(128 - prefixLength).subtract(BigInteger.ONE);
        BigInteger networkMask = BigInteger.valueOf(2).pow(128).subtract(BigInteger.ONE).xor(hostMask);

        return ipBigInt.and(networkMask);
    }

    /**
     * 计算IPv6 CIDR的网络地址
     *
     * @param cidr IPv6 CIDR格式的IP地址，如"2001:db8::/32"
     * @return 网络地址，如"2001:db8::"
     */
    public static String getIpv6NetworkAddress(String cidr) {
        BigInteger networkBigInt = getIpv6NetworkBigInteger(cidr);
        return bigIntegerToIpv6(networkBigInt);
    }

    /**
     * 计算IPv6 CIDR的最大地址（类似IPv4的广播地址）
     *
     * @param cidr IPv6 CIDR格式的IP地址，如"2001:db8::/32"
     * @return 最大地址的BigInteger值
     */
    public static BigInteger getIpv6MaxBigInteger(String cidr) {
        String[] parts = parseIpv6Cidr(cidr);
        String ipPart = parts[0];
        int prefixLength = Integer.parseInt(parts[1]);

        BigInteger ipBigInt = ipv6ToBigInteger(ipPart);

        if (prefixLength == 0) {
            // 如果前缀长度为0，返回IPv6的最大值
            return BigInteger.valueOf(2).pow(128).subtract(BigInteger.ONE);
        }

        if (prefixLength >= 128) {
            // 如果前缀长度为128，只有一个地址
            return ipBigInt;
        }

        // 创建网络掩码
        BigInteger mask = BigInteger.valueOf(2).pow(128 - prefixLength).subtract(BigInteger.ONE);

        // 计算网络地址
        BigInteger networkMask = BigInteger.valueOf(2).pow(128).subtract(BigInteger.ONE).xor(mask);
        BigInteger networkAddress = ipBigInt.and(networkMask);

        // 计算最大地址
        return networkAddress.or(mask);
    }

    /**
     * 计算IPv6 CIDR的最大地址（类似IPv4的广播地址）
     *
     * @param cidr IPv6 CIDR格式的IP地址，如"2001:db8::/32"
     * @return 最大地址，如"2001:db8:ffff:ffff:ffff:ffff:ffff:ffff"
     */
    public static String getIpv6MaxAddress(String cidr) {
        BigInteger maxBigInt = getIpv6MaxBigInteger(cidr);
        return bigIntegerToIpv6(maxBigInt);
    }

    /**
     * 生成指定数量的可用IPv6地址
     *
     * @param cidr IPv6 CIDR格式的IP地址，如"2001:db8::/64"
     * @param usedIps 已使用的IP列表，可为null
     * @param filterIps 需要过滤的IP列表，可为null
     * @param count 需要生成的IP数量
     * @return 可用的IPv6地址列表
     */
    public static List<String> generateAvailableIpv6s(String cidr, List<String> usedIps, List<String> filterIps, int count) {
        if (!isIPv6Cidr(cidr)) {
            throw new IllegalArgumentException("无效的IPv6 CIDR格式: " + cidr);
        }

        if (count <= 0) {
            return new ArrayList<>();
        }

        // 计算网络地址和最大地址
        String networkAddress = getIpv6NetworkAddress(cidr);
        String maxAddress = getIpv6MaxAddress(cidr);

        // 计算可用IP数量
        BigInteger networkBigInt = ipv6ToBigInteger(networkAddress);
        BigInteger maxBigInt = ipv6ToBigInteger(maxAddress);
        BigInteger availableIpCount = maxBigInt.subtract(networkBigInt);

        // IPv6网段通常很大，这里限制生成数量以避免性能问题
        if (availableIpCount.compareTo(BigInteger.valueOf(count * 10)) < 0) {
            System.out.println("警告: IPv6网段可用IP数量较少: " + availableIpCount);
        }

        // 转换已使用和需要过滤的IP为集合
        List<String> unavailableIps = new ArrayList<>();
        if (usedIps != null) {
            unavailableIps.addAll(usedIps);
        }
        if (filterIps != null) {
            unavailableIps.addAll(filterIps);
        }

        // 添加网络地址到不可用列表
        unavailableIps.add(networkAddress);

        // 生成可用IP列表
        List<String> availableIps = new ArrayList<>();
        BigInteger currentIp = networkBigInt.add(BigInteger.ONE);

        while (availableIps.size() < count && currentIp.compareTo(maxBigInt) <= 0) {
            String ipAddress = bigIntegerToIpv6(currentIp);
            if (!unavailableIps.contains(ipAddress)) {
                availableIps.add(ipAddress);
            }
            currentIp = currentIp.add(BigInteger.ONE);

            // 防止无限循环，限制最大尝试次数
            if (currentIp.subtract(networkBigInt).compareTo(BigInteger.valueOf(count * 100)) > 0) {
                break;
            }
        }

        return availableIps;
    }

    /**
     * 判断指定的IPv6地址是否在CIDR网段内
     *
     * @param ip 需要检查的IPv6地址，如"2001:db8::1"
     * @param cidr IPv6 CIDR格式的网段，如"2001:db8::/32"
     * @return 如果IP在网段内返回true，否则返回false
     * @throws IllegalArgumentException 如果IP格式或CIDR格式不正确
     */
    public static boolean isIpv6InCidr(String ip, String cidr) {
        if (!isIPv6(ip)) {
            throw new IllegalArgumentException("无效的IPv6地址格式: " + ip);
        }

        if (!isIPv6Cidr(cidr)) {
            throw new IllegalArgumentException("无效的IPv6 CIDR格式: " + cidr);
        }

        // 将IP地址转换为BigInteger
        BigInteger ipBigInt = ipv6ToBigInteger(ip);

        // 获取CIDR的网络地址和最大地址
        BigInteger networkBigInt = getIpv6NetworkBigInteger(cidr);
        BigInteger maxBigInt = getIpv6MaxBigInteger(cidr);

        // 判断IP是否在网段范围内
        return ipBigInt.compareTo(networkBigInt) >= 0 && ipBigInt.compareTo(maxBigInt) <= 0;
    }

    // ==================== 通用方法（同时支持IPv4和IPv6） ====================

    /**
     * 自动检测IP版本并判断是否在CIDR网段内
     *
     * @param ip 需要检查的IP地址（IPv4或IPv6）
     * @param cidr CIDR格式的网段（IPv4或IPv6）
     * @return 如果IP在网段内返回true，否则返回false
     * @throws IllegalArgumentException 如果IP格式或CIDR格式不正确，或IP版本与CIDR版本不匹配
     */
    public static boolean isIpInCidrAuto(String ip, String cidr) {
        if (isIPv4(ip) && isIPv4Cidr(cidr)) {
            return isIpInCidr(ip, cidr);
        } else if (isIPv6(ip) && isIPv6Cidr(cidr)) {
            return isIpv6InCidr(ip, cidr);
        } else {
            throw new IllegalArgumentException(
                String.format("IP版本与CIDR版本不匹配: IP=%s, CIDR=%s", ip, cidr));
        }
    }

    /**
     * 自动检测CIDR版本并生成可用IP地址
     *
     * @param cidr CIDR格式的IP地址（IPv4或IPv6）
     * @param usedIps 已使用的IP列表，可为null
     * @param filterIps 需要过滤的IP列表，可为null
     * @param count 需要生成的IP数量
     * @return 可用的IP地址列表
     * @throws IllegalArgumentException 如果CIDR格式不正确
     */
    public static List<String> generateAvailableIpsAuto(String cidr, List<String> usedIps, List<String> filterIps, int count) {
        if (isIPv4Cidr(cidr)) {
            return generateAvailableIps(cidr, usedIps, filterIps, count);
        } else if (isIPv6Cidr(cidr)) {
            return generateAvailableIpv6s(cidr, usedIps, filterIps, count);
        } else {
            throw new IllegalArgumentException("无效的CIDR格式: " + cidr);
        }
    }

    /**
     * 自动检测CIDR版本并获取网络地址
     *
     * @param cidr CIDR格式的IP地址（IPv4或IPv6）
     * @return 网络地址
     * @throws IllegalArgumentException 如果CIDR格式不正确
     */
    public static String getNetworkAddressAuto(String cidr) {
        if (isIPv4Cidr(cidr)) {
            return getNetworkAddress(cidr);
        } else if (isIPv6Cidr(cidr)) {
            return getIpv6NetworkAddress(cidr);
        } else {
            throw new IllegalArgumentException("无效的CIDR格式: " + cidr);
        }
    }

    /**
     * 检测IP地址版本
     *
     * @param ip IP地址字符串
     * @return 4表示IPv4，6表示IPv6，0表示无效格式
     */
    public static int getIpVersion(String ip) {
        if (isIPv4(ip)) {
            return 4;
        } else if (isIPv6(ip)) {
            return 6;
        } else {
            return 0;
        }
    }

    /**
     * 检测CIDR版本
     *
     * @param cidr CIDR字符串
     * @return 4表示IPv4，6表示IPv6，0表示无效格式
     */
    public static int getCidrVersion(String cidr) {
        if (isIPv4Cidr(cidr)) {
            return 4;
        } else if (isIPv6Cidr(cidr)) {
            return 6;
        } else {
            return 0;
        }
    }
}

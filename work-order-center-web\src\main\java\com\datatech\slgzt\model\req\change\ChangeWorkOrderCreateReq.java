package com.datatech.slgzt.model.req.change;

import com.datatech.slgzt.model.change.ChangeReqModel;
import com.datatech.slgzt.model.file.UploadFileModel;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年 03月31日 14:49:11
 */
@Data
public class ChangeWorkOrderCreateReq {

    // 工单id，重新提交时必传
    private String id;

    //工单标题
    private String orderTitle;

    //所属部门名称
    private String departmentName;

    /**
     * 厂家名称
     */
    private String manufacturer;

    /**
     * 厂家联系人
     */
    private String manufacturerContacts;

    /**
     * 厂家联系电话
     */
    private String manufacturerMobile;

    /**
     * 三级业务部门领导名称
     */
    private String levelThreeLeaderName;

    /**
     * 三级业务部门领导Id
     */
    private Long levelThreeLeaderId;

    /**
     * 二级业务部门领导ID
     */
    private Long businessDepartLeaderId;

    /**
     * 二级业务部门领导名称
     */
    private String businessDepartLeaderName;

    /**
     * 订单描述
     */
    private String orderDesc;

    /**
     * 业务系统id
     */
    private Long businessSystemId;

    /**
     * 业务系统名称
     */
    private String businessSystemName;

    /**
     * 业务系统所属模块id
     */
    private Long moduleId;

    /**
     * 业务系统所属模块名称
     */
    private String moduleName;

    /**
     * 申请人名称
     */
    private String createdUserName;

    /**
     * 局方负责人
     */
    private String bureauUserName;

    /**
     * 资源上云说明书上传文件信息
     */
    private List<UploadFileModel> resourceApplyFiles;


    //--------更变设备部分字段--------------------------
    private List<ChangeReqModel> ecsPropertyList;

    private List<ChangeReqModel> evsPropertyList;

    private List<ChangeReqModel> gcsPropertyList;

    private List<ChangeReqModel> mysqlPropertyList;

    private List<ChangeReqModel> redisPropertyList;

    private List<ChangeReqModel> obsPropertyList;

    private List<ChangeReqModel> natPropertyList;

    private List<ChangeReqModel> slbPropertyList;

    private List<ChangeReqModel> eipPropertyList;

    //--------变更时间部分字段--------------------------

    private List<ChangeReqModel> ecsTimeList;

    private List<ChangeReqModel> evsTimeList;

    private List<ChangeReqModel> mysqlTimeList;

    private List<ChangeReqModel> redisTimeList;

    private List<ChangeReqModel> gcsTimeList;

    private List<ChangeReqModel> obsTimeList;

    private List<ChangeReqModel> natTimeList;

    private List<ChangeReqModel> slbTimeList;

    private List<ChangeReqModel> eipTimeList;

}

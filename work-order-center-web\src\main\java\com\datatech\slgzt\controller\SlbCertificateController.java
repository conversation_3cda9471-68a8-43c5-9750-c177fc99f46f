package com.datatech.slgzt.controller;

import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.annotation.OperationLog;
import com.datatech.slgzt.convert.SlbCertificateWebConvert;
import com.datatech.slgzt.manager.SlbCertificateManager;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.dto.SlbCertificateDTO;
import com.datatech.slgzt.model.query.SlbCertificateQuery;
import com.datatech.slgzt.model.req.slb.SlbCertificateCreateReq;
import com.datatech.slgzt.model.req.slb.SlbCertificateIdReq;
import com.datatech.slgzt.model.req.slb.SlbCertificatePageReq;
import com.datatech.slgzt.model.req.slb.SlbCertificateUpdateReq;
import com.datatech.slgzt.model.vo.slb.SlbCertificateVO;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.utils.Precondition;
import com.datatech.slgzt.warpper.PageWarppers;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * SLB证书控制器
 */
@RestController
@RequestMapping("/slbCertificate")
public class SlbCertificateController {

    @Resource
    private SlbCertificateManager slbCertificateManager;
    
    @Resource
    private SlbCertificateWebConvert slbCertificateWebConvert;
    
    /**
     * 分页查询
     */
    @PostMapping("/page")
    public CommonResult<PageResult<SlbCertificateVO>> page(@RequestBody SlbCertificatePageReq req) {
        // 校验分页参数
        Precondition.checkArgument(req.getPageNum() != null && req.getPageNum() > 0, "页码必须大于0");
        Precondition.checkArgument(req.getPageSize() != null && req.getPageSize() > 0, "每页条数必须大于0");
        SlbCertificateQuery query = slbCertificateWebConvert.convert(req);
        PageResult<SlbCertificateDTO> page = slbCertificateManager.page(query);
        return CommonResult.success(PageWarppers.box(page, slbCertificateWebConvert::convert));
    }
    
    /**
     * 新增
     */
    @PostMapping("/create")
    @OperationLog(description = "新增SLB证书", operationType = "CREATE")
    public CommonResult<Void> create(@RequestBody SlbCertificateCreateReq req) {
        // 校验必填参数
        Precondition.checkArgument(StringUtils.isNotBlank(req.getCertificateName()), "证书名称不能为空");
        Precondition.checkArgument(StringUtils.isNotBlank(req.getPublicKeyContent()), "公钥内容不能为空");
        Precondition.checkArgument(StringUtils.isNotBlank(req.getPrivateKeyContent()), "私钥内容不能为空");
        Precondition.checkArgument(StringUtils.isNotBlank(req.getCertificateType()), "证书类型不能为空");
        Precondition.checkArgument(StringUtils.isNotBlank(req.getDomainCode()), "域名编码不能为空");
        Precondition.checkArgument(StringUtils.isNotBlank(req.getDomainName()), "域名名称不能为空");
        Precondition.checkArgument(StringUtils.isNotBlank(req.getRegionCode()), "资源池编码不能为空");
        Precondition.checkArgument(StringUtils.isNotBlank(req.getBusinessSystemId()), "业务系统ID不能为空");
        SlbCertificateDTO dto = slbCertificateWebConvert.convert(req);
        slbCertificateManager.create(dto);
        return CommonResult.success(null);
    }




    /**
     * 修改
     */
    @PostMapping("/update")
    @OperationLog(description = "修改SLB证书", operationType = "UPDATE")
    public CommonResult<Void> update(@RequestBody SlbCertificateUpdateReq req) {
        // 校验必填参数
        Precondition.checkArgument(StringUtils.isNotBlank(req.getId()), "证书ID不能为空");
        Precondition.checkArgument(StringUtils.isNotBlank(req.getCertificateName()), "证书名称不能为空");
        Precondition.checkArgument(StringUtils.isNotBlank(req.getPublicKeyContent()), "公钥内容不能为空");
        Precondition.checkArgument(StringUtils.isNotBlank(req.getPrivateKeyContent()), "私钥内容不能为空");
        Precondition.checkArgument(StringUtils.isNotBlank(req.getCertificateType()), "证书类型不能为空");
        Precondition.checkArgument(StringUtils.isNotBlank(req.getDomainCode()), "域名编码不能为空");
        Precondition.checkArgument(StringUtils.isNotBlank(req.getDomainName()), "域名名称不能为空");
        // 检查记录是否存在
        SlbCertificateDTO existDTO = slbCertificateManager.getById(req.getId());
        Precondition.checkArgument(existDTO != null, "证书不存在");
        SlbCertificateDTO dto = slbCertificateWebConvert.convert(req);
        slbCertificateManager.update(dto);
        return CommonResult.success(null);
    }
    
    /**
     * 删除
     */
    @PostMapping("/delete")
    @OperationLog(description = "删除SLB证书", operationType = "DELETE")
    public CommonResult<Void> delete(@RequestBody SlbCertificateIdReq req) {
        // 校验参数
        Precondition.checkArgument(StringUtils.isNotBlank(req.getId()), "证书ID不能为空");
        slbCertificateManager.delete(req.getId());
        return CommonResult.success(null);
    }
    
    /**
     * 详情
     */
    @PostMapping("/detail")
    @OperationLog(description = "查询SLB证书详情", operationType = "QUERY")
    public CommonResult<SlbCertificateVO> detail(@RequestBody SlbCertificateIdReq req) {
        // 校验参数
        Precondition.checkArgument(StringUtils.isNotBlank(req.getId()), "证书ID不能为空");
        
        SlbCertificateDTO dto = slbCertificateManager.getById(req.getId());
        Precondition.checkArgument(dto != null, "证书不存在");
        
        return CommonResult.success(slbCertificateWebConvert.convert(dto));
    }
} 
package com.datatech.slgzt.model.opm;

import com.datatech.slgzt.model.tem.*;
import lombok.Data;

import java.util.List;

/**
 * DAG模版创建服务参数
 * <AUTHOR>
 * @description TODO
 * @date 2025年 05月20日 15:43:44
 */
@Data
public class DagProductOpm {

    private String configId;

    //全局参数模型
    private TemGlobalParamModel temGlobalParamModel;

    //Ecs参数模型
    private List<DagEcsModel> dagEcsModelList;

    //gcs参数模型
    private List<DagGcsModel> dagGcsModelList;

    //evs参数模型
    private List<DagEvsModel> dagEvsModelList;

    //eip参数模型
    private List<DagEipModel> dagEipModelList;

    //slb参数模型
    private List<DagSlbModel> dagSlbModelList;

    //vpc参数模型
    private List<DagVpcModel> dagVpcModelList;

    //nat参数模型
    private List<DagNatModel> dagNatModelList;

    //obs参数模型
    private List<DagObsModel> dagObsModelList;

}

package com.datatech.slgzt.manager;

import com.datatech.slgzt.model.dto.VnicDTO;
import com.datatech.slgzt.model.query.VnicQuery;
import com.datatech.slgzt.utils.PageResult;

/**
 * 虚拟网卡Manager接口
 */
public interface VnicManager {
    
    /**
     * 保存虚拟网卡
     */
    void save(VnicDTO dto);

    /**
     * 更新虚拟网卡
     */
    void update(VnicDTO dto);

    /**
     * 更新绑定的云主机信息
     */
    void updateVmById(VnicDTO dto);
    
    /**
     * 删除虚拟网卡
     */
    void delete(String id);
    
    /**
     * 根据ID查询
     */
    VnicDTO getById(String id);

    /**
     * 根据主机id查询
     */
    VnicDTO getByVmId(String vmId);

    /**
     * 根据ID查询
     */
    VnicDTO getByVnicId(String vnicId);
    
    /**
     * 分页查询
     */
    PageResult<VnicDTO> page(VnicQuery query);
} 
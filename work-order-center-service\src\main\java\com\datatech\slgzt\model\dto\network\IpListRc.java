package com.datatech.slgzt.model.dto.network;

import com.datatech.slgzt.enums.ip.IpLevelEnum;
import com.datatech.slgzt.enums.ip.IpStatusEnum;
import lombok.Data;

import java.io.Serializable;

@Data
public class IpListRc implements Serializable {

    private String regionCode;

    private String networkPlane;

    private IpLevelEnum ipLevel;

    private String vpn;

    private String type;

    private String ipVersion;

    private String instanceId;

    private String prefix;

    private Integer mask;

    private Integer pageNum;

    private Integer pageSize;

    private String cloud;

    private String relatedPool;

    private IpStatusEnum status;
}

package com.datatech.slgzt.controller.container;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.datatech.slgzt.model.dto.ContainerQuotaDTO;
import com.datatech.slgzt.model.query.ContainerQuotaQuery;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.service.container.ContainerQuotaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 容器配额控制器
 * <AUTHOR>
 * @description 容器配额相关接口
 * @date 2025年05月27日
 */
@Slf4j
@RestController
@RequestMapping("/container/quota")
@Api(tags = "容器配额管理")
public class ContainerQuotaController {

    @Resource
    private ContainerQuotaService containerQuotaService;

    @GetMapping("/{id}")
    @ApiOperation("根据ID查询容器配额详情")
    public CommonResult<ContainerQuotaDTO> getById(
            @ApiParam(value = "容器配额ID", required = true) @PathVariable String id) {
        log.info("查询容器配额详情，ID：{}", id);
        ContainerQuotaDTO containerQuotaDTO = containerQuotaService.getById(id);
        return CommonResult.success(containerQuotaDTO);
    }

    @GetMapping("/workOrder/{workOrderId}")
    @ApiOperation("根据工单ID查询容器配额列表")
    public CommonResult<List<ContainerQuotaDTO>> getByWorkOrderId(
            @ApiParam(value = "工单ID", required = true) @PathVariable String workOrderId) {
        log.info("根据工单ID查询容器配额列表，工单ID：{}", workOrderId);
        List<ContainerQuotaDTO> containerQuotaList = containerQuotaService.getByWorkOrderId(workOrderId);
        return CommonResult.success(containerQuotaList);
    }

    @GetMapping("/subOrder/{subOrderId}")
    @ApiOperation("根据子订单ID查询容器配额")
    public CommonResult<ContainerQuotaDTO> getBySubOrderId(
            @ApiParam(value = "子订单ID", required = true) @PathVariable String subOrderId) {
        log.info("根据子订单ID查询容器配额，子订单ID：{}", subOrderId);
        ContainerQuotaDTO containerQuotaDTO = containerQuotaService.getBySubOrderId(subOrderId);
        return CommonResult.success(containerQuotaDTO);
    }

    @PostMapping("/page")
    @ApiOperation("分页查询容器配额")
    public CommonResult<IPage<ContainerQuotaDTO>> queryPage(@RequestBody ContainerQuotaQuery query) {
        log.info("分页查询容器配额，查询条件：{}", query);
        IPage<ContainerQuotaDTO> page = containerQuotaService.queryPage(query);
        return CommonResult.success(page);
    }

    @PostMapping("/list")
    @ApiOperation("查询容器配额列表")
    public CommonResult<List<ContainerQuotaDTO>> queryList(@RequestBody ContainerQuotaQuery query) {
        log.info("查询容器配额列表，查询条件：{}", query);
        List<ContainerQuotaDTO> containerQuotaList = containerQuotaService.queryList(query);
        return CommonResult.success(containerQuotaList);
    }

    @GetMapping("/search")
    @ApiOperation("搜索容器配额（支持名称和业务系统搜索）")
    public CommonResult<IPage<ContainerQuotaDTO>> search(
            @ApiParam("配额名称") @RequestParam(required = false) String cqName,
            @ApiParam("业务系统ID") @RequestParam(required = false) Long businessSystemId,
            @ApiParam("业务系统名称") @RequestParam(required = false) String businessSystemName,
            @ApiParam("工单ID") @RequestParam(required = false) String workOrderId,
            @ApiParam("状态") @RequestParam(required = false) String status,
            @ApiParam("云平台编码") @RequestParam(required = false) String domainCode,
            @ApiParam("资源池编码") @RequestParam(required = false) String regionCode,
            @ApiParam("4A账号") @RequestParam(required = false) String a4Account,
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("页大小") @RequestParam(defaultValue = "10") Integer pageSize) {

        log.info("搜索容器配额，配额名称：{}，业务系统ID：{}，业务系统名称：{}，工单ID：{}，状态：{}，云平台编码：{}，资源池编码：{}，4A账号：{}，页码：{}，页大小：{}",
                cqName, businessSystemId, businessSystemName, workOrderId, status, domainCode, regionCode, a4Account, pageNum, pageSize);

        ContainerQuotaQuery query = new ContainerQuotaQuery();
        query.setCqName(cqName);
        query.setBusinessSystemId(businessSystemId);
        query.setBusinessSystemName(businessSystemName);
        query.setWorkOrderId(workOrderId);
        query.setStatus(status);
        query.setDomainCode(domainCode);
        query.setRegionCode(regionCode);
        query.setA4Account(a4Account);
        query.setPageNum(pageNum);
        query.setPageSize(pageSize);

        IPage<ContainerQuotaDTO> page = containerQuotaService.queryPage(query);
        return CommonResult.success(page);
    }
}

package com.datatech.slgzt.controller.container;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.datatech.slgzt.model.CommonResult;
import com.datatech.slgzt.model.dto.ContainerQuotaDTO;
import com.datatech.slgzt.model.query.ContainerQuotaQuery;
import com.datatech.slgzt.model.req.container.ContainerQuotaPageReq;
import com.datatech.slgzt.model.vo.container.ContainerQuotaVO;
import com.datatech.slgzt.service.container.ContainerQuotaService;
import com.datatech.slgzt.utils.BeanCopyUtils;
import com.datatech.slgzt.utils.PageResult;
import com.datatech.slgzt.warpper.PageWarppers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 容器配额控制器
 * <AUTHOR>
 * @description 容器配额相关接口
 * @date 2025年05月27日
 */
@Slf4j
@RestController
@RequestMapping("/container/quota")
public class ContainerQuotaController {

    @Resource
    private ContainerQuotaService containerQuotaService;

    /**
     * 容器配额分页查询
     */
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public CommonResult<PageResult<ContainerQuotaVO>> page(@RequestBody ContainerQuotaPageReq req) {
        log.info("分页查询容器配额，查询条件：{}", req);

        // 转换查询条件
        ContainerQuotaQuery query = BeanCopyUtils.copyBean(req, ContainerQuotaQuery.class);

        // 执行分页查询
        PageResult<ContainerQuotaDTO> page = containerQuotaService.queryPage(query);

        // 转换为VO分页结果
        PageResult<ContainerQuotaVO> result = PageWarppers.box(page, dto -> BeanCopyUtils.copyBean(dto, ContainerQuotaVO.class));

        log.info("分页查询容器配额完成，总记录数：{}，当前页记录数：{}",
                result.getTotal(), result.getRecords().size());

        return CommonResult.success(result);
    }
}

package com.datatech.slgzt.manager;

import com.datatech.slgzt.model.dto.SlbCertificateDTO;
import com.datatech.slgzt.model.query.SlbCertificateQuery;
import com.datatech.slgzt.utils.PageResult;

/**
 * SLB证书Manager接口
 */
public interface SlbCertificateManager {
    
    /**
     * 新增
     */
    void create(SlbCertificateDTO dto);
    
    /**
     * 修改
     */
    void update(SlbCertificateDTO dto);
    
    /**
     * 删除
     */
    void delete(String id);
    
    /**
     * 根据ID查询
     */
    SlbCertificateDTO getById(String id);
    
    /**
     * 分页查询
     */
    PageResult<SlbCertificateDTO> page(SlbCertificateQuery query);
} 
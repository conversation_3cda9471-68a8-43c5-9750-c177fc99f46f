package com.datatech.slgzt.utils;

import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.Supplier;

/**
 * Bean拷贝工具类
 * <AUTHOR>
 * @description Bean对象拷贝相关工具方法
 * @date 2025年05月27日
 */
public class BeanCopyUtils {

    /**
     * 拷贝单个对象
     * @param source 源对象
     * @param targetClass 目标类型
     * @param <T> 目标类型泛型
     * @return 目标对象
     */
    public static <T> T copyBean(Object source, Class<T> targetClass) {
        if (source == null) {
            return null;
        }
        try {
            T target = targetClass.newInstance();
            BeanUtils.copyProperties(source, target);
            return target;
        } catch (Exception e) {
            throw new RuntimeException("Bean拷贝失败", e);
        }
    }

    /**
     * 拷贝单个对象（使用Supplier创建目标对象）
     * @param source 源对象
     * @param targetSupplier 目标对象供应商
     * @param <T> 目标类型泛型
     * @return 目标对象
     */
    public static <T> T copyBean(Object source, Supplier<T> targetSupplier) {
        if (source == null) {
            return null;
        }
        T target = targetSupplier.get();
        BeanUtils.copyProperties(source, target);
        return target;
    }

    /**
     * 拷贝对象列表
     * @param sourceList 源对象列表
     * @param targetClass 目标类型
     * @param <T> 目标类型泛型
     * @return 目标对象列表
     */
    public static <T> List<T> copyBeanList(List<?> sourceList, Class<T> targetClass) {
        if (sourceList == null || sourceList.isEmpty()) {
            return Collections.emptyList();
        }
        List<T> targetList = new ArrayList<>(sourceList.size());
        for (Object source : sourceList) {
            T target = copyBean(source, targetClass);
            if (target != null) {
                targetList.add(target);
            }
        }
        return targetList;
    }

    /**
     * 拷贝对象列表（使用Supplier创建目标对象）
     * @param sourceList 源对象列表
     * @param targetSupplier 目标对象供应商
     * @param <T> 目标类型泛型
     * @return 目标对象列表
     */
    public static <T> List<T> copyBeanList(List<?> sourceList, Supplier<T> targetSupplier) {
        if (sourceList == null || sourceList.isEmpty()) {
            return Collections.emptyList();
        }
        List<T> targetList = new ArrayList<>(sourceList.size());
        for (Object source : sourceList) {
            T target = copyBean(source, targetSupplier);
            if (target != null) {
                targetList.add(target);
            }
        }
        return targetList;
    }
}

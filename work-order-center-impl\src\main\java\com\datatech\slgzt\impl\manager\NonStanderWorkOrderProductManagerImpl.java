package com.datatech.slgzt.impl.manager;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.datatech.slgzt.convert.NonStanderWorkOrderProductManagerConvert;
import com.datatech.slgzt.dao.NonStanderWorkOrderProductDAO;
import com.datatech.slgzt.dao.model.order.NonStanderWorkOrderProductDO;
import com.datatech.slgzt.enums.ProductTypeEnum;
import com.datatech.slgzt.manager.NonStanderWorkOrderProductManager;
import com.datatech.slgzt.model.dto.NonStanderWorkOrderProductDTO;
import com.datatech.slgzt.model.nostander.*;
import com.datatech.slgzt.model.query.NonStanderWorkOrderProductQuery;
import com.datatech.slgzt.model.resourcce.*;
import com.datatech.slgzt.utils.StreamUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月13日 17:02:23
 */
@Service
public class NonStanderWorkOrderProductManagerImpl implements NonStanderWorkOrderProductManager {


    @Resource
    private NonStanderWorkOrderProductDAO dao;

    @Resource
    private NonStanderWorkOrderProductManagerConvert convert;

    @Override
    public List<NonStanderWorkOrderProductDTO> list(NonStanderWorkOrderProductQuery query) {
        return StreamUtils.mapArray(dao.list(query), convert::do2dto);
    }

    /**
     * update
     *
     * @param dto
     */
    @Override
    public void update(NonStanderWorkOrderProductDTO dto) {
        dao.updateById(convert.dto2do(dto));

    }

    @Override
    public NonStanderWorkOrderProductDTO getById(Long id) {
        return convert.do2dto(dao.getById(id));
    }


    @Override
    public NonStanderWorkOrderProductDTO getByGid(String gid) {
        return convert.do2dto(dao.getByGid(gid));
    }

    @Override
    public NonStanderWorkOrderProductDTO getBySubOrderId(Long subOrderId) {
        return convert.do2dto(dao.getBySubOrderId(subOrderId));
    }

    @Override
    public void deleteByWorkOrderId(String id) {
        dao.delByWorkOrderId(id);
    }

    /**
     * insert
     *
     * @param dto
     */
    @Override
    public void insert(NonStanderWorkOrderProductDTO dto) {
        dao.insert(convert.dto2do(dto));
    }

    @Override
    public void updateStatusById(Long id, String status) {
        NonStanderWorkOrderProductDO standardWorkOrderProductDO = new NonStanderWorkOrderProductDO();
        standardWorkOrderProductDO.setId(id);
        standardWorkOrderProductDO.setOpenStatus(status);
        dao.updateById(standardWorkOrderProductDO);
    }


    @Override
    public void updateStatusByParentId(Long id, String status) {
        NonStanderWorkOrderProductDO standardWorkOrderProductDO = new NonStanderWorkOrderProductDO();
        standardWorkOrderProductDO.setParentProductId(id);
        standardWorkOrderProductDO.setOpenStatus(status);
        dao.updateByParentId(standardWorkOrderProductDO);
    }


    @Override
    public ResourceShowInfoDTO selectResourceOverview(NonStanderWorkOrderProductQuery productQuery) {
        List<NonStanderWorkOrderProductDO> productDOS = dao.getByWorkOrderId(productQuery.getOrderId());
        // 通过parentProductId是否为0进行分组开通的产品类型
        Map<Long, NonStanderWorkOrderProductDO> parentProductMap = productDOS.stream().filter(product -> product.getParentProductId() == 0)
                .collect(Collectors.toMap(NonStanderWorkOrderProductDO::getId, productDO -> productDO));

        ResourceShowInfoDTO infoDTO = new ResourceShowInfoDTO();
        ExtendEcsResource ecsResource = new ExtendEcsResource();
        ExtendEcsResource gcsResource = new ExtendEcsResource();
        parentProductMap.forEach((productId, product) -> {
            if (ProductTypeEnum.ECS.getCode().equalsIgnoreCase(product.getProductType())) {
                convertEcs(product, ecsResource);
                infoDTO.setEcs(ecsResource);
            }
            if (ProductTypeEnum.GCS.getCode().equalsIgnoreCase(product.getProductType())) {
                convertEcs(product, gcsResource);
                infoDTO.setGcs(gcsResource);
            }
        });

        assembleResource(infoDTO);
        return infoDTO;
    }

    /**
     * 组装单位
     */
    private void assembleResource(ResourceShowInfoDTO infoDTO) {
        ExtendEcsResource ecs = infoDTO.getEcs();
        if (ecs != null) {
            ecs.setVcpuNumbers(ecs.getCpuNumbersTmp() + "核");
            ecs.setRamNumbers(ecs.getRamNumbersTmp() + "G");
            ecs.setStorageNumbers(ecs.getStorageNumbersTmp() + "G");
            ecs.setBandWidthNumbers(ecs.getBandWidthNumbersTmp() + "M");
        }
        ExtendEcsResource gcs = infoDTO.getGcs();
        if (gcs != null) {
            gcs.setVcpuNumbers(gcs.getCpuNumbersTmp() == null ? "0" : gcs.getCpuNumbersTmp() + "核");
            gcs.setRamNumbers(gcs.getRamNumbersTmp() + "G");
            gcs.setStorageNumbers(gcs.getStorageNumbersTmp() + "G");
            gcs.setBandWidthNumbers(gcs.getBandWidthNumbersTmp() + "M");
            gcs.setVgpuNumbers(gcs.getGpuNumbersTmp() + "T4");
        }
        ExtendEvsResource evs = infoDTO.getEvs();
        if (evs != null) {
            evs.setStorageNumbers(evs.getStorageNumbersTmp() + "G");
        }
        ExtendSlbResource slb = infoDTO.getSlb();
        if (slb != null) {
            slb.setBandWidthNumbers(slb.getBandWidthNumbersTmp() + "M");
        }
        ExtendObsResource obs = infoDTO.getObs();
        if (obs != null) {
            obs.setStorageNumbers(obs.getStorageNumbersTmp() + "G");
        }
        ExtendNatResource nat = infoDTO.getNat();
        if (nat != null) {
            nat.setBandWidthNumbers(nat.getBandWidthNumbersTmp() + "M");
        }
    }

    /**
     * ecs资源概览转换
     */
    private ExtendEcsResource convertEcs(NonStanderWorkOrderProductDO productDO, ExtendEcsResource ecsResource) {
        String propertySnapshot = productDO.getPropertySnapshot();
        EcsModel escModel = JSON.parseObject(propertySnapshot, EcsModel.class);
        ecsResource.setResourceNumbers(ecsResource.getResourceNumbers() + 1);
        // 计算cpu大小
        String flavorName = escModel.getFlavorName();
        // 4C16GB/1T4
        if (StringUtils.isNotEmpty(flavorName)) {
            Integer cpuNumber = Integer.parseInt(StringUtils.substring(flavorName,
                    0, StringUtils.indexOf(flavorName, "C")));
            ecsResource.setCpuNumbersTmp(ecsResource.getCpuNumbersTmp() + cpuNumber);

            //获取内存
            Integer ramNumber = Integer.parseInt(StringUtils.substringBetween(flavorName, "C", "G"));
            ecsResource.setRamNumbersTmp(ecsResource.getRamNumbersTmp() + ramNumber);
            if (flavorName.contains("T")) {
                Integer gpuNumber = Integer.parseInt(StringUtils.substringBetween(flavorName, "/", "T"));
                ecsResource.setGpuNumbersTmp(ecsResource.getGpuNumbersTmp() + gpuNumber);
            }
        }

        //存储大小的算法是默认的系统盘大小+数据盘大小
        Integer storageNumbersTmp = ecsResource.getStorageNumbersTmp();//默认的系统盘大小
        //先判断是否存在挂载
        if (escModel.getMountDataDisk()) {
            //再计算数据盘大小
            List<MountDataDiskModel> mountDataDiskModelList = escModel.getMountDataDiskList();
            if (CollectionUtil.isNotEmpty(mountDataDiskModelList)) {
                for (MountDataDiskModel mountDataDiskModel : mountDataDiskModelList) {
                    storageNumbersTmp += Optional.ofNullable(mountDataDiskModel.getSysDiskSize()).orElse(0);
                }
            }
        }
        ecsResource.setStorageNumbersTmp(storageNumbersTmp+ escModel.getSysDiskSize());
        List<EipModel> eipModelList = escModel.getEipModelList();
        if (CollectionUtil.isNotEmpty(eipModelList)) {
            EipModel eipModel = eipModelList.get(0);
            Integer width = eipModel.getBandwidth();
            ecsResource.setBandWidthNumbersTmp(ecsResource.getBandWidthNumbersTmp() + width);
        }

        return ecsResource;
    }



    /**
     * ecs资源概览转换
     */
    private ExtendEcsResource convertGcs(NonStanderWorkOrderProductDO productDO, ExtendEcsResource ecsResource) {
        String propertySnapshot = productDO.getPropertySnapshot();
        EcsModel escModel = JSON.parseObject(propertySnapshot, EcsModel.class);
        ecsResource.setResourceNumbers(ecsResource.getResourceNumbers() + 1);
        // 计算cpu大小
        String flavorName = escModel.getFlavorName();
        Integer cpuNumber = Integer.parseInt(StringUtils.substring(flavorName,
                0, StringUtils.indexOf(flavorName, "C")));
        ecsResource.setCpuNumbersTmp(ecsResource.getCpuNumbersTmp() + cpuNumber);

        //获取内存
        Integer ramNumber = Integer.parseInt(StringUtils.substringBetween(flavorName, "C", "G"));
        ecsResource.setRamNumbersTmp(ecsResource.getRamNumbersTmp() + ramNumber);
        //存储大小的算法是默认的系统盘大小+数据盘大小
        Integer storageNumbersTmp = ecsResource.getStorageNumbersTmp();//默认的系统盘大小
        //先判断是否存在挂载
        if (escModel.getMountDataDisk()) {
            //再计算数据盘大小
            List<MountDataDiskModel> mountDataDiskModelList = escModel.getMountDataDiskList();
            if (CollectionUtil.isNotEmpty(mountDataDiskModelList)) {
                for (MountDataDiskModel mountDataDiskModel : mountDataDiskModelList) {
                    storageNumbersTmp += Optional.ofNullable(mountDataDiskModel.getSysDiskSize()).orElse(0);
                }
            }
        }
        ecsResource.setStorageNumbersTmp(storageNumbersTmp+ escModel.getSysDiskSize());
        List<EipModel> eipModelList = escModel.getEipModelList();
        if (CollectionUtil.isNotEmpty(eipModelList)) {
            EipModel eipModel = eipModelList.get(0);
            Integer width = eipModel.getBandwidth();
            ecsResource.setBandWidthNumbersTmp(ecsResource.getBandWidthNumbersTmp() + width);
        }

        return ecsResource;
    }
}

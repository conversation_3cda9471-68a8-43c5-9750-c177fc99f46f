package com.datatech.slgzt.model.opm;

import com.datatech.slgzt.model.change.ChangeReqModel;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025年 03月31日 15:18:05
 */
@Data
public class ChangeWorkOrderCreateOpm {
    // 工单id，重新提交时必传
    private String id;

    //工单标题
    private String orderTitle;

    //所属部门名称
    private String departmentName;

    /**
     * 厂家名称
     */
    private String manufacturer;

    /**
     * 厂家联系人
     */
    private String manufacturerContacts;

    /**
     * 厂家联系电话
     */
    private String manufacturerMobile;

    /**
     * 三级业务部门领导名称
     */
    private String levelThreeLeaderName;

    /**
     * 三级业务部门领导Id
     */
    private Long levelThreeLeaderId;

    /**
     * 订单描述
     */
    private String orderDesc;

    /**
     * 业务系统id
     */
    private Long businessSystemId;

    /**
     * 业务系统名称
     */
    private String businessSystemName;

    private Long tenantId;

    private String tenantName;

    private String businessSystemCode;

    private String billId;

    private String customNo;

    /**
     * 二级业务部门领导ID
     */
    private Long businessDepartLeaderId;

    /**
     * 二级业务部门领导名称
     */
    private String businessDepartLeaderName;

    /**
     * 业务系统所属模块id
     */
    private Long moduleId;

    /**
     * 业务系统所属模块名称
     */
    private String moduleName;

    /**
     * 申请人名称
     */
    private String createdUserName;

    /**
     * 局方负责人
     */
    private String bureauUserName;

    /**
     * 资源上云说明书上传文件信息
     */
    private String resourceApplyFiles;

    //草稿判断
    private Boolean canDraft = false;

    //--------更变设备部分字段--------------------------
    private List<ChangeReqModel> ecsPropertyList;

    private List<ChangeReqModel> evsPropertyList;

    private List<ChangeReqModel> gcsPropertyList;

    private List<ChangeReqModel> mysqlPropertyList;

    private List<ChangeReqModel> redisPropertyList;

    private List<ChangeReqModel> obsPropertyList;

    private List<ChangeReqModel> natPropertyList;

    private List<ChangeReqModel> slbPropertyList;

    private List<ChangeReqModel> eipPropertyList;

    private Integer variablesValue;

}
